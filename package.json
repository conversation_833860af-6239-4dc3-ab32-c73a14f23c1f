{"name": "cinesend-on-demand-client", "version": "0.1.0", "private": true, "devDependencies": {"autoprefixer": "^10.4.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1"}, "dependencies": {"@babel/helper-call-delegate": "^7.8.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.11.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@bitcine/cinesend-player": "^2.1.3", "@bitcine/cinesend-theme": "^0.3.73", "@bitcine/piracy-detector": "^0.1.18", "@emotion/core": "^10.0.27", "@emotion/styled": "^10.0.27", "@sentry/browser": "^5.15.5", "@stripe/react-stripe-js": "^1.4.1", "@stripe/stripe-js": "^1.17.1", "@vitejs/plugin-react": "^4.2.1", "@zoomus/websdk": "^1.7.6", "babel-preset-react-app": "^9.1.2", "basscss": "^8.1.0", "connected-react-router": "^6.6.1", "dayjs": "^1.8.27", "dayjs-plugin-utc": "^0.1.2", "detect-browser": "^5.0.0", "dompurify": "^3.1.2", "html-react-parser": "^5.0.11", "i18next": "^23.7.16", "rc-slider": "^9.2.4", "rc-virtual-list": "^3.11.4", "react": "^16.12.0", "react-autosize-textarea": "^7.0.0", "react-collapse": "^5.1.1", "react-dom": "^16.12.0", "react-dropzone": "^10.2.1", "react-favicon": "^2.0.3", "react-ga": "^3.0.0", "react-ga4": "^1.4.1", "react-helmet": "^6.1.0", "react-hot-loader": "^4.12.19", "react-i18next": "^11.4.0", "react-icons": "^4.3.1", "react-multi-carousel": "^2.5.4", "react-paginate": "^6.3.2", "react-redux": "^7.1.3", "react-responsive": "^8.0.3", "react-responsive-carousel": "^3.1.51", "react-router-dom": "^6.26.2", "react-router-hash-link": "^2.2.2", "react-select": "^3.0.8", "react-slick": "^0.25.2", "react-toastify": "^5.5.0", "react-toggle": "^4.1.2", "react-tooltip": "^4.2.21", "react-transition-group": "^4.3.0", "redux": "^4.0.5", "redux-promise-middleware": "^6.1.2", "redux-thunk": "^2.3.0", "slick-carousel": "^1.8.1", "stream-chat": "^3.11.0", "stream-chat-react": "^6.1.1", "vanilla-cookieconsent": "3.0.1", "vite": "^5.0.11", "vite-jsconfig-paths": "^2.0.1"}, "scripts": {"dev": "vite", "add-custom-headers": "node scripts/add_custom_headers.js", "start": "vite", "build": "vite build", "build-production": "vite build", "watch:css": "tailwind build -i ./src/styles/index.css -o ./src/styles/tailwind.css --watch"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}