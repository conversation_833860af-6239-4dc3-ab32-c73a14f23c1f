import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import viteJsConfigPaths from 'vite-jsconfig-paths'

export default defineConfig(({ command, mode }) => {
  return {
    // depending on your application, base can also be "/"
    base: '/',
    plugins: [react(), viteJsConfigPaths()],
    server: {
      // this ensures that the browser opens upon server start
      open: true,
      // this sets a default port to 3000
      port: 8013,
      // As of vite v6.0.11, this needs to be set when making use of proxies
      allowedHosts: true
    },
    define: {
      // needed for some 3rd party libs that need global nodejs shims
      global: {},
      'process.env': loadEnv(mode, process.cwd(), '')
    },
  }
})

