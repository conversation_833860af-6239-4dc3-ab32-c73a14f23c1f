import React, { useEffect } from 'react'
//import * as Sen<PERSON> from '@sentry/browser';
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { ToastContainer, Slide, toast } from 'react-toastify'
import { Helmet } from 'react-helmet'
import Favicon from "react-favicon"
import styled, { createGlobalStyle } from 'styled-components'
import Status from 'components/Status'
import { authCheck } from '/src/api/auth'
import { getOrganization } from '/src/api/organization'
import Routes from '/src/routes'
import '/src/styles/index.css'
import '/node_modules/react-toastify/dist/ReactToastify.css'
import InjectedCheckoutForm from '/src/components/Stripe/elements_wrapper'
import useIsMobile from 'hooks/use_is_mobile'
import StateSetter from 'components/StateSetter'
import useCookies from 'hooks/use_cookies'
import { LanguageProvider } from './contexts/language_context'

// Sentry.init({
//   dsn: "https://<EMAIL>/5216550",
//   ignoreErrors: [
//     'Non-Error exception captured'
//   ]
// })

const fallback = `-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue'`

const HeaderFontStyle = createGlobalStyle`
  @font-face {
    font-family: 'CustomHeaderFont';
    src: url('${props => props.url}');
  }
  h1,h2,h3,h4,h5 {
    font-family: 'CustomHeaderFont', ${fallback};
  }`

const BodyFontStyle = createGlobalStyle`
  @font-face {
    font-family: 'CustomBodyFont';
    src: url('${props => props.url}');
  }
  div,span,button,input {
    font-family: 'CustomBodyFont', ${fallback};
  }`

const PageContainer = styled.div`
  position: relative;
  min-height: 100vh;
  width: 100%;
  .spinner {
    border-right-color: ${props => props.accentColor} !important;
  }
  .material-icons {
    // color: ${props => props.useLightTheme ? 'black' : 'white'} !important;
  }
  .h1,h2,h3,h4,h5,p {
    // color: ${props => props.useLightTheme ? 'black' : 'white'};
  }
`

const App = ({ language, getOrganization, organization, authStatus, authCheck, search }) => {
  const { isMobile } = useIsMobile()
  useCookies(organization, language)
  useEffect(() => {
    console.log(`%c*************************`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
    console.log(`%c*  Powered by CineSend  *`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
    console.log(`%c*    Built in Canada    *`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
    console.log(`%c*************************`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
  }, [])

  let urlParams = new URLSearchParams(location.search)
  if (urlParams.has("token")) {
    localStorage.setItem('token', urlParams.get("token"))
  }
  if (urlParams.has("previewToken")) {
    sessionStorage.setItem('previewToken', urlParams.get("previewToken"))
  }
  useEffect(() => {
    if (!organization) {
      return
    }
    if (!organization.use_light_theme) {
      document.documentElement.classList.add('dark')
    }
    else {
      document.documentElement.classList.remove('dark')
    }
  }, [organization])
  useEffect(() => {
    getOrganization()
  }, [getOrganization, language])
  useEffect(() => {
    authCheck()
  }, [authCheck])
  useEffect(() => {
    const body = document.getElementsByTagName('body')[0]
    body.style.background = organization.background_color
    body.style.color = organization.text_color
  }, [organization])

  const swappers = [
    { from: '?tagIDs=65e273ca120e135a2608cbde&language=en_US', to: '?tagIDs=international&language=en_US' },
    { from: '?tagIDs=65e273ca120e135a2608cbde&language=ja', to: '?tagIDs=international&language=ja' },
    { from: '?tagIDs=65e273d213bb060c820763f0&language=en_US', to: '?tagIDs=domestic&language=en_US' },
    { from: '?tagIDs=65e273d213bb060c820763f0&language=ja', to: '?tagIDs=domestic&language=ja' }
  ]

  swappers.forEach(swap => {
    if (window.location.search === swap.from) {
      window.location.search = swap.to
    }
  })

  return (
    <LanguageProvider>
      <Helmet>
        <meta charSet="utf-8"/>
        <title>{organization.name}</title>
        {!!organization.custom_css_enabled && (
          <style>{organization.custom_css_min}</style>
        )}
        {!!organization.custom_css_enabled && (
          <style>{organization.translatable_custom_css}</style>
        )}
        {organization.userway_widget_enabled &&
        <script data-account="jJQstuVTuh" src="https://cdn.userway.org/widget.js"></script>}
        {organization.custom_script_urls.map((url, index) => {
          if (url.includes('<script')) {
            const firstChild = (new DOMParser().parseFromString(url, 'text/xml')).firstChild
            return <script src={firstChild.getAttribute('src')} id={firstChild.getAttribute('id')} key={index}/>
          }
          return <script src={url} key={index}/>
        })}
      </Helmet>
      {organization.favicon_url ? <Favicon url={organization.favicon_url}/> : null}
      <PageContainer
        accentColor={organization.accent_color}
        useLightTheme={organization.use_light_theme}
        className={`
          ${isMobile ? 'csod-mobile' : ''}
          csod-page-container ${search.showResultsPage ? 'csod-page-container-with-search-results' : ''}
          csod-url-path${window.location.pathname.replaceAll('/', '-')}
        `}>
        <Status
          className='h-screen'
          error={organization.status === 'FAILED'}
          pending={organization.status === 'PENDING' || authStatus === 'PENDING'}>
          {organization.header_font_url && <HeaderFontStyle url={organization.header_font_url}/>}
          {organization.body_font_url && <BodyFontStyle url={organization.body_font_url}/>}
          {organization.status === 'READY' && <Routes/>}
          <ToastContainer transition={Slide} position={toast.POSITION.BOTTOM_RIGHT}/>
        </Status>
      </PageContainer>
      <StateSetter/>
      <InjectedCheckoutForm/>
    </LanguageProvider>
  )
}

const mapStateToProps = state => ({
  user: state.auth.user,
  authStatus: state.auth.status,
  organization: state.organization,
  playerLoaded: !!state.session.endpoints,
  search: state.search,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  authCheck: bindActionCreators(authCheck, dispatch),
  getOrganization: bindActionCreators(getOrganization, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(App)
