import i18next from "i18next"
import React, { createContext, useContext, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { setLanguageCode } from "/src/api/auth"
import * as CookieConsent from 'vanilla-cookieconsent'
import OVERRIDE_MAP from "./override_map"
import { useLocation } from "react-router-dom"

const LANGUAGE_COOKIE = "csod-active-language-code"
const ENGLISH_CODE= "en_US"

const LanguageContext = createContext()

export const LanguageProvider = ({ children } ) => {
  const organization = useSelector(state => state.organization)
  const user = useSelector(state => state.user)
  const dispatch = useDispatch()
  const { search } = useLocation()

  const getDefaultLanguage = () => {
    if (user && user.active_language_code) {
      return user.active_language_code
    }
    let queryLanguage = (new URLSearchParams(search)).get("lang")
    if (!queryLanguage) {
      queryLanguage = (new URLSearchParams(search)).get("language")
    }
    if (queryLanguage) {
      return queryLanguage
    }
    let storageLanguage = localStorage.getItem(LANGUAGE_COOKIE)
    if (storageLanguage) {
      return storageLanguage
    }
    const lang = navigator.language || navigator.userLanguage
    const browserLang = lang.replace('-', '_')
    const formattedBrowserLang = OVERRIDE_MAP[browserLang] ? OVERRIDE_MAP[browserLang] : browserLang
    if (Array.isArray(organization.languages) && organization.languages.includes(formattedBrowserLang)) {
      return formattedBrowserLang
    }
    if (Array.isArray(organization.languages) && organization.languages.length > 0) {
      return organization.languages[0]
    }
    return ENGLISH_CODE
  }

  const [activeLanguage, setActiveLanguage] = useState(null)

  useEffect(() => {
    if (organization.status === "READY" && !activeLanguage) {
      const fetchedLanguage = getDefaultLanguage()
      const defaultLanguage = OVERRIDE_MAP[fetchedLanguage] ? OVERRIDE_MAP[fetchedLanguage] : fetchedLanguage
      setActiveLanguage(defaultLanguage)
    }
  }, [organization])

  useEffect(() => {
    if (activeLanguage) {
      localStorage.setItem(LANGUAGE_COOKIE, activeLanguage)
      i18next.changeLanguage(activeLanguage)
      dispatch(setLanguageCode(activeLanguage))
      CookieConsent.setLanguage(activeLanguage, true)
      CookieConsent.reset(false)
    }
  }, [activeLanguage])

  return (
    <LanguageContext.Provider value={{ activeLanguage, setActiveLanguage }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => {
  return useContext(LanguageContext)
}