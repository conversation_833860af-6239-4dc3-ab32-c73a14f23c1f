*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.12 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #EDEDFB;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  /* 3 */
  tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #A5A5B9;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

.\!container {
  width: 100% !important;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}

.pointer-events-none {
  pointer-events: none;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.\!relative {
  position: relative !important;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-4 {
  bottom: 1rem;
}

.end-1 {
  inset-inline-end: 0.25rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-1\/3 {
  left: 33.333333%;
}

.left-3 {
  left: 0.75rem;
}

.left-4 {
  left: 1rem;
}

.right-0 {
  right: 0px;
}

.right-2 {
  right: 0.5rem;
}

.right-2\/3 {
  right: 66.666667%;
}

.right-4 {
  right: 1rem;
}

.start-1 {
  inset-inline-start: 0.25rem;
}

.top-0 {
  top: 0px;
}

.top-2 {
  top: 0.5rem;
}

.top-4 {
  top: 1rem;
}

.-z-10 {
  z-index: -10;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-50 {
  z-index: 50;
}

.z-\[9\] {
  z-index: 9;
}

.float-right {
  float: right;
}

.m-2 {
  margin: 0.5rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-16 {
  margin-left: 4rem;
  margin-right: 4rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-40 {
  margin-top: 10rem;
  margin-bottom: 10rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-60 {
  margin-left: 15rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.box-border {
  box-sizing: border-box;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

.contents {
  display: contents;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.aspect-\[2\/3\] {
  aspect-ratio: 2/3;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-20 {
  height: 5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-60 {
  height: 15rem;
}

.h-8 {
  height: 2rem;
}

.h-\[41px\] {
  height: 41px;
}

.h-\[44px\] {
  height: 44px;
}

.h-\[73px\] {
  height: 73px;
}

.h-fit {
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[600px\] {
  max-height: 600px;
}

.max-h-screen {
  max-height: 100vh;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-96 {
  min-height: 24rem;
}

.min-h-\[120px\] {
  min-height: 120px;
}

.min-h-\[128px\] {
  min-height: 128px;
}

.min-h-\[350px\] {
  min-height: 350px;
}

.min-h-\[72px\] {
  min-height: 72px;
}

.min-h-\[88px\] {
  min-height: 88px;
}

.min-h-\[96px\] {
  min-height: 96px;
}

.min-h-fit {
  min-height: fit-content;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/6 {
  width: 16.666667%;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: 0.75rem;
}

.w-4 {
  width: 1rem;
}

.w-4\/5 {
  width: 80%;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-6 {
  width: 1.5rem;
}

.w-60 {
  width: 15rem;
}

.w-64 {
  width: 16rem;
}

.w-8 {
  width: 2rem;
}

.w-\[204px\] {
  width: 204px;
}

.w-\[232px\] {
  width: 232px;
}

.w-\[320px\] {
  width: 320px;
}

.w-\[32px\] {
  width: 32px;
}

.w-\[488px\] {
  width: 488px;
}

.w-\[95\%\] {
  width: 95%;
}

.w-full {
  width: 100%;
}

.w-screen {
  width: 100vw;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-\[15rem\] {
  min-width: 15rem;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[220px\] {
  min-width: 220px;
}

.min-w-\[550px\] {
  min-width: 550px;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-\[120px\] {
  max-width: 120px;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.flex-grow {
  flex-grow: 1;
}

.grow {
  flex-grow: 1;
}

.origin-top {
  transform-origin: top;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-40 {
  --tw-translate-y: -10rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-16 {
  --tw-translate-y: 4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.cursor-auto {
  cursor: auto;
}

.cursor-grab {
  cursor: grab;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
          user-select: none;
}

.resize-none {
  resize: none;
}

.\!resize {
  resize: both !important;
}

.resize {
  resize: both;
}

.snap-x {
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}

.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

.snap-always {
  scroll-snap-stop: always;
}

.scroll-mx-16 {
  scroll-margin-left: 4rem;
  scroll-margin-right: 4rem;
}

.scroll-mx-4 {
  scroll-margin-left: 1rem;
  scroll-margin-right: 1rem;
}

.appearance-none {
  -webkit-appearance: none;
          appearance: none;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.place-self-center {
  place-self: center;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-clip {
  overflow: clip;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[3px\] {
  border-radius: 3px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-4 {
  border-bottom-width: 4px;
}

.border-b-8 {
  border-bottom-width: 8px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-l-\[12px\] {
  border-left-width: 12px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-8 {
  border-top-width: 8px;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-error-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 231 229 / var(--tw-border-opacity));
}

.border-error-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 65 52 / var(--tw-border-opacity));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(246 246 253 / var(--tw-border-opacity));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(237 237 251 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(165 165 185 / var(--tw-border-opacity));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(87 90 113 / var(--tw-border-opacity));
}

.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity));
}

.border-info-100 {
  --tw-border-opacity: 1;
  border-color: rgb(229 243 255 / var(--tw-border-opacity));
}

.border-info-500 {
  --tw-border-opacity: 1;
  border-color: rgb(49 156 255 / var(--tw-border-opacity));
}

.border-neutral-100 {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity));
}

.border-neutral-500 {
  --tw-border-opacity: 1;
  border-color: rgb(115 115 115 / var(--tw-border-opacity));
}

.border-primary-100 {
  --tw-border-opacity: 1;
  border-color: rgb(233 242 255 / var(--tw-border-opacity));
}

.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(27 105 255 / var(--tw-border-opacity));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.border-success-100 {
  --tw-border-opacity: 1;
  border-color: rgb(229 255 240 / var(--tw-border-opacity));
}

.border-success-500 {
  --tw-border-opacity: 1;
  border-color: rgb(0 182 76 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.border-warning-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 245 229 / var(--tw-border-opacity));
}

.border-warning-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 167 38 / var(--tw-border-opacity));
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-b-transparent {
  border-bottom-color: transparent;
}

.border-l-gray-300 {
  --tw-border-opacity: 1;
  border-left-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-t-black {
  --tw-border-opacity: 1;
  border-top-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-t-transparent {
  border-top-color: transparent;
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-error-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 231 229 / var(--tw-bg-opacity));
}

.bg-error-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 65 52 / var(--tw-bg-opacity));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 253 / var(--tw-bg-opacity));
}

.bg-gray-150 {
  background-color: #EFEFEFB3;
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 237 251 / var(--tw-bg-opacity));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 165 185 / var(--tw-bg-opacity));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(116 119 142 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(87 90 113 / var(--tw-bg-opacity));
}

.bg-info-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 243 255 / var(--tw-bg-opacity));
}

.bg-info-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(49 156 255 / var(--tw-bg-opacity));
}

.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.bg-overlay {
  background-color: rgba(0, 0, 10, 0.2);
}

.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 242 255 / var(--tw-bg-opacity));
}

.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(27 105 255 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-success-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 255 240 / var(--tw-bg-opacity));
}

.bg-success-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 182 76 / var(--tw-bg-opacity));
}

.bg-warning-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 245 229 / var(--tw-bg-opacity));
}

.bg-warning-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 167 38 / var(--tw-bg-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-black {
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-80\% {
  --tw-gradient-from-position: 80%;
}

.to-black {
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}

.to-primary-100 {
  --tw-gradient-to: #E9F2FF var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-20\% {
  --tw-gradient-to-position: 20%;
}

.to-50\% {
  --tw-gradient-to-position: 50%;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-current {
  fill: currentColor;
}

.object-cover {
  object-fit: cover;
}

.p-0\.5 {
  padding: 0.125rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-12 {
  padding: 3rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-start {
  text-align: start;
}

.align-baseline {
  vertical-align: baseline;
}

.align-bottom {
  vertical-align: bottom;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-2xs {
  font-size: 0.6rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[0\.5rem\] {
  font-size: 0.5rem;
}

.text-\[0\.9rem\] {
  font-size: 0.9rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.ordinal {
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-error-100 {
  --tw-text-opacity: 1;
  color: rgb(255 231 229 / var(--tw-text-opacity));
}

.text-error-500 {
  --tw-text-opacity: 1;
  color: rgb(255 65 52 / var(--tw-text-opacity));
}

.text-error-600 {
  --tw-text-opacity: 1;
  color: rgb(210 54 40 / var(--tw-text-opacity));
}

.text-error-700 {
  --tw-text-opacity: 1;
  color: rgb(164 41 30 / var(--tw-text-opacity));
}

.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(246 246 253 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(237 237 251 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(165 165 185 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(116 119 142 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(87 90 113 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(58 61 84 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(33 33 45 / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-info-100 {
  --tw-text-opacity: 1;
  color: rgb(229 243 255 / var(--tw-text-opacity));
}

.text-info-500 {
  --tw-text-opacity: 1;
  color: rgb(49 156 255 / var(--tw-text-opacity));
}

.text-info-600 {
  --tw-text-opacity: 1;
  color: rgb(36 121 197 / var(--tw-text-opacity));
}

.text-info-700 {
  --tw-text-opacity: 1;
  color: rgb(24 85 140 / var(--tw-text-opacity));
}

.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity));
}

.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity));
}

.text-primary-100 {
  --tw-text-opacity: 1;
  color: rgb(233 242 255 / var(--tw-text-opacity));
}

.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(27 105 255 / var(--tw-text-opacity));
}

.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(21 81 197 / var(--tw-text-opacity));
}

.text-primary-900 {
  --tw-text-opacity: 1;
  color: rgb(0 9 26 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-success-100 {
  --tw-text-opacity: 1;
  color: rgb(229 255 240 / var(--tw-text-opacity));
}

.text-success-500 {
  --tw-text-opacity: 1;
  color: rgb(0 182 76 / var(--tw-text-opacity));
}

.text-success-600 {
  --tw-text-opacity: 1;
  color: rgb(0 143 59 / var(--tw-text-opacity));
}

.text-success-700 {
  --tw-text-opacity: 1;
  color: rgb(0 104 43 / var(--tw-text-opacity));
}

.text-warning-100 {
  --tw-text-opacity: 1;
  color: rgb(255 245 229 / var(--tw-text-opacity));
}

.text-warning-500 {
  --tw-text-opacity: 1;
  color: rgb(255 167 38 / var(--tw-text-opacity));
}

.text-warning-600 {
  --tw-text-opacity: 1;
  color: rgb(197 129 30 / var(--tw-text-opacity));
}

.text-warning-700 {
  --tw-text-opacity: 1;
  color: rgb(140 91 20 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-70 {
  opacity: 0.7;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.\!transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 150ms !important;
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[max-height\] {
  transition-property: max-height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.\[vite\:css\] {
  vite: css;
}

.\[vite\:html\] {
  vite: html;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Helvetica, sans-serif;
  line-height: 1.643em;
  letter-spacing: 0;
  color: #424A50;
  font-weight: 400;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  background-color: #F9F9F9;
}

.tether-element {
  z-index: 10;
}

.opacity-0 {
  opacity: 0
}

.opacity-5 {
  opacity: 0.5
}

.opacity-1 {
  opacity: 1
}

.scale-0 {
  transform: scale(0)
}

.pointer {
  cursor: pointer
}

.not-allowed {
  cursor: not-allowed
}

.select-none {
  -webkit-user-select: none;
  user-select: none;
}

h1, h2, h3,
h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.013em;
  line-height: 1.4285em;
  margin: 0;
  &.regular {
    font-weight: 400;
  }
}

h1 {
  font-size: 2.857em;
}

h2 {
  font-size: 2.286em;
}

h3 {
  font-size: 1.571em;
}

h4 {
  font-size: 1.286em;
}

b, strong, .bold {
  font-weight: 400;
}

p, dl, ol, ul, pre, blockquote {
  margin: 0;
}

p {
  margin-bottom: 0.7143em;
}

.monospace {
  font-family: monospace;
}

a {
  color: #424a50;
  text-decoration: none;
}

a:focus, a:hover, a:visited, a:active {
  outline: none;
  text-decoration: none;
}

.caption {
  font-size: 0.857em;
  line-height: 1.429em;
  letter-spacing: 0.036em;
}

small, .small {
  font-size: 0.714em;
  line-height: 1.143em;
  letter-spacing: 0.036em;
}

/* Basscss Colors */

.light {
  color: #9E9DA3
}

.white {
  color: #ffffff
}

.muted {
  opacity: .5
}

.red {
  color: #fb0f3b
}

.purple {
  color: #7a6bbd
}

.green {
  color: #1aca8a
}

/* Basscss Typography */

.font-family-inherit {
  font-family: inherit
}

.font-size-inherit {
  font-size: inherit
}

.text-decoration-none {
  text-decoration: none
}

.regular {
  font-weight: 400
}

.italic {
  font-style: italic
}

.caps {
  text-transform: uppercase;
  letter-spacing: .2em;
}

.capitalize {
  text-transform: capitalize
}

.left-align {
  text-align: left
}

.center {
  text-align: center
}

.right-align {
  text-align: right
}

.justify {
  text-align: justify
}

.nowrap {
  white-space: nowrap
}

.break-word {
  word-wrap: break-word
}

.list-style-none {
  list-style: none
}

.underline {
  text-decoration: underline
}

.truncate {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-reset {
  list-style: none;
  padding-left: 0;
}

@keyframes __spinnerAnimation {
  0% {
    transform: rotate(0deg);
  }

  30% {
    transform: rotate(200deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.spinner {
  display: inline-block;
  height: 44px;
  width: 44px;
  border: 2px solid transparent;
  border-color: #ededed;
  border-right-color: red;
  border-radius: 50%;
  animation: __spinnerAnimation 0.8s linear infinite;
}

.spinner.spinner-small {
  width: 22px;
  height: 22px;
}

.__snackbar {
  z-index: 1000;
}

.__snackbar.__right {
  right: 0;
}

.__snackbar.__right .__snackbar_message {
  right: 0;
}

.__snackbar.__left {
  left: 0;
}

.__snackbar.__left .__snackbar_message {
  left: 0;
}

.__snackbar.__center {
  left: 50%;
  transform: translateX(-50%);
}

.__snackbar.__center .__snackbar_message {
  left: 50%;
  transform: translateX(-50%);
}

.__snackbar .__snackbar_message {
  min-width: 280px;
  border-bottom-width: 3px;
  transition: all 0.2s linear;
  transform: translateY(100px);
  animation: snackbarEnter 0.1s ease-in forwards;
  opacity: 0;
}

.__snackbar .__snackbar_message.success {
  border-bottom-color: #1ebf68;
}

.__snackbar .__snackbar_message.success .__snackbar_message_icon {
  color: #1ebf68;
}

.__snackbar .__snackbar_message.info {
  border-bottom-color: #acacac;
}

.__snackbar .__snackbar_message.info .__snackbar_message_icon {
  color: #acacac;
}

.__snackbar .__snackbar_message.warning {
  border-bottom-color: #fbad46;
}

.__snackbar .__snackbar_message.warning .__snackbar_message_icon {
  color: #fbad46;
}

.__snackbar .__snackbar_message.error {
  border-bottom-color: #ff4d4d;
}

.__snackbar .__snackbar_message.error .__snackbar_message_icon {
  color: #ff4d4d;
}

@keyframes: global(snackbarEnter) {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes Select-animation-spin {
  to {
    transform: rotate(1turn);
  }
}

.Select {
  position: relative;
}

.Select, .Select div, .Select input, .Select span {
  box-sizing: border-box;
}

.Select.is-disabled > .Select-control {
  background-color: #e0e3e4;
}

.Select.is-disabled > .Select-control:hover {
  box-shadow: none;
}

.Select.is-disabled .Select-arrow-zone {
  cursor: default;
  pointer-events: none;
}

.Select-control {
  background-color: #fff;
  border-color: #d5d9db #c7cdcf #acb4b7;
  border-radius: 3px;
  border: 1px solid #c7cdcf;
  color: #424a50;
  cursor: default;
  display: block;
  border-spacing: 0;
  border-collapse: separate;
  height: 40px;
  outline: none;
  overflow: hidden;
  position: relative;
  width: 100%;
  min-width: 252px;
}

.Select-control:hover {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
}

.Select-control .Select-input:focus {
  outline: none;
}

.is-searchable.is-open > .Select-control {
  cursor: text;
}

.is-open > .Select-control {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  background: #fff;
  border-color: #acb4b7 #c7cdcf #d5d9db;
}

.is-open > .Select-control .Select-arrow {
  top: -2px;
  border-color: transparent transparent #999;
  border-width: 0 5px 5px;
}

.is-searchable.is-focused:not(.is-open) > .Select-control {
  cursor: text;
}

.is-focused:not(.is-open) > .Select-control {
  border-color: #545c63 #606971 #606971;
  box-shadow: none;
}

.Select-placeholder, .Select--single > .Select-control .Select-value {
  bottom: 0;
  color: #bbc2c4;
  left: 0;
  line-height: 38px;
  padding-left: 10px;
  padding-right: 10px;
  position: absolute;
  right: 0;
  top: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.has-value.is-clearable.Select--single > .Select-control .Select-value {
  padding-right: 44px;
}

.has-value.Select--single > .Select-control .Select-value .Select-value-label, .has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {
  color: #424a50;
}

.has-value.Select--single > .Select-control .Select-value a.Select-value-label, .has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {
  cursor: pointer;
  text-decoration: none;
}

.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover, .has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover, .has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus, .has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {
  color: #545c63;
  outline: none;
  text-decoration: underline;
}

.Select-input {
  height: 38px;
  padding-left: 10px;
  padding-right: 10px;
  vertical-align: middle;
}

.Select-input > input {
  background: none transparent;
  border: 0 none;
  box-shadow: none;
  cursor: default;
  display: inline-block;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  outline: none;
  line-height: 14px;
  /* For IE 8 compatibility */
  padding: 0;
  -webkit-appearance: none;
}

.is-focused .Select-input > input {
  cursor: text;
}

.has-value.is-pseudo-focused .Select-input {
  opacity: 0;
}

.Select-control:not(.is-searchable) > .Select-input {
  outline: none;
}

.Select-loading-zone {
  cursor: pointer;
  display: block;
  position: absolute;
  top: 5px;
  right: 30px;
  text-align: center;
  vertical-align: middle;
  width: 16px;
}

.Select-loading {
  animation: Select-animation-spin 400ms infinite linear;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2px solid #c7cdcf;
  border-right-color: #424a50;
  display: inline-block;
  position: relative;
  vertical-align: middle;
}

.Select-clear-zone {
  animation: Select-animation-fadeIn 200ms;
  color: #999;
  cursor: pointer;
  display: table-cell;
  position: relative;
  text-align: center;
  vertical-align: middle;
  width: 19px;
}

.Select-clear-zone:hover {
  color: #d0021b;
}

.Select-clear {
  display: inline-block;
  font-size: 20px;
  line-height: 1;
}

.Select--multi .Select-clear-zone {
  width: 19px;
}

.Select--multi .Select-multi-value-wrapper {
  display: inline-block;
}

.Select .Select-aria-only {
  display: inline-block;
  height: 1px;
  width: 1px;
  margin: -1px;
  clip: rect(0, 0, 0, 0);
  overflow: hidden;
  float: left;
}

.Select-arrow-zone {
  cursor: pointer;
  display: inline-block;
  position: absolute;
  top: 5px;
  right: 0px;
  text-align: center;
  vertical-align: middle;
  width: 25px;
  padding-right: 5px;
}

.Select-arrow {
  border-color: #999 transparent transparent;
  border-style: solid;
  border-width: 5px 5px 2.5px;
  display: inline-block;
  height: 0;
  width: 0;
  position: relative;
}

.is-open .Select-arrow, .Select-arrow-zone:hover > .Select-arrow {
  border-top-color: #666;
}

@keyframes Select-animation-fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.Select-menu-outer {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
  background-color: #fff;
  border: 1px solid #c7cdcf;
  border-top-color: #e3e6e7;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  margin-top: 0.25rem;
  max-height: 200px;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 1000;
  -webkit-overflow-scrolling: touch;
  box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.13);
}

.Select-menu {
  max-height: 198px;
  overflow-y: auto;
}

.Select-option {
  box-sizing: border-box;
  background-color: #fff;
  color: #707e88;
  cursor: pointer;
  display: block;
  padding: 8px 10px;
  border-bottom: 1px solid #e0e3e4;
}

.Select-option:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}

.Select-option.is-selected {
  background-color: #f5faff;
  color: #424a50;
  font-weight: bold;
}

.Select-option.is-focused {
  background-color: #f9f9f9;
  color: #424a50;
}

.Select-option.is-disabled {
  color: #dfe2e5;
  cursor: default;
}

.Select-noresults {
  box-sizing: border-box;
  color: #a7b0b7;
  cursor: default;
  display: block;
  padding: 8px 10px;
}

.cs-progress {
  display: block;
  width: 100%;
  height: 0.5625rem;
  margin: .5rem 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, .125);
  border: 0;
  border-radius: 10000px;
  -webkit-appearance: none;
}

.cs-progress::-webkit-progress-bar {
  -webkit-appearance: none;
  background-color: rgba(0, 0, 0, .125)
}

.cs-progress::-webkit-progress-value {
  -webkit-appearance: none;
  background-color: currentcolor;
}

.cs-progress::-moz-progress-bar {
  background-color: currentcolor;
}

/* Basscss Position */

.relative {
  position: relative
}

.absolute {
  position: absolute
}

.fixed {
  position: fixed
}

.top-0 {
  top: 0
}

.right-0 {
  right: 0
}

.bottom-0 {
  bottom: 0
}

.left-0 {
  left: 0
}

.top-100 {
  top: 100%
}

.right-100 {
  right: 100%
}

.bottom-100 {
  bottom: 100%
}

.left-100 {
  left: 100%
}

.z1 {
  z-index: 1
}

.z2 {
  z-index: 2
}

.z3 {
  z-index: 3
}

.z4 {
  z-index: 4
}

/* Basscss Responsive Position */

@media (min-width: 40em) {
  .sm-relative {
    position: relative
  }

  .sm-absolute {
    position: absolute
  }

  .sm-fixed {
    position: fixed
  }

  .sm-top-0 {
    top: 0
  }

  .sm-right-0 {
    right: 0
  }

  .sm-bottom-0 {
    bottom: 0
  }

  .sm-left-0 {
    left: 0
  }
}

@media (min-width: 52em) {
  .md-relative {
    position: relative
  }

  .md-absolute {
    position: absolute
  }

  .md-fixed {
    position: fixed
  }

  .md-top-0 {
    top: 0
  }

  .md-right-0 {
    right: 0
  }

  .md-bottom-0 {
    bottom: 0
  }

  .md-left-0 {
    left: 0
  }
}

@media (min-width: 64em) {
  .lg-relative {
    position: relative
  }

  .lg-absolute {
    position: absolute
  }

  .lg-fixed {
    position: fixed
  }

  .lg-top-0 {
    top: 0
  }

  .lg-right-0 {
    right: 0
  }

  .lg-bottom-0 {
    bottom: 0
  }

  .lg-left-0 {
    left: 0
  }
}

/* Basscss Responsive Typography */

@media (min-width: 40em) {
  .sm-left-align {
    text-align: left
  }

  .sm-center {
    text-align: center
  }

  .sm-right-align {
    text-align: right
  }

  .sm-justify {
    text-align: justify
  }
}

@media (min-width: 52em) {
  .md-left-align {
    text-align: left
  }

  .md-center {
    text-align: center
  }

  .md-right-align {
    text-align: right
  }

  .md-justify {
    text-align: justify
  }
}

@media (min-width: 64em) {
  .lg-left-align {
    text-align: left
  }

  .lg-center {
    text-align: center
  }

  .lg-right-align {
    text-align: right
  }

  .lg-justify {
    text-align: justify
  }
}

/* Basscss Padding */

.p0 {
  padding: 0
}

.pt0 {
  padding-top: 0
}

.pr0 {
  padding-right: 0
}

.pb0 {
  padding-bottom: 0
}

.pl0 {
  padding-left: 0
}

.px0 {
  padding-left: 0;
  padding-right: 0
}

.py0 {
  padding-top: 0;
  padding-bottom: 0
}

.p1 {
  padding: .5rem
}

.pt1 {
  padding-top: .5rem
}

.pr1 {
  padding-right: .5rem
}

.pb1 {
  padding-bottom: .5rem
}

.pl1 {
  padding-left: .5rem
}

.py1 {
  padding-top: .5rem;
  padding-bottom: .5rem
}

.px1 {
  padding-left: .5rem;
  padding-right: .5rem
}

.p2 {
  padding: 1rem
}

.pt2 {
  padding-top: 1rem
}

.pr2 {
  padding-right: 1rem
}

.pb2 {
  padding-bottom: 1rem
}

.pl2 {
  padding-left: 1rem
}

.py2 {
  padding-top: 1rem;
  padding-bottom: 1rem
}

.px2 {
  padding-left: 1rem;
  padding-right: 1rem
}

.p3 {
  padding: 2rem
}

.pt3 {
  padding-top: 2rem
}

.pr3 {
  padding-right: 2rem
}

.pb3 {
  padding-bottom: 2rem
}

.pl3 {
  padding-left: 2rem
}

.py3 {
  padding-top: 2rem;
  padding-bottom: 2rem
}

.px3 {
  padding-left: 2rem;
  padding-right: 2rem
}

.p4 {
  padding: 4rem
}

.pt4 {
  padding-top: 4rem
}

.pr4 {
  padding-right: 4rem
}

.pb4 {
  padding-bottom: 4rem
}

.pl4 {
  padding-left: 4rem
}

.py4 {
  padding-top: 4rem;
  padding-bottom: 4rem
}

.px4 {
  padding-left: 4rem;
  padding-right: 4rem
}

/* Basscss Responsive Padding */

@media (min-width: 40em) {
  .sm-p0 {
    padding: 0
  }

  .sm-pt0 {
    padding-top: 0
  }

  .sm-pr0 {
    padding-right: 0
  }

  .sm-pb0 {
    padding-bottom: 0
  }

  .sm-pl0 {
    padding-left: 0
  }

  .sm-px0 {
    padding-left: 0;
    padding-right: 0
  }

  .sm-py0 {
    padding-top: 0;
    padding-bottom: 0
  }

  .sm-p1 {
    padding: .5rem
  }

  .sm-pt1 {
    padding-top: .5rem
  }

  .sm-pr1 {
    padding-right: .5rem
  }

  .sm-pb1 {
    padding-bottom: .5rem
  }

  .sm-pl1 {
    padding-left: .5rem
  }

  .sm-px1 {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .sm-py1 {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .sm-p2 {
    padding: 1rem
  }

  .sm-pt2 {
    padding-top: 1rem
  }

  .sm-pr2 {
    padding-right: 1rem
  }

  .sm-pb2 {
    padding-bottom: 1rem
  }

  .sm-pl2 {
    padding-left: 1rem
  }

  .sm-px2 {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .sm-py2 {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .sm-p3 {
    padding: 2rem
  }

  .sm-pt3 {
    padding-top: 2rem
  }

  .sm-pr3 {
    padding-right: 2rem
  }

  .sm-pb3 {
    padding-bottom: 2rem
  }

  .sm-pl3 {
    padding-left: 2rem
  }

  .sm-px3 {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .sm-py3 {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .sm-p4 {
    padding: 4rem
  }

  .sm-pt4 {
    padding-top: 4rem
  }

  .sm-pr4 {
    padding-right: 4rem
  }

  .sm-pb4 {
    padding-bottom: 4rem
  }

  .sm-pl4 {
    padding-left: 4rem
  }

  .sm-px4 {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .sm-py4 {
    padding-top: 4rem;
    padding-bottom: 4rem
  }
}

@media (min-width: 52em) {
  .md-p0 {
    padding: 0
  }

  .md-pt0 {
    padding-top: 0
  }

  .md-pr0 {
    padding-right: 0
  }

  .md-pb0 {
    padding-bottom: 0
  }

  .md-pl0 {
    padding-left: 0
  }

  .md-px0 {
    padding-left: 0;
    padding-right: 0
  }

  .md-py0 {
    padding-top: 0;
    padding-bottom: 0
  }

  .md-p1 {
    padding: .5rem
  }

  .md-pt1 {
    padding-top: .5rem
  }

  .md-pr1 {
    padding-right: .5rem
  }

  .md-pb1 {
    padding-bottom: .5rem
  }

  .md-pl1 {
    padding-left: .5rem
  }

  .md-px1 {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .md-py1 {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .md-p2 {
    padding: 1rem
  }

  .md-pt2 {
    padding-top: 1rem
  }

  .md-pr2 {
    padding-right: 1rem
  }

  .md-pb2 {
    padding-bottom: 1rem
  }

  .md-pl2 {
    padding-left: 1rem
  }

  .md-px2 {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .md-py2 {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .md-p3 {
    padding: 2rem
  }

  .md-pt3 {
    padding-top: 2rem
  }

  .md-pr3 {
    padding-right: 2rem
  }

  .md-pb3 {
    padding-bottom: 2rem
  }

  .md-pl3 {
    padding-left: 2rem
  }

  .md-px3 {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .md-py3 {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .md-p4 {
    padding: 4rem
  }

  .md-pt4 {
    padding-top: 4rem
  }

  .md-pr4 {
    padding-right: 4rem
  }

  .md-pb4 {
    padding-bottom: 4rem
  }

  .md-pl4 {
    padding-left: 4rem
  }

  .md-px4 {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .md-py4 {
    padding-top: 4rem;
    padding-bottom: 4rem
  }
}

@media (min-width: 64em) {
  .lg-p0 {
    padding: 0
  }

  .lg-pt0 {
    padding-top: 0
  }

  .lg-pr0 {
    padding-right: 0
  }

  .lg-pb0 {
    padding-bottom: 0
  }

  .lg-pl0 {
    padding-left: 0
  }

  .lg-px0 {
    padding-left: 0;
    padding-right: 0
  }

  .lg-py0 {
    padding-top: 0;
    padding-bottom: 0
  }

  .lg-p1 {
    padding: .5rem
  }

  .lg-pt1 {
    padding-top: .5rem
  }

  .lg-pr1 {
    padding-right: .5rem
  }

  .lg-pb1 {
    padding-bottom: .5rem
  }

  .lg-pl1 {
    padding-left: .5rem
  }

  .lg-px1 {
    padding-left: .5rem;
    padding-right: .5rem
  }

  .lg-py1 {
    padding-top: .5rem;
    padding-bottom: .5rem
  }

  .lg-p2 {
    padding: 1rem
  }

  .lg-pt2 {
    padding-top: 1rem
  }

  .lg-pr2 {
    padding-right: 1rem
  }

  .lg-pb2 {
    padding-bottom: 1rem
  }

  .lg-pl2 {
    padding-left: 1rem
  }

  .lg-px2 {
    padding-left: 1rem;
    padding-right: 1rem
  }

  .lg-py2 {
    padding-top: 1rem;
    padding-bottom: 1rem
  }

  .lg-p3 {
    padding: 2rem
  }

  .lg-pt3 {
    padding-top: 2rem
  }

  .lg-pr3 {
    padding-right: 2rem
  }

  .lg-pb3 {
    padding-bottom: 2rem
  }

  .lg-pl3 {
    padding-left: 2rem
  }

  .lg-px3 {
    padding-left: 2rem;
    padding-right: 2rem
  }

  .lg-py3 {
    padding-top: 2rem;
    padding-bottom: 2rem
  }

  .lg-p4 {
    padding: 4rem
  }

  .lg-pt4 {
    padding-top: 4rem
  }

  .lg-pr4 {
    padding-right: 4rem
  }

  .lg-pb4 {
    padding-bottom: 4rem
  }

  .lg-pl4 {
    padding-left: 4rem
  }

  .lg-px4 {
    padding-left: 4rem;
    padding-right: 4rem
  }

  .lg-py4 {
    padding-top: 4rem;
    padding-bottom: 4rem
  }
}

/* Basscss Margin */

.m0 {
  margin: 0
}

.mt0 {
  margin-top: 0
}

.mr0 {
  margin-right: 0
}

.mb0 {
  margin-bottom: 0
}

.ml0 {
  margin-left: 0
}

.mx0 {
  margin-left: 0;
  margin-right: 0
}

.my0 {
  margin-top: 0;
  margin-bottom: 0
}

.m1 {
  margin: .5rem
}

.mt1 {
  margin-top: .5rem
}

.mr1 {
  margin-right: .5rem
}

.mb1 {
  margin-bottom: .5rem
}

.ml1 {
  margin-left: .5rem
}

.mx1 {
  margin-left: .5rem;
  margin-right: .5rem
}

.my1 {
  margin-top: .5rem;
  margin-bottom: .5rem
}

.m2 {
  margin: 1rem
}

.mt2 {
  margin-top: 1rem
}

.mr2 {
  margin-right: 1rem
}

.mb2 {
  margin-bottom: 1rem
}

.ml2 {
  margin-left: 1rem
}

.mx2 {
  margin-left: 1rem;
  margin-right: 1rem
}

.my2 {
  margin-top: 1rem;
  margin-bottom: 1rem
}

.m3 {
  margin: 2rem
}

.mt3 {
  margin-top: 2rem
}

.mr3 {
  margin-right: 2rem
}

.mb3 {
  margin-bottom: 2rem
}

.ml3 {
  margin-left: 2rem
}

.mx3 {
  margin-left: 2rem;
  margin-right: 2rem
}

.my3 {
  margin-top: 2rem;
  margin-bottom: 2rem
}

.m4 {
  margin: 4rem
}

.mt4 {
  margin-top: 4rem
}

.mr4 {
  margin-right: 4rem
}

.mb4 {
  margin-bottom: 4rem
}

.ml4 {
  margin-left: 4rem
}

.mx4 {
  margin-left: 4rem;
  margin-right: 4rem
}

.my4 {
  margin-top: 4rem;
  margin-bottom: 4rem
}

.mxn1 {
  margin-left: -.5rem;
  margin-right: -.5rem;
}

.mxn2 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.mxn3 {
  margin-left: -2rem;
  margin-right: -2rem;
}

.mxn4 {
  margin-left: -4rem;
  margin-right: -4rem;
}

.ml-auto {
  margin-left: auto
}

.mr-auto {
  margin-right: auto
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 40em) {
  .sm-m0 {
    margin: 0
  }

  .sm-mt0 {
    margin-top: 0
  }

  .sm-mr0 {
    margin-right: 0
  }

  .sm-mb0 {
    margin-bottom: 0
  }

  .sm-ml0 {
    margin-left: 0
  }

  .sm-mx0 {
    margin-left: 0;
    margin-right: 0
  }

  .sm-my0 {
    margin-top: 0;
    margin-bottom: 0
  }

  .sm-m1 {
    margin: .5rem
  }

  .sm-mt1 {
    margin-top: .5rem
  }

  .sm-mr1 {
    margin-right: .5rem
  }

  .sm-mb1 {
    margin-bottom: .5rem
  }

  .sm-ml1 {
    margin-left: .5rem
  }

  .sm-mx1 {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .sm-my1 {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .sm-m2 {
    margin: 1rem
  }

  .sm-mt2 {
    margin-top: 1rem
  }

  .sm-mr2 {
    margin-right: 1rem
  }

  .sm-mb2 {
    margin-bottom: 1rem
  }

  .sm-ml2 {
    margin-left: 1rem
  }

  .sm-mx2 {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .sm-my2 {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .sm-m3 {
    margin: 2rem
  }

  .sm-mt3 {
    margin-top: 2rem
  }

  .sm-mr3 {
    margin-right: 2rem
  }

  .sm-mb3 {
    margin-bottom: 2rem
  }

  .sm-ml3 {
    margin-left: 2rem
  }

  .sm-mx3 {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .sm-my3 {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .sm-m4 {
    margin: 4rem
  }

  .sm-mt4 {
    margin-top: 4rem
  }

  .sm-mr4 {
    margin-right: 4rem
  }

  .sm-mb4 {
    margin-bottom: 4rem
  }

  .sm-ml4 {
    margin-left: 4rem
  }

  .sm-mx4 {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .sm-my4 {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .sm-mxn1 {
    margin-left: -.5rem;
    margin-right: -.5rem
  }

  .sm-mxn2 {
    margin-left: -1rem;
    margin-right: -1rem
  }

  .sm-mxn3 {
    margin-left: -2rem;
    margin-right: -2rem
  }

  .sm-mxn4 {
    margin-left: -4rem;
    margin-right: -4rem
  }

  .sm-ml-auto {
    margin-left: auto
  }

  .sm-mr-auto {
    margin-right: auto
  }

  .sm-mx-auto {
    margin-left: auto;
    margin-right: auto
  }
}

@media (min-width: 52em) {
  .md-m0 {
    margin: 0
  }

  .md-mt0 {
    margin-top: 0
  }

  .md-mr0 {
    margin-right: 0
  }

  .md-mb0 {
    margin-bottom: 0
  }

  .md-ml0 {
    margin-left: 0
  }

  .md-mx0 {
    margin-left: 0;
    margin-right: 0
  }

  .md-my0 {
    margin-top: 0;
    margin-bottom: 0
  }

  .md-m1 {
    margin: .5rem
  }

  .md-mt1 {
    margin-top: .5rem
  }

  .md-mr1 {
    margin-right: .5rem
  }

  .md-mb1 {
    margin-bottom: .5rem
  }

  .md-ml1 {
    margin-left: .5rem
  }

  .md-mx1 {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .md-my1 {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .md-m2 {
    margin: 1rem
  }

  .md-mt2 {
    margin-top: 1rem
  }

  .md-mr2 {
    margin-right: 1rem
  }

  .md-mb2 {
    margin-bottom: 1rem
  }

  .md-ml2 {
    margin-left: 1rem
  }

  .md-mx2 {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .md-my2 {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .md-m3 {
    margin: 2rem
  }

  .md-mt3 {
    margin-top: 2rem
  }

  .md-mr3 {
    margin-right: 2rem
  }

  .md-mb3 {
    margin-bottom: 2rem
  }

  .md-ml3 {
    margin-left: 2rem
  }

  .md-mx3 {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .md-my3 {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .md-m4 {
    margin: 4rem
  }

  .md-mt4 {
    margin-top: 4rem
  }

  .md-mr4 {
    margin-right: 4rem
  }

  .md-mb4 {
    margin-bottom: 4rem
  }

  .md-ml4 {
    margin-left: 4rem
  }

  .md-mx4 {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .md-my4 {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .md-mxn1 {
    margin-left: -.5rem;
    margin-right: -.5rem;
  }

  .md-mxn2 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .md-mxn3 {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  .md-mxn4 {
    margin-left: -4rem;
    margin-right: -4rem;
  }

  .md-ml-auto {
    margin-left: auto
  }

  .md-mr-auto {
    margin-right: auto
  }

  .md-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
}

@media (min-width: 64em) {
  .lg-m0 {
    margin: 0
  }

  .lg-mt0 {
    margin-top: 0
  }

  .lg-mr0 {
    margin-right: 0
  }

  .lg-mb0 {
    margin-bottom: 0
  }

  .lg-ml0 {
    margin-left: 0
  }

  .lg-mx0 {
    margin-left: 0;
    margin-right: 0
  }

  .lg-my0 {
    margin-top: 0;
    margin-bottom: 0
  }

  .lg-m1 {
    margin: .5rem
  }

  .lg-mt1 {
    margin-top: .5rem
  }

  .lg-mr1 {
    margin-right: .5rem
  }

  .lg-mb1 {
    margin-bottom: .5rem
  }

  .lg-ml1 {
    margin-left: .5rem
  }

  .lg-mx1 {
    margin-left: .5rem;
    margin-right: .5rem
  }

  .lg-my1 {
    margin-top: .5rem;
    margin-bottom: .5rem
  }

  .lg-m2 {
    margin: 1rem
  }

  .lg-mt2 {
    margin-top: 1rem
  }

  .lg-mr2 {
    margin-right: 1rem
  }

  .lg-mb2 {
    margin-bottom: 1rem
  }

  .lg-ml2 {
    margin-left: 1rem
  }

  .lg-mx2 {
    margin-left: 1rem;
    margin-right: 1rem
  }

  .lg-my2 {
    margin-top: 1rem;
    margin-bottom: 1rem
  }

  .lg-m3 {
    margin: 2rem
  }

  .lg-mt3 {
    margin-top: 2rem
  }

  .lg-mr3 {
    margin-right: 2rem
  }

  .lg-mb3 {
    margin-bottom: 2rem
  }

  .lg-ml3 {
    margin-left: 2rem
  }

  .lg-mx3 {
    margin-left: 2rem;
    margin-right: 2rem
  }

  .lg-my3 {
    margin-top: 2rem;
    margin-bottom: 2rem
  }

  .lg-m4 {
    margin: 4rem
  }

  .lg-mt4 {
    margin-top: 4rem
  }

  .lg-mr4 {
    margin-right: 4rem
  }

  .lg-mb4 {
    margin-bottom: 4rem
  }

  .lg-ml4 {
    margin-left: 4rem
  }

  .lg-mx4 {
    margin-left: 4rem;
    margin-right: 4rem
  }

  .lg-my4 {
    margin-top: 4rem;
    margin-bottom: 4rem
  }

  .lg-mxn1 {
    margin-left: -.5rem;
    margin-right: -.5rem;
  }

  .lg-mxn2 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .lg-mxn3 {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  .lg-mxn4 {
    margin-left: -4rem;
    margin-right: -4rem;
  }

  .lg-ml-auto {
    margin-left: auto
  }

  .lg-mr-auto {
    margin-right: auto
  }

  .lg-mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Basscss Layout */

.inline {
  display: inline
}

.block {
  display: block
}

.inline-block {
  display: inline-block
}

.table {
  display: table
}

.table-cell {
  display: table-cell
}

.overflow-hidden {
  overflow: hidden
}

.overflow-scroll {
  overflow: scroll
}

.overflow-auto {
  overflow: auto
}

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table
}

.clearfix:after {
  clear: both
}

.left {
  float: left
}

.right {
  float: right
}

.fit {
  max-width: 100%
}

.border-box {
  box-sizing: border-box
}

/* Basscss Hide */

.hide {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
}

@media (max-width: 40em) {
  .xs-hide {
    display: none !important
  }
}

@media (min-width: 40em) and (max-width: 52em) {
  .sm-hide {
    display: none !important
  }
}

@media (min-width: 52em) and (max-width: 64em) {
  .md-hide {
    display: none !important
  }
}

@media (min-width: 64em) {
  .lg-hide {
    display: none !important
  }
}

.display-none {
  display: none !important
}

/* Basscss Responsive Layout */

@media (min-width: 40em) {
  .sm-inline {
    display: inline
  }

  .sm-block {
    display: block
  }

  .sm-inline-block {
    display: inline-block
  }

  .sm-table {
    display: table
  }

  .sm-table-cell {
    display: table-cell
  }

  .sm-overflow-hidden {
    overflow: hidden
  }

  .sm-overflow-scroll {
    overflow: scroll
  }

  .sm-overflow-auto {
    overflow: auto
  }

  .sm-left {
    float: left
  }

  .sm-right {
    float: right
  }

  .sm-flex {
    display: flex
  }
}

@media (min-width: 52em) {
  .md-inline {
    display: inline
  }

  .md-block {
    display: block
  }

  .md-inline-block {
    display: inline-block
  }

  .md-table {
    display: table
  }

  .md-table-cell {
    display: table-cell
  }

  .md-overflow-hidden {
    overflow: hidden
  }

  .md-overflow-scroll {
    overflow: scroll
  }

  .md-overflow-auto {
    overflow: auto
  }

  .md-left {
    float: left
  }

  .md-right {
    float: right
  }

  .md-flex {
    display: flex
  }
}

@media (min-width: 64em) {
  .lg-inline {
    display: inline
  }

  .lg-block {
    display: block
  }

  .lg-inline-block {
    display: inline-block
  }

  .lg-table {
    display: table
  }

  .lg-table-cell {
    display: table-cell
  }

  .lg-overflow-hidden {
    overflow: hidden
  }

  .lg-overflow-scroll {
    overflow: scroll
  }

  .lg-overflow-auto {
    overflow: auto
  }

  .lg-left {
    float: left
  }

  .lg-right {
    float: right
  }

  .lg-flex {
    display: flex
  }
}

.cs-img {
  max-width: 100%;
  height: auto;
}

.cs-svg {
  max-height: 100%;
}

.max-width-1 {
  max-width: 24rem
}

.max-width-2 {
  max-width: 32rem
}

.max-width-3 {
  max-width: 48rem
}

.max-width-4 {
  max-width: 64rem
}

.max-width-5 {
  max-width: 80rem
}

.max-width-6 {
  max-width: 96rem
}

.full-height {
  height: 100vh
}

.half-height {
  height: 50vh
}

/* Basscss Grid */

.col {
  float: left;
  box-sizing: border-box;
}

.col-right {
  float: right;
  box-sizing: border-box;
}

.col-1 {
  width: 8.33333%;
}

.col-2 {
  width: 16.66667%;
}

.col-3 {
  width: 25%;
}

.col-4 {
  width: 33.33333%;
}

.col-5 {
  width: 41.66667%;
}

.col-6 {
  width: 50%;
}

.col-7 {
  width: 58.33333%;
}

.col-8 {
  width: 66.66667%;
}

.col-9 {
  width: 75%;
}

.col-10 {
  width: 83.33333%;
}

.col-11 {
  width: 91.66667%;
}

.col-12 {
  width: 100%;
}

@media (min-width: 40em) {
  .sm-col {
    float: left;
    box-sizing: border-box;
  }

  .sm-col-right {
    float: right;
    box-sizing: border-box;
  }

  .sm-col-1 {
    width: 8.33333%;
  }

  .sm-col-2 {
    width: 16.66667%;
  }

  .sm-col-3 {
    width: 25%;
  }

  .sm-col-4 {
    width: 33.33333%;
  }

  .sm-col-5 {
    width: 41.66667%;
  }

  .sm-col-6 {
    width: 50%;
  }

  .sm-col-7 {
    width: 58.33333%;
  }

  .sm-col-8 {
    width: 66.66667%;
  }

  .sm-col-9 {
    width: 75%;
  }

  .sm-col-10 {
    width: 83.33333%;
  }

  .sm-col-11 {
    width: 91.66667%;
  }

  .sm-col-12 {
    width: 100%;
  }
}

@media (min-width: 52em) {
  .md-col {
    float: left;
    box-sizing: border-box;
  }

  .md-col-right {
    float: right;
    box-sizing: border-box;
  }

  .md-col-1 {
    width: 8.33333%;
  }

  .md-col-2 {
    width: 16.66667%;
  }

  .md-col-3 {
    width: 25%;
  }

  .md-col-4 {
    width: 33.33333%;
  }

  .md-col-5 {
    width: 41.66667%;
  }

  .md-col-6 {
    width: 50%;
  }

  .md-col-7 {
    width: 58.33333%;
  }

  .md-col-8 {
    width: 66.66667%;
  }

  .md-col-9 {
    width: 75%;
  }

  .md-col-10 {
    width: 83.33333%;
  }

  .md-col-11 {
    width: 91.66667%;
  }

  .md-col-12 {
    width: 100%;
  }
}

@media (min-width: 64em) {
  .lg-col {
    float: left;
    box-sizing: border-box;
  }

  .lg-col-right {
    float: right;
    box-sizing: border-box;
  }

  .lg-col-1 {
    width: 8.33333%;
  }

  .lg-col-2 {
    width: 16.66667%;
  }

  .lg-col-3 {
    width: 25%;
  }

  .lg-col-4 {
    width: 33.33333%;
  }

  .lg-col-5 {
    width: 41.66667%;
  }

  .lg-col-6 {
    width: 50%;
  }

  .lg-col-7 {
    width: 58.33333%;
  }

  .lg-col-8 {
    width: 66.66667%;
  }

  .lg-col-9 {
    width: 75%;
  }

  .lg-col-10 {
    width: 83.33333%;
  }

  .lg-col-11 {
    width: 91.66667%;
  }

  .lg-col-12 {
    width: 100%;
  }
}

@media (min-width: 86em) {
  .xl-col {
    float: left;
    box-sizing: border-box;
  }

  .xl-col-right {
    float: right;
    box-sizing: border-box;
  }

  .xl-col-1 {
    width: 8.33333%;
  }

  .xl-col-2 {
    width: 16.66667%;
  }

  .xl-col-3 {
    width: 25%;
  }

  .xl-col-4 {
    width: 33.33333%;
  }

  .xl-col-5 {
    width: 41.66667%;
  }

  .xl-col-6 {
    width: 50%;
  }

  .xl-col-7 {
    width: 58.33333%;
  }

  .xl-col-8 {
    width: 66.66667%;
  }

  .xl-col-9 {
    width: 75%;
  }

  .xl-col-10 {
    width: 83.33333%;
  }

  .xl-col-11 {
    width: 91.66667%;
  }

  .xl-col-12 {
    width: 100%;
  }
}

.cs-label {
  display: block;
  font-size: 0.857rem;
  line-height: 1.429rem;
  font-weight: 400;
  color: #80878e;
}

.cs-input, .cs-textarea {
  border-radius: 3px;
  background: #fff;
  border: 1px solid #c7cdcf;
  font-size: 1rem;
  color: #424a50;
  min-width: 0px;
  display: block;
}

.cs-input:hover, .cs-textarea:hover, .cs-input:focus, .cs-textarea:focus {
  border-color: #545c63;
  outline: none;
}

.cs-input:not(:placeholder-shown), .cs-textarea:not(:placeholder-shown) {
  border-color: #92999f;
}

.cs-input.success, .cs-textarea.success {
  border-color: #26d878;
}

.cs-input.error, .cs-textarea.error {
  border-color: #fc333d;
}

.cs-input:disabled, .cs-textarea:disabled {
  background: #e0e3e4;
  border-color: #e0e3e4;
  cursor: not-allowed;
}

.cs-input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}

.cs-input {
  padding: 0 0.75rem;
  height: 40px;
}

.cs-textarea {
  padding: 0.75rem;
  resize: none;
  min-height: 124px;
}

.__checkbox-wrap {
  display: inline-flex;
}

.__checkbox-wrap:hover .__checkbox-input:not(.__checkbox-disabled) {
  color: #fb0f3b;
}

.__radio-wrap {
  display: inline-flex;
}

.__radio-wrap:hover .__radio-input:not(.__radio-disabled) {
  color: #fb0f3b;
}

.input-success, .input-error {
  display: block;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
}

.input-success:before, .input-error:before {
  font-family: 'Material Icons';
  vertical-align: middle;
  font-size: 1rem;
  margin-right: 0.25rem;
}

.input-success {
  color: #26d878;
}

.input-success:before {
  content: '\E86C';
}

.input-error {
  color: #fc333d;
}

.input-error:before {
  content: '\E000';
}

::-webkit-input-placeholder {
  color: #bbc2c4;
  font-weight: 400;
  font-size: 0.875rem;
}

::-moz-placeholder {
  color: #bbc2c4;
  font-weight: 400;
  font-size: 0.875rem;
}

:-ms-input-placeholder {
  color: #bbc2c4;
  font-weight: 400;
  font-size: 0.875rem;
}

:-moz-placeholder {
  color: #bbc2c4;
  font-weight: 400;
  font-size: 0.875rem;
}

.flex {
  display: flex
}

.inline-flex {
  display: inline-flex;
}

.flex-column {
  flex-direction: column
}

.flex-wrap {
  flex-wrap: wrap
}

.items-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  -ms-grid-row-align: flex-start;
  align-items: flex-start
}

.items-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  -ms-grid-row-align: flex-end;
  align-items: flex-end
}

.items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -ms-grid-row-align: center;
  align-items: center
}

.items-baseline {
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  -ms-grid-row-align: initial;
  align-items: baseline
}

.items-stretch {
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  -ms-grid-row-align: stretch;
  align-items: stretch
}

.self-start {
  align-self: flex-start
}

.self-end {
  align-self: flex-end
}

.self-center {
  align-self: center
}

.self-baseline {
  align-self: baseline
}

.self-stretch {
  align-self: stretch
}

.justify-start {
  justify-content: flex-start
}

.justify-end {
  justify-content: flex-end
}

.justify-center {
  justify-content: center
}

.justify-between {
  justify-content: space-between
}

.justify-around {
  justify-content: space-around
}

.content-start {
  align-content: flex-start
}

.content-end {
  align-content: flex-end
}

.content-center {
  align-content: center
}

.content-between {
  align-content: space-between
}

.content-around {
  align-content: space-around
}

.content-stretch {
  align-content: stretch
}

/* 1. Fix for Chrome 44 bug. https://code.google.com/p/chromium/issues/detail?id=506893 */

.flex-auto {
  flex: 1 1 auto;
  min-width: 0;
  /* 1 */
  min-height: 0;
  /* 1 */
}

.flex-none {
  flex: none
}

.order-0 {
  order: 0
}

.order-1 {
  order: 1
}

.order-2 {
  order: 2
}

.order-3 {
  order: 3
}

.order-last {
  order: 99999
}

.__dialog {
  width: 320px;
  transform: scaleX(0);
  animation: dialog 0.1s linear forwards;
}

.__dialog.__dialog_left {
  transform-origin: left;
}

.__dialog.__dialog_right {
  transform-origin: right;
}

@keyframes: global(dialog) {
  0% {
    transform: scaleX(0);
  }

  100% {
    transform: scaleX(1);
  }
}

.datepicker-inline {
  border: none;
  height: auto;
  padding: 0;
  font-family: inherit;
}

.react-datepicker-wrapper {
  display: inline-block;
  position: relative;
}

.react-datepicker-wrapper:before {
  content: '\E916';
  font-family: 'Material Icons';
  position: absolute;
  left: 0;
  padding-left: 0.5rem;
  z-index: 1;
  top: 0.35rem;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
}

.react-datepicker-wrapper input {
  padding-left: 2.5rem;
  font-family: 'Roboto';
  font-size: 1rem;
}

.react-datepicker {
  font-family: "Roboto";
  font-size: 0.875rem;
  background-color: #fff;
  color: #424a50;
  border: 1px solid #c7cdcf;
  border-radius: 3px;
  display: inline-block;
  position: relative;
  box-shadow: 0 4px 7px 0 rgba(84, 92, 99, 0.23);
}

.react-datepicker__triangle {
  position: absolute;
  left: 50px;
}

.react-datepicker-popper {
  z-index: 2;
}

.react-datepicker-popper[data-placement^="bottom"] {
  margin-top: 2px;
}

.react-datepicker-popper[data-placement^="top"] {
  margin-bottom: 2px;
}

.react-datepicker-popper[data-placement^="right"] {
  margin-left: 0px;
}

.react-datepicker-popper[data-placement^="right"] .react-datepicker__triangle {
  left: auto;
  right: 42px;
}

.react-datepicker-popper[data-placement^="left"] {
  margin-right: 0px;
}

.react-datepicker-popper[data-placement^="left"] .react-datepicker__triangle {
  left: 42px;
  right: auto;
}

.react-datepicker__header {
  text-align: center;
  background-color: #fff;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  position: relative;
}

.react-datepicker__header--time {
  padding-bottom: 8px;
}

.react-datepicker__year-dropdown-container--select, .react-datepicker__month-dropdown-container--select, .react-datepicker__year-dropdown-container--scroll, .react-datepicker__month-dropdown-container--scroll {
  display: inline-block;
  margin: 0 2px;
}

.react-datepicker__current-month {
  margin: 0.875rem 0 !important;
}

.react-datepicker__current-month, .react-datepicker-time__header {
  margin-top: 0;
  color: #424a50;
  font-weight: bold;
  font-size: 1.0325rem;
}

.react-datepicker__navigation {
  position: absolute;
  top: 12px;
  z-index: 1;
  font-size: 1.25rem;
  cursor: pointer;
}

.react-datepicker__navigation--previous {
  left: 10px;
}

.react-datepicker__navigation--previous:after {
  content: '\E314';
  font-family: 'Material Icons';
}

.react-datepicker__navigation--next {
  right: 10px;
}

.react-datepicker__navigation--next:after {
  content: '\E315';
  font-family: 'Material Icons';
}

.react-datepicker__month-container {
  float: left;
}

.react-datepicker__month {
  margin: 0.4rem;
  text-align: center;
}

.react-datepicker__time-container {
  float: right;
  border-left: 1px solid #c7cdcf;
}

.react-datepicker__time-container--with-today-button {
  display: inline;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  position: absolute;
  right: -72px;
  top: 0;
}

.react-datepicker__time-container .react-datepicker__time {
  position: relative;
  background: white;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  width: 70px;
  overflow-x: hidden;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
  list-style: none;
  margin: 0;
  height: calc(195px + (2.33rem / 2));
  overflow-y: scroll;
  padding-right: 30px;
  width: 100%;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  padding: 5px 10px;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  cursor: pointer;
  background-color: #fff;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: #fb0f3b;
  color: white;
  font-weight: bold;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
  background-color: #fb0f3b;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled {
  color: #ccc;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover {
  cursor: default;
  background-color: transparent;
}

.react-datepicker__week-number {
  color: #ccc;
  display: inline-block;
  width: 2.33rem;
  line-height: 2.33rem;
  text-align: center;
  margin: 0.166rem;
}

.react-datepicker__week-number.react-datepicker__week-number--clickable {
  cursor: pointer;
}

.react-datepicker__week-number.react-datepicker__week-number--clickable:hover {
  border-radius: 3px;
  background-color: #fff;
}

.react-datepicker__day-names {
  font-size: 0.66rem;
  border-top: 1px solid #c7cdcf;
  padding: 0.5rem 0 0 0;
}

.react-datepicker__day-names, .react-datepicker__week {
  white-space: nowrap;
}

.react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name {
  color: #424a50;
  display: inline-block;
  width: 2.33rem;
  line-height: 2.33rem;
  text-align: center;
  margin: 0.166rem;
}

.react-datepicker__day {
  cursor: pointer;
}

.react-datepicker__day:hover {
  border-radius: 3px;
  background-color: #fff;
}

.react-datepicker__day--highlighted {
  border-radius: 3px;
  background-color: #fb0f3b;
  color: #fff;
}

.react-datepicker__day--highlighted:hover {
  background-color: #ed042f;
}

.react-datepicker__day--highlighted-custom-1 {
  color: magenta;
}

.react-datepicker__day--highlighted-custom-2 {
  color: green;
}

.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range {
  border-radius: 3px;
  background-color: #fb0f3b;
  color: #fff;
}

.react-datepicker__day--selected:hover, .react-datepicker__day--in-selecting-range:hover, .react-datepicker__day--in-range:hover {
  background-color: #ed042f;
}

.react-datepicker__day--keyboard-selected {
  border-radius: 50%;
  background-color: #fb0f3b;
  color: #fff;
}

.react-datepicker__day--keyboard-selected:hover {
  background-color: #ed042f;
}

.react-datepicker__day self--in-selecting-range:not(&--in-range) {
  background-color: rgba(251, 15, 59, .5);
}

.react-datepicker__month--selecting-range .react-datepicker__day self--in-range:not(&--in-selecting-range) {
  background-color: #fff;
  color: #424a50;
}

.react-datepicker__day--disabled {
  cursor: default;
  color: #ccc;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
}

.react-datepicker__input-container {
  position: relative;
  display: inline-block;
}

.react-datepicker__year-read-view, .react-datepicker__month-read-view {
  border: 1px solid transparent;
  border-radius: 3px;
}

.react-datepicker__year-read-view:hover, .react-datepicker__month-read-view:hover {
  cursor: pointer;
}

.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow, .react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow, .react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow {
  border-top-color: #b3b3b3;
}

.react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow {
  border-top-color: #ccc;
  float: right;
  margin-left: 20px;
  top: 8px;
  position: relative;
  border-width: 0.45rem;
}

.react-datepicker__year-dropdown, .react-datepicker__month-dropdown {
  background-color: #fff;
  position: absolute;
  width: 50%;
  left: 25%;
  top: 30px;
  z-index: 1;
  text-align: center;
  border-radius: 3px;
  border: 1px solid #c7cdcf;
}

.react-datepicker__year-dropdown:hover, .react-datepicker__month-dropdown:hover {
  cursor: pointer;
}

.react-datepicker__year-dropdown--scrollable, .react-datepicker__month-dropdown--scrollable {
  height: 150px;
  overflow-y: scroll;
}

.react-datepicker__year-option, .react-datepicker__month-option {
  line-height: 20px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.react-datepicker__year-option:first-of-type, .react-datepicker__month-option:first-of-type {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.react-datepicker__year-option:last-of-type, .react-datepicker__month-option:last-of-type {
  -webkit-user-select: none;
  user-select: none;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

.react-datepicker__year-option:hover, .react-datepicker__month-option:hover {
  background-color: #ccc;
}

.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming, .react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming {
  border-bottom-color: #b3b3b3;
}

.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous, .react-datepicker__month-option:hover .react-datepicker__navigation--years-previous {
  border-top-color: #b3b3b3;
}

.react-datepicker__year-option--selected, .react-datepicker__month-option--selected {
  position: absolute;
  left: 15px;
}

.react-datepicker__close-icon {
  background-color: transparent;
  border: 0;
  cursor: pointer;
  display: inline-block;
  height: 0;
  outline: 0;
  padding: 0;
  vertical-align: middle;
}

.react-datepicker__close-icon::after {
  background-color: #fb0f3b;
  border-radius: 50%;
  bottom: 0;
  box-sizing: border-box;
  color: #fff;
  content: "\00d7";
  cursor: pointer;
  font-size: 12px;
  height: 16px;
  width: 16px;
  line-height: 1;
  margin: -8px auto 0;
  padding: 2px;
  position: absolute;
  right: 7px;
  text-align: center;
  top: 50%;
}

.react-datepicker__today-button {
  background: #fff;
  border-top: 1px solid #c7cdcf;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left;
}

.react-datepicker__portal {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, .8);
  left: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 2147483647;
}

.react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__time-name {
  width: 3rem;
  line-height: 3rem;
}

@media (max-width: 400px), (max-height: 550px) {
  .react-datepicker__portal .react-datepicker__day-name, .react-datepicker__portal .react-datepicker__day, .react-datepicker__portal .react-datepicker__time-name {
    width: 2rem;
    line-height: 2rem;
  }
}

.react-datepicker__portal .react-datepicker__current-month, .react-datepicker__portal .react-datepicker-time__header {
  font-size: 1.25rem;
}

.react-datepicker__portal .react-datepicker__navigation {
  border: 0.81rem solid transparent;
  top: 3px;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle, .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle, .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow {
  margin-left: 0px;
  position: absolute;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle, .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle, .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow, .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle::before, .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle::before, .react-datepicker__year-read-view--down-arrow::before, .react-datepicker__month-read-view--down-arrow::before {
  box-sizing: content-box;
  position: absolute;
  border: 0px solid transparent;
  height: 0;
  width: 1px;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle::before, .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle::before, .react-datepicker__year-read-view--down-arrow::before, .react-datepicker__month-read-view--down-arrow::before {
  content: "";
  z-index: -1;
  border-width: 0px;
  left: 0px;
  border-bottom-color: #c7cdcf;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
  top: 0;
  margin-top: 0px;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle, .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle::before {
  border-top: none;
  border-bottom-color: #fff;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle::before {
  top: -1px;
  border-bottom-color: #c7cdcf;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle, .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow {
  bottom: 0;
  margin-bottom: 0px;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle, .react-datepicker__year-read-view--down-arrow, .react-datepicker__month-read-view--down-arrow, .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle::before, .react-datepicker__year-read-view--down-arrow::before, .react-datepicker__month-read-view--down-arrow::before {
  border-bottom: none;
  border-top-color: #fff;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle::before, .react-datepicker__year-read-view--down-arrow::before, .react-datepicker__month-read-view--down-arrow::before {
  bottom: -1px;
  border-top-color: #c7cdcf;
}

@keyframes __buttonSpinner {
  0% {
    transform: rotate(0deg);
  }

  30% {
    transform: rotate(200deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.cs-button {
  height: 40px;
  text-align: center;
  border-radius: 3px;
  min-width: 137px;
  border: 1px solid transparent;
  background: linear-gradient(180deg, #fb0f3b 0%, #e30a33 97.83%);
  padding: 0.25rem 1rem;
  position: relative;
  cursor: pointer;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
}

.cs-button.link.__tag {
  border-radius: 12px;
  height: auto;
  padding: 0.25rem 0.75rem;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  font-size: 0.8em;
}

.cs-button.link.__tag > * {
  height: 14px;
}

.cs-button.link.__tag .material-icons {
  font-size: 14px;
}

.cs-button.link.__tag .material-icons.__tag-icon-left {
  margin-right: 0.5em;
}

.cs-button.link.__tag .material-icons.__tag-icon-right {
  margin-left: 0.5em;
}

.cs-button.link.__tag.__tag_white {
  border: 1px solid #c7cdcf;
  background: linear-gradient(0deg, #f7f7f7 0%, #fff 100%);
}

.cs-button.link.__tag.__tag_white .material-icons {
  color: #92999f;
}

.cs-button.link.__tag.__tag_white:hover {
  background: linear-gradient(0deg, #e5e5e5 0%, #f7f7f7 100%);
}

.cs-button.link.__tag.__tag_white:focus {
  background: linear-gradient(0deg, #d9d9d9 0%, #f3f3f3 100%);
}

.cs-button.link.__tag.__tag_red {
  background: linear-gradient(180deg, #fb0f3b 0%, #e90f38 100%);
  color: white;
}

.cs-button.link.__tag.__tag_red .material-icons {
  color: white;
}

.cs-button.link.__tag:hover {
  background: linear-gradient(180deg, #ff4063 0%, #e42f50 100%);
}

.cs-button.link.__tag:focus {
  background: linear-gradient(180deg, #e91c42 0%, #d02242 100%);
}

.cs-button.tooltip:hover:before {
  position: absolute;
  top: 100%;
  height: 1rem;
  left: 50%;
  width: 1rem;
  background: black;
  content: '';
  transform: translateX(-50%) rotate(45deg);
}

.cs-button.tooltip:hover:after {
  white-space: nowrap;
  content: attr(data-tooltip);
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: black;
  border-radius: 3px;
  color: white;
  font-weight: bold;
  padding: 0.66rem 0.75rem;
  font-family: 'Roboto';
  font-size: 0.75rem;
  margin-top: 0.25rem;
  z-index: 100;
}

.cs-button.small {
  height: 34px;
  min-width: 0px;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.cs-button .__button_icon_small {
  font-size: 16px;
}

.cs-button:hover {
  background: linear-gradient(180deg, #ff4063 0%, #e42f50 100%);
}

.cs-button:focus {
  outline: none;
  background: linear-gradient(180deg, #e91c42 0%, #d02242 100%);
}

.cs-button.pending {
  font-size: 0px;
}

.cs-button.pending::after {
  content: '';
  display: inline-block;
  font-size: 14px;
  height: 20px;
  width: 20px;
  border: 2px solid transparent;
  border-right-color: white;
  border-top-color: white;
  border-radius: 50%;
  animation: __buttonSpinner 0.8s linear infinite;
}

.cs-button:disabled, .cs-button.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  pointer-events: auto;
}

.cs-button.white {
  background: linear-gradient(0deg, #f7f7f7 0%, #fff 100%);
  color: #424a50;
  border: 1px solid #c7cdcf;
}

.cs-button.white.pending::after {
  border-right-color: #424a50;
  border-top-color: #424a50;
}

.cs-button.white:hover {
  background: linear-gradient(0deg, #e5e5e5 0%, #f7f7f7 100%);
}

.cs-button.white:focus {
  background: linear-gradient(0deg, #d9d9d9 0%, #f3f3f3 100%);
}

.cs-button.outline {
  background: linear-gradient(180deg, #fff 0%, #fef2f5 100%);
  color: #fb0f3b;
  border-color: #fb0f3b;
}

.cs-button.outline.pending::after {
  border-right-color: #fb0f3b;
  border-top-color: #fb0f3b;
}

.cs-button.outline:hover {
  background: linear-gradient(180deg, #fff8f9 0%, #ffdae1 100%);
}

.cs-button.outline:focus {
  background: linear-gradient(180deg, #fdebef 0%, #ffc1cc 100%);
}

.cs-button.link {
  background: none;
  min-width: 0px;
  box-shadow: none;
  border: none;
  padding: 0;
  color: #424a50;
}

.box-shadow {
  box-shadow: 0 0 9px 0 rgba(0, 0, 0, .13);
}

.border-red {
  border-color: #fb0f3b;
}

.border-light-red {
  border-color: #fc4164;
}

.border-dark-red {
  border-color: #da3856;
}

.border-white {
  border-color: #fff;
}

.border-purple {
  border-color: #7a6bbd;
}

.border-light-purple {
  border-color: #9787dc;
}

.border-dark-purple {
  border-color: #574a91;
}

.border-gray-1 {
  border-color: #383e43;
}

.border-gray-2 {
  border-color: #545c63;
}

.border-gray-3 {
  border-color: #92999f;
}

.border-gray-4 {
  border-color: #c7cdcf;
}

.border-gray-5 {
  border-color: #e0e3e4;
}

.border-gray-6 {
  border-color: #f9f9f9;
}

.border-transparent {
  border-color: transparent;
}

/* Basscss Border */

.border {
  border-style: solid;
  border-width: 1px;
}

.border-top {
  border-top-style: solid;
  border-top-width: 1px;
}

.border-right {
  border-right-style: solid;
  border-right-width: 1px;
}

.border-bottom {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.border-left {
  border-left-style: solid;
  border-left-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-width-2 {
  border-width: 2px;
}

.border-width-3 {
  border-width: 3px;
}

.border-width-4 {
  border-width: 4px;
}

.border-width-5 {
  border-width: 5px;
}

.border-none {
  border: 0;
}

.rounded {
  border-radius: 3px;
}

.circle {
  border-radius: 50%;
}

.rounded-top {
  border-radius: 3px 3px 0 0;
}

.rounded-right {
  border-radius: 0 3px 3px 0;
}

.rounded-bottom {
  border-radius: 0 0 3px 3px;
}

.rounded-left {
  border-radius: 3px 0 0 3px;
}

.not-rounded {
  border-radius: 0;
}

/* Basscss Background Colors */

.bg-red {
  background-color: #fb0f3b;
}

.bg-light-red {
  background-color: #fc4164;
}

.bg-dark-red {
  background-color: #da3856;
}

.bg-white {
  background-color: #fff;
}

.bg-purple {
  background-color: #7a6bbd;
}

.bg-light-purple {
  background-color: #9787dc;
}

.bg-dark-purple {
  background-color: #574a91;
}

.bg-gray-1 {
  background-color: #383e43;
}

.bg-gray-2 {
  background-color: #545c63;
}

.bg-gray-3 {
  background-color: #92999f;
}

.bg-gray-4 {
  background-color: #c7cdcf;
}

.bg-gray-5 {
  background-color: #e0e3e4;
}

.bg-gray-6 {
  background-color: #f9f9f9;
}

.bg-green {
  background-color: #1aca8a;
}

/* Basscss Darken */

.bg-darken-1 {
  background-color: rgba(0, 0, 0, .0625);
}

.bg-darken-2 {
  background-color: rgba(0, 0, 0, .125);
}

.bg-darken-3 {
  background-color: rgba(0, 0, 0, .25);
}

.bg-darken-4 {
  background-color: rgba(0, 0, 0, .5);
}

.bg-darken-5 {
  background-color: rgba(0, 0, 0, .75);
}

/* Basscss Lighten */

.bg-lighten-1 {
  background-color: rgba(255, 255, 255, .0625);
}

.bg-lighten-2 {
  background-color: rgba(255, 255, 255, .125);
}

.bg-lighten-3 {
  background-color: rgba(255, 255, 255, .25);
}

.bg-lighten-4 {
  background-color: rgba(255, 255, 255, .5);
}

/* Basscss Background Images */

.bg-cover {
  background-size: cover;
}

.bg-contain {
  background-size: contain;
}

.bg-center {
  background-position: center;
}

.bg-top {
  background-position: top;
}

.bg-right {
  background-position: right;
}

.bg-bottom {
  background-position: bottom;
}

.bg-left {
  background-position: left;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.aspera-iframe-container {
  position: fixed !important;
}

.after\:h-3::after {
  content: var(--tw-content);
  height: 0.75rem;
}

.after\:h-4::after {
  content: var(--tw-content);
  height: 1rem;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-3::after {
  content: var(--tw-content);
  width: 0.75rem;
}

.after\:w-4::after {
  content: var(--tw-content);
  width: 1rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.after\:shadow-md::after {
  content: var(--tw-content);
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:duration-300::after {
  content: var(--tw-content);
  transition-duration: 300ms;
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-125:hover {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border-error-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 197 192 / var(--tw-border-opacity));
}

.hover\:border-error-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(210 54 40 / var(--tw-border-opacity));
}

.hover\:border-gray-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(116 119 142 / var(--tw-border-opacity));
}

.hover\:border-gray-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(87 90 113 / var(--tw-border-opacity));
}

.hover\:border-green-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity));
}

.hover\:border-info-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(184 222 255 / var(--tw-border-opacity));
}

.hover\:border-info-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(36 121 197 / var(--tw-border-opacity));
}

.hover\:border-neutral-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity));
}

.hover\:border-neutral-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 82 / var(--tw-border-opacity));
}

.hover\:border-primary-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(180 204 255 / var(--tw-border-opacity));
}

.hover\:border-primary-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(27 105 255 / var(--tw-border-opacity));
}

.hover\:border-primary-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(21 81 197 / var(--tw-border-opacity));
}

.hover\:border-red-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}

.hover\:border-success-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(171 236 199 / var(--tw-border-opacity));
}

.hover\:border-success-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(0 143 59 / var(--tw-border-opacity));
}

.hover\:border-warning-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 227 182 / var(--tw-border-opacity));
}

.hover\:border-warning-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(197 129 30 / var(--tw-border-opacity));
}

.hover\:bg-error-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 231 229 / var(--tw-bg-opacity));
}

.hover\:bg-error-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 197 192 / var(--tw-bg-opacity));
}

.hover\:bg-error-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(210 54 40 / var(--tw-bg-opacity));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 253 / var(--tw-bg-opacity));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(237 237 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(87 90 113 / var(--tw-bg-opacity));
}

.hover\:bg-info-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 243 255 / var(--tw-bg-opacity));
}

.hover\:bg-info-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(184 222 255 / var(--tw-bg-opacity));
}

.hover\:bg-info-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(36 121 197 / var(--tw-bg-opacity));
}

.hover\:bg-neutral-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.hover\:bg-neutral-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
}

.hover\:bg-primary-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(233 242 255 / var(--tw-bg-opacity));
}

.hover\:bg-primary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(180 204 255 / var(--tw-bg-opacity));
}

.hover\:bg-primary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 81 197 / var(--tw-bg-opacity));
}

.hover\:bg-success-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 255 240 / var(--tw-bg-opacity));
}

.hover\:bg-success-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(171 236 199 / var(--tw-bg-opacity));
}

.hover\:bg-success-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 143 59 / var(--tw-bg-opacity));
}

.hover\:bg-warning-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 245 229 / var(--tw-bg-opacity));
}

.hover\:bg-warning-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 227 182 / var(--tw-bg-opacity));
}

.hover\:bg-warning-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(197 129 30 / var(--tw-bg-opacity));
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-error-600:hover {
  --tw-text-opacity: 1;
  color: rgb(210 54 40 / var(--tw-text-opacity));
}

.hover\:text-info-600:hover {
  --tw-text-opacity: 1;
  color: rgb(36 121 197 / var(--tw-text-opacity));
}

.hover\:text-neutral-600:hover {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity));
}

.hover\:text-primary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(27 105 255 / var(--tw-text-opacity));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(21 81 197 / var(--tw-text-opacity));
}

.hover\:text-success-600:hover {
  --tw-text-opacity: 1;
  color: rgb(0 143 59 / var(--tw-text-opacity));
}

.hover\:text-warning-600:hover {
  --tw-text-opacity: 1;
  color: rgb(197 129 30 / var(--tw-text-opacity));
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.focus\:text-gray-600:focus {
  --tw-text-opacity: 1;
  color: rgb(87 90 113 / var(--tw-text-opacity));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.active\:border-error-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(164 41 30 / var(--tw-border-opacity));
}

.active\:border-info-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(24 85 140 / var(--tw-border-opacity));
}

.active\:border-neutral-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(64 64 64 / var(--tw-border-opacity));
}

.active\:border-primary-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(14 57 140 / var(--tw-border-opacity));
}

.active\:border-success-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(0 104 43 / var(--tw-border-opacity));
}

.active\:border-warning-700:active {
  --tw-border-opacity: 1;
  border-color: rgb(140 91 20 / var(--tw-border-opacity));
}

.active\:bg-error-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 197 192 / var(--tw-bg-opacity));
}

.active\:bg-error-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 131 122 / var(--tw-bg-opacity));
}

.active\:bg-error-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(164 41 30 / var(--tw-bg-opacity));
}

.active\:bg-gray-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.active\:bg-gray-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(58 61 84 / var(--tw-bg-opacity));
}

.active\:bg-info-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(184 222 255 / var(--tw-bg-opacity));
}

.active\:bg-info-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(139 200 255 / var(--tw-bg-opacity));
}

.active\:bg-info-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(24 85 140 / var(--tw-bg-opacity));
}

.active\:bg-neutral-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity));
}

.active\:bg-neutral-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity));
}

.active\:bg-primary-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(180 204 255 / var(--tw-bg-opacity));
}

.active\:bg-primary-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(129 171 255 / var(--tw-bg-opacity));
}

.active\:bg-primary-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(14 57 140 / var(--tw-bg-opacity));
}

.active\:bg-success-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(171 236 199 / var(--tw-bg-opacity));
}

.active\:bg-success-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(114 218 158 / var(--tw-bg-opacity));
}

.active\:bg-success-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(0 104 43 / var(--tw-bg-opacity));
}

.active\:bg-warning-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 227 182 / var(--tw-bg-opacity));
}

.active\:bg-warning-300:active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 207 134 / var(--tw-bg-opacity));
}

.active\:bg-warning-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(140 91 20 / var(--tw-bg-opacity));
}

.active\:text-error-700:active {
  --tw-text-opacity: 1;
  color: rgb(164 41 30 / var(--tw-text-opacity));
}

.active\:text-info-700:active {
  --tw-text-opacity: 1;
  color: rgb(24 85 140 / var(--tw-text-opacity));
}

.active\:text-neutral-700:active {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity));
}

.active\:text-primary-700:active {
  --tw-text-opacity: 1;
  color: rgb(14 57 140 / var(--tw-text-opacity));
}

.active\:text-success-700:active {
  --tw-text-opacity: 1;
  color: rgb(0 104 43 / var(--tw-text-opacity));
}

.active\:text-warning-700:active {
  --tw-text-opacity: 1;
  color: rgb(140 91 20 / var(--tw-text-opacity));
}

.group:hover .group-hover\:visible {
  visibility: visible;
}

.group:hover .group-hover\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(116 119 142 / var(--tw-border-opacity));
}

.group:hover .group-hover\:border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgb(21 81 197 / var(--tw-border-opacity));
}

.group:hover .group-hover\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 165 185 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.group:hover .group-hover\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(21 81 197 / var(--tw-text-opacity));
}

.group:active .group-active\:border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(87 90 113 / var(--tw-border-opacity));
}

.group:active .group-active\:bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.group:active .group-active\:bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(116 119 142 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:after\:translate-x-3::after {
  content: var(--tw-content);
  --tw-translate-x: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:translate-x-4::after {
  content: var(--tw-content);
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:translate-x-5::after {
  content: var(--tw-content);
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .peer:checked ~ .group-hover\:peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.group:active .peer:checked ~ .group-active\:peer-checked\:bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 90 113 / var(--tw-border-opacity));
}

.dark\:border-primary-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(0 9 26 / var(--tw-border-opacity));
}

.dark\:bg-black\/60:is(.dark *) {
  background-color: rgb(0 0 0 / 0.6);
}

.dark\:bg-black\/70:is(.dark *) {
  background-color: rgb(0 0 0 / 0.7);
}

.dark\:bg-gray-50\/\[\.08\]:is(.dark *) {
  background-color: rgb(249 250 251 / .08);
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 45 / var(--tw-bg-opacity));
}

.dark\:bg-primary-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(0 9 26 / var(--tw-bg-opacity));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(237 237 251 / var(--tw-text-opacity));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(165 165 185 / var(--tw-text-opacity));
}

.dark\:text-gray-600:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(87 90 113 / var(--tw-text-opacity));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(58 61 84 / var(--tw-bg-opacity));
}

.dark\:focus\:text-gray-200:focus:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(237 237 251 / var(--tw-text-opacity));
}

@media (min-width: 640px) {
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px) {
  .lg\:mr-8 {
    margin-right: 2rem;
  }

  .lg\:max-w-60 {
    max-width: 15rem;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-wrap {
    flex-wrap: wrap;
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

@media print {
  .print\:hidden {
    display: none;
  }
}


