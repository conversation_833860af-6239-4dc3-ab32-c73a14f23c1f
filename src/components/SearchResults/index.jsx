import React from 'react'
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import GroupedVideoGrid from 'components/GroupedVideoGrid'

const SearchResults = ({ search, organization }) => {
  const { t } = useTranslation()
  return (
    <GroupedVideoGrid
      organization={organization}
      pending={search.status === 'PENDING'}
      error={search.status === 'FAILED'}
      assets={search.results}
      headerClass={'csod-search-results-page-header'}
      header={
        <>
          <span style={{ opacity: "0.5" }} className="">
            {t("search.searchingFor")}:
          </span>{" "}
          <span className="csod-search-results-page-term">{search.term}</span>
        </>
      }
      emptyClass={'csod-search-no-results'}
      emptyTranslation={'search.empty'}/>
  );
}

const mapStateToProps = state => ({
  search: state.search,
  organization: state.organization
})

export default connect(mapStateToProps)(SearchResults)
