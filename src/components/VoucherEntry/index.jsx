import React, { useState } from "react"
import PropTypes from "prop-types"
import { useNavigate } from "react-router-dom"
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import { Button, Checkbox } from '@bitcine/cinesend-theme'

const VoucherEntry = ({ accent, invalidVoucherMessage, terms }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [code, setCode] = useState('')
  const [checkedTerms, setCheckedTerms] = useState(false)
  const handleSubmit = () => {
    if (submitDisabled()) {
      return
    }
    navigate(`/landing/${code}`)
  }
  const submitDisabled = () => {
    return !code || (terms.accept_required_upon_voucher_entry && !checkedTerms)
  }
  return (
    <>
      <h4 style={{ textAlign: 'center' }} className='mb2'>{t("home.ticketHolders")}</h4>
      <input
        className='cs-input col-12 mt1'
        placeholder={t("home.code")}
        value={code}
        onChange={(e) => setCode(e.target.value.toUpperCase())}
        onKeyPress={e => {
          if (e.key === 'Enter' && code) {
            handleSubmit()
          }
        }}/>
      {terms.accept_required_upon_voucher_entry && <div className='flex items-center mt1'>
        <Checkbox
          checked={checkedTerms}
          onChange={() => setCheckedTerms(!checkedTerms)}/>
        <small>Accept <span className='pointer bold' onClick={() => window.open(terms.url, '_blank')}>{terms.name}</span></small>
      </div>}
      <div className={`flex mt2 justify-end`}>
        <Button
          className={'cs-button'}
          style={{ background: accent }}
          small
          disabled={submitDisabled()}
          onClick={handleSubmit}>
          {t("home.submit")}
        </Button>
      </div>
      {/*invalidVoucherMessage && <small className='red bold mt2'>{invalidVoucherMessage}</small>*/}
    </>
  )
}

VoucherEntry.propTypes = {
  accent: PropTypes.string,
  invalidVoucherMessage: PropTypes.string,
  terms: PropTypes.object
}

const mapStateToProps = state => ({
  accent: state.organization.accent_color,
  invalidVoucherMessage: state.session.error,
  terms: state.organization.terms_and_conditions,
})

export default connect(mapStateToProps)(VoucherEntry)
