import React, { useRef, useEffect } from 'react'
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { useTranslation } from 'react-i18next'
import CSLogo from '/src/img/cinesend-gray.inline.svg'
import useIsMobile from 'hooks/use_is_mobile'
import { setFooterHeight } from 'api/dashboard'
import usePageWidth from 'hooks/use_page_width'

const FooterLogo = ({ footerLogoUrl, name, height }) => {
  return (
    <div>
      <img
        className={'inline object-contain'}
        src={footerLogoUrl}
        alt={name}
        style={{ 
          height: `${height}px`, 
          width: 'auto' 
        }}
      />
    </div>
  )
}

const PoweredBy = () => {
  const { t } = useTranslation()
  return (
    <div style={{ opacity: '65%' }}>
      <h5 className="csod-footer-powered-by">{t("footer.poweredBy")}</h5>
      <a href="https://cinesend.com">
        <img className={'inline'} src={CSLogo} width="100" alt="CineSend"/>
      </a>
    </div>
  )
}

const Terms = ({ terms }) => {
  return (
    <div className={`pointer bold csod-footer-terms-name`}
         onClick={() => window.open(terms.url, '_blank')}>{terms.name}</div>
  )
}

const Copyright = ({ name }) => {
  const { t } = useTranslation()
  return (<div>
    <small className="csod-footer-terms-copyright">{name && <span>© {name}. </span>}{t(
      "footer.allRightsReserved")}.</small>
  </div>)
}

const Footer = props => {
  const ref = useRef(null)
  useEffect(() => {
    if (!ref.current) {
      return
    }
    const resizeObserver = new ResizeObserver(() => {
      props.setFooterHeight(ref.current.offsetHeight)
    });
    resizeObserver.observe(ref.current);
    return () => resizeObserver.disconnect(); // clean up
  }, [])

  const { isMobile } = useIsMobile()
  const createHtml = (html) => ({
    __html: html || ''
  })
  const constrainWidth = !isMobile && props.constrainWidth
  const pageWidth = usePageWidth(constrainWidth)
  return !!props.organization.custom_footer_html_enabled ? (
    <div
      style={{ width: pageWidth }}
      ref={ref}
      className={`
        absolute bottom-0
        ${props.className + '-footer'}
        p-4 md:px-16 md:py-4
      `}
      dangerouslySetInnerHTML={createHtml(props.organization.custom_footer_html)}/>
  ) : (
    <div
      className={`w-full absolute bottom-0 csod-footer-container pb-4 ${props.className}-footer px-4 md:px-16 text-center md:${props.alignmentClass}`}
      style={{
        background: props.footerColor,
        color: props.footerTextColor
      }}
    >
      <div
        className={'mx-auto flex flex-col'}
        ref={ref}
        style={{
          width: pageWidth
        }}>
        {props.footerLogoUrl && <FooterLogo {...props}/>}
        {props.terms?.enabled && <Terms {...props} />}
        <Copyright {...props} />
        <PoweredBy {...props} />
      </div>
      <div className={'hidden md:text-left md:text-right md:text-center'}/>
    </div>
  )
}

const mapStateToProps = state => ({
  organization: state.organization,
  name: state.organization.name,
  terms: state.organization.terms_and_conditions,
  footerLogoUrl: state.organization.footer_logo_url,
  footerPositioning: state.organization.footer_positioning,
  height: state.organization.footer_height ? parseInt(state.organization.footer_height) : 0,
  footerColor: state.organization.footer_color,
  footerTextColor: state.organization.footer_text_color,
  constrainWidth: state.organization.constrain_page_width,
  alignmentClass: `text-${state.organization.footer_positioning}`
})

const mapDispatchToProps = dispatch => ({
  setFooterHeight: bindActionCreators(setFooterHeight, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Footer)
