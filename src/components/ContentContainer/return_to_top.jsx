import useIsMobile from "hooks/use_is_mobile"
import { connect } from "react-redux"
import { Icon } from '@bitcine/cinesend-theme'
import { useEffect, useState } from "react"

function ReturnToTop({ accentColor, footerColor, constrainWidth, outerRef }) {
  const { isMobile } = useIsMobile()
  const [hasReturnToTop, setHasReturnToTop] = useState(false)

  useEffect(() => {
    const onScroll = e => {
      const scrollTop = e.target.documentElement.scrollTop
      const pageHeight = outerRef.current.offsetHeight
      const windowHeight = window.innerHeight

      // If scrollTop is more than 20% down the page,
      // and the page height is larger than the window height, we can scroll to top.
      const scrollPercentage = scrollTop / pageHeight * 100
      setHasReturnToTop(scrollPercentage > 20 && pageHeight > windowHeight)
    };
    window.addEventListener("scroll", onScroll);

    return () => window.removeEventListener("scroll", onScroll)
  }, [outerRef])

  return (
    <div className={`
      csod-return-to-top-container flex items-center justify-center fixed bottom-0 left-0 right-0
      transition-all duration-1000 ${!hasReturnToTop ? 'translate-y-16 opacity-0' : ''}
    `}>
      <div className={`flex items-center justify-end pb-4 ${isMobile ? 'px-2' : 'px-4' }`} style={{
        width: constrainWidth ? `${constrainWidth}px` : '100%',
      }}>
        <div
          style={{
            backgroundColor: accentColor
          }}
          className={`flex csod-return-to-top-button items-center justify-center rounded-full cursor-pointer transition-all hover:-translate-y-1 w-8 h-8 z-10`}
          onClick={() => window.scrollTo({top: 0, behavior: 'smooth'})}>
          <Icon className='csod-return-to-top-arrow' icon={'keyboard_arrow_up'} style={{ color: footerColor ?? 'white' }} tooltip={{ text: 'Back to top', direction: 'left' }}/>
        </div>
      </div>
    </div>
  )
}

const mapStateToProps = state => ({
  accentColor: state.organization.accent_color,
  footerColor: state.organization.footer_color,
  constrainWidth: state.organization.constrain_page_width
})

export default connect(mapStateToProps)(ReturnToTop)
