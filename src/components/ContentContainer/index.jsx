import React, { useRef } from 'react'
import { connect } from "react-redux"
import HeaderBar from '/src/components/HeaderBar'
import SecondaryHeaderBar from 'components/HeaderBar/secondary_header_bar'
import HeroLogo from '/src/components/HeroLogo'
import Footer from '/src/components/Footer'
import LanguageDropdown from '/src/components/HeaderBar/language_dropdown'
import DefaultBackground from '../Background/default'
import styled from '@emotion/styled'
import ReturnToTop from './return_to_top'

const Outer = styled.div`
  position: relative;
  ${props => props.includePreviewBar ? 'top: 64px;' : ''}
  min-height: ${props => props.includePreviewBar ? 'calc(100vh - 64px);' : '100vh'};
  height: 100%;
  width: 100%;
`

const ConstrainedContent = styled.div`
  margin: 0 auto;
  padding-bottom: ${props => props.paddingBottom};
  ${props => props.constrainWidth && `
    max-width: ${props.constrainWidth}px;
  `}
  width: 100%;
  box-sizing: border-box;
`

const Inner = styled(ConstrainedContent)`
  position: relative;
`

const TopRight = styled.div`
  position: absolute;
  top: 0;
  right: 0;
`

const ContentContainer = ({
  className,
  paddingBottom,
  backgroundURL = null,
  backgroundClassName = '',
  includeHeader = false,
  includeSubheader = false,
  includeFooter = true,
  includeTranslator = false,
  includeHeroLogo = false,
  includePreviewBar = false,
  constrainWidth,
  children,
  headerColor,
  footerColor
}) => {
  const outerRef = useRef()
  return (
    <>
      {backgroundURL && <DefaultBackground className={backgroundClassName} url={backgroundURL}/>}
      <Outer className={className} includePreviewBar={includePreviewBar} ref={outerRef}>
        <div className='csod-content-inner-container'>
          {includeHeader && (
            <div className='csod-navbar-container' style={{ backgroundColor: headerColor, width: '100%' }}>
              <ConstrainedContent constrainWidth={constrainWidth}>
                <HeaderBar />
              </ConstrainedContent>
            </div>
          )}
          {includeSubheader && (
            <div className='csod-subnavbar-container' style={{ backgroundColor: headerColor, width: '100%' }}>
              <ConstrainedContent constrainWidth={constrainWidth}>
                <SecondaryHeaderBar />
              </ConstrainedContent>
            </div>
          )}
          <Inner className='csod-main-content' paddingBottom={includeFooter ? paddingBottom : 0} constrainWidth={constrainWidth}>
            {includeHeroLogo && <HeroLogo />}
            {includeTranslator && <TopRight className='p1'><LanguageDropdown/></TopRight>}
            {children}
          </Inner>
        </div>
        {includeFooter && (
          <Footer className={className} />
        )}
        <ReturnToTop outerRef={outerRef}/>
      </Outer>
    </>
  )
}

const mapStateToProps = state => ({
  paddingBottom: `${state.organization.footer_height ? parseInt(state.organization.footer_height) + 100 : (state.dashboard.footerHeight + 16)}px`,
  includePreviewBar: state.auth.admin && !state.preview.is_minimized,
  constrainWidth: state.organization.constrain_page_width,
  headerColor: state.organization.header_color,
})

export default connect(mapStateToProps)(ContentContainer)
