import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import VideoCarousel from '../VideoCarousel'
import AssetThumbnail from '../AssetThumbnail'

const TimedScreenings = ({ event, theme, parentVoucher = null }) => {
  return (
    <>
      <VideoCarousel
        theme={theme}>
        {event.timed_screenings.map(timedScreening =>
          <AssetThumbnail
            key={timedScreening._id}
            asset={timedScreening}
            parentVoucher={parentVoucher}/>
        )}
      </VideoCarousel>
    </>
  )
}

TimedScreenings.propTypes = {
  event: PropTypes.object
}

const mapStateToProps = state => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps)(TimedScreenings)
