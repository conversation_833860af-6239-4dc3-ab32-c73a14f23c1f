import React from 'react'
import ReactTooltip from 'react-tooltip'

const Icon = ({ id, icon, disabled, text, onClick = null, tooltipDirection = 'left', className = '' }) => 
  <>
    <ReactTooltip id={id} place={tooltipDirection} effect='solid' className='bold'/>
    <span
      data-tip={text} 
      data-for={id}
      className={`${className} ${disabled ? 'not-allowed' : onClick ? 'pointer' : ''}`}
      onClick={e => {
        if (typeof onClick === 'function' && !disabled) {
          e.stopPropagation()
          e.preventDefault()
          onClick(e)
        }
      }}>
      {icon}
    </span>
  </>

export default Icon