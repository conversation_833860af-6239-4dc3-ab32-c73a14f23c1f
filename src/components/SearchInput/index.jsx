import React, { useState, useRef, useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import { updateSearchTerm, getSearchResults, toggleFilters, showSearchInput } from '/src/api/search'
import styled from '@emotion/styled'
import useDebounce from '/src/hooks/use_debounce'
import { Icon } from "@bitcine/cinesend-theme"
import useIsMobile from 'hooks/use_is_mobile'
import { useLanguage } from 'contexts/language_context'

const InputContainer = styled.input`
  background: none;
  color: ${props => props.textColor};
  border: none;
  border-bottom: 1px solid ${props => props.textColor};
  padding: 5px 0;
  font-size: 1.2em;
  outline: none;
  -webkit-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  transition: all 250ms;
  width: ${props => props.show ? props.width : '0px'};
`

const ClearSearchButton = styled.button`
  height: 0;
  i {
    font-size: 1em;
    top: -12px;
    right: -5px;
  }
`;

const SearchInput = ({
  organization,
  search,
  displayInput,
  showSearchInput,
  updateSearchTerm,
  getSearchResults,
  accentColor,
  textColor,
  toggleFilters,
}) => {
  const [onblurTriggered, setOnblurTriggered] = useState(false)
  const [inputFieldLength, setInputFieldLength] = useState(230)
  const { t } = useTranslation()
  const { activeLanguage } = useLanguage()
  const input = useRef(null)
  const inputParent = useRef(null)
  const { isMobile } = useIsMobile()
  const showInput = () => {
    showSearchInput(true)
    input.current.focus()
  };
  const handleInput = () => {
    const value = input.current.value
    updateSearchTerm(value)
  }

  const debouncedSearchTerm = useDebounce(input.current?.value || '', 1500)

  useEffect(() => {
    setInputFieldLength(inputParent?.current?.offsetWidth - 28)
  }, [displayInput])
  useEffect(() => {
    if (debouncedSearchTerm.length > 0) {
      getSearchResults(debouncedSearchTerm, activeLanguage)
    }
  }, [getSearchResults, debouncedSearchTerm, activeLanguage])
  const onSearchClick = () => {
    if (displayInput && !onblurTriggered) {
      updateSearchTerm("")
      showSearchInput(false)
    }
    else if (!onblurTriggered || !isMobile) {
      showInput()
    }
    else {
      setOnblurTriggered(false)
    }
  }
  return (
    <>
      <div ref={inputParent} className={`flex items-center ${isMobile ? 'w-full' : ''} justify-end`}>
        <Icon
          icon={organization.search_icon}
          className="mx-2 csod-nav-icon csod-nav-icon-search"
          style={{ color: textColor }}
          onClick={() => onSearchClick()}
        />
        <div className='csod-nav-icon-search-placeholder hidden cursor-pointer' onClick={() => onSearchClick()}/>
        <div>
          <InputContainer
            className="csod-search-input"
            textColor={textColor}
            ref={input}
            placeholder={`${t("search.label")}...`}
            show={displayInput}
            width={isMobile ? `${inputFieldLength}px` : '230px'}
            onInput={handleInput} 
            onBlur={() => {
              if (!input.current.value.length) {
                setOnblurTriggered(true)
                showSearchInput(false)
              }
            }}
          />
          {search.term !== "" && (
            <ClearSearchButton
              className="cs-button link absolute"
              onClick={() => {
                updateSearchTerm("")
                input.current.value = ""
                input.current.focus()
              }}
            >
              <i
                className="absolute material-icons mr1"
                style={{ color: search.showFilters ? accentColor : textColor }}
              >
                cancel
              </i>
            </ClearSearchButton>
          )}
        </div>
      </div>
      <Icon
        style={{ color: search.showFilters ? accentColor : textColor }}
        icon="filter_list"
        className="mx-2 csod-nav-icon csod-nav-icon-filter"
        onClick={toggleFilters}
      />
    </>
  );
};

const mapStateToProps = state => ({
  search: state.search,
  displayInput: state.search.showSearchInput,
  organization: state.organization,
  accentColor: state.organization.accent_color,
  textColor: state.organization.header_text_color
})

const mapDispatchToProps = dispatch => ({
  updateSearchTerm: bindActionCreators(updateSearchTerm, dispatch),
  getSearchResults: bindActionCreators(getSearchResults, dispatch),
  toggleFilters: bindActionCreators(toggleFilters, dispatch),
  showSearchInput: bindActionCreators(showSearchInput, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(SearchInput)