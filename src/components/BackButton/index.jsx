import React from "react"
import { connect } from "react-redux"
import { MdKeyboardArrowLeft } from 'react-icons/md'
import styled from '@emotion/styled'
import { useNavigate } from "react-router-dom"

const BackIcon = styled.div`
  position: ${props => props.position};
  z-index: 5;
  top: ${props => props.top};
  left: ${props => props.left};
  color: ${props => props.useLightTheme ? 'black' : 'white'};
  fill: ${props => props.useLightTheme ? 'black' : 'white'};
  -webkit-transition: opacity 0.5s ease-in-out;
  -moz-transition: opacity 0.5s ease-in-out;
  -o-transition: opacity 0.5s ease-in-out;
  transition: opacity 0.5s ease-in-out;
  opacity: 80%;
  :hover {
    opacity: 100%;
  }
`

const BackButton = ({
  position = 'absolute',
  top = '56px',
  left = '8px',
  useLightTheme,
  className = '',
  title = '',
  forceDark = false,
  backPath = null,
  small = false
}) =>{
  const navigate = useNavigate()
  return (<BackIcon
    position={position}
    top={top}
    left={left}
    useLightTheme={forceDark ? false : useLightTheme}
    className={`csod-back-button ${className}`}>
    <MdKeyboardArrowLeft
      className='pointer csod-back-arrow'
      size={small ? '2em' : '4em'}
      title={title}
      onClick={() => {
        backPath ? navigate(backPath) : navigate(-1)
      }}/>
  </BackIcon>)}

const mapStateToProps = state => ({
  useLightTheme: state.organization.use_light_theme
})

export default connect(mapStateToProps)(BackButton)
