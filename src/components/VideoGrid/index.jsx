import React, { useEffect, useRef, useState } from 'react'
import { connect } from 'react-redux'
import AssetThumbnail from '/src/components/AssetThumbnail'
import { 
  calcItemWidth, 
  determineSlidesToShow
} from '../VideoCarousel/utils'
import styled from '@emotion/styled'
import useIsMobile from 'hooks/use_is_mobile'

const Item = styled.div`
  ${props => `
    width: ${props.itemWidth}px;
  `} 
  padding: 6px 12px 6px 0;
  display: inline-flex; 
`

function getDisplayStyle(category) {
  if (category.display_style === 'thumbnail') {
    return 'thumbnail'
  }
  else if (category.display_style === 'poster' || category.use_poster_images) {
    return 'poster'
  }
  else if (category.display_style === 'square') {
    return 'square'
    
  }
  return 'thumbnail'
}

const VideoGrid = ({ videos, parentSeries = null, parentVoucher = null, organization, category = {} }) => {
  const [gridWidth, setGridWidth] = useState(0)
  const [itemWidth, setItemWidth] = useState(0)
  const { isMobile } = useIsMobile()
  const gridRef = useRef(null)

  const resizeItemWidth = () => { 
    const displayStyle = getDisplayStyle(category)
    const size = isMobile ? 'small' : displayStyle === 'poster' ? 'large' : 'medium'
    const slidesToShow = determineSlidesToShow(displayStyle, size, organization.constrain_page_width, isMobile)
    const newItemWidth = calcItemWidth(gridWidth, slidesToShow, true);
    setItemWidth(newItemWidth)
  } 

  useEffect(() => {
    setItemParams() 
    window.addEventListener("resize", setItemParams)
    return () => {
      resizeItemWidth()
      window.removeEventListener("resize", setItemParams)
    }
  }, [resizeItemWidth])

  const setItemParams = () => {
    if (gridRef && gridRef.current) {
      setGridWidth(gridRef.current.offsetWidth)
    }
  }

  return (
    <div ref={gridRef} className={`flex flex-row flex-wrap gap-0.5 md:gap-1 csod-video-grid-container`}>
      {videos.map((video, index) =>
        <Item
          itemWidth={itemWidth}
          className={`csod-video-grid-item`}
          key={video._id}>
          <AssetThumbnail asset={video} category={category} parentSeries={parentSeries} parentVoucher={parentVoucher} fromGrid/>
        </Item>  
      )}
    </div>
  )
}

const mapStateToProps = state => ({
  organization: state.organization
})

export default connect(mapStateToProps)(VideoGrid)
