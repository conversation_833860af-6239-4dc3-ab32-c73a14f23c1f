import React, { useState } from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import AssetThumbnail from '/src/components/AssetThumbnail'
import { setCategorySlideIndex } from '/src/api/dashboard'
import VideoCarousel from '/src/components/VideoCarousel'
import VideoGrid from '/src/components/VideoGrid'
import { getButtonWidth, getSlidePadding } from '/src/components/VideoCarousel/utils'
import getTextColor from "helpers/get_text_color"
import useIsMobile from "hooks/use_is_mobile"
import { Icon } from "@bitcine/cinesend-theme"
import { getWindowWidth } from "components/VideoCarousel/utils"
import ViewMoreThumbnail from "./view_more_thumbnail"

const Container = styled.div`
  position: relative;
  ${props => props.fillWidth ? `
    width: 100vw;
    left: calc(-50vw + 50%);
  ` : null}
  color: ${props => props.textColor} !important;
  z-index: 2;
  ${props => props.backgroundColor ? `
    background-color: ${props.backgroundColor};
  ` : null}
  ${props => props.verticalPadding ? `
    padding-top: ${props.verticalPadding}px;
    padding-bottom: ${props.verticalPadding}px;
  ` : null}
  ${props => props.horizontalPadding ? `
    padding-left: ${props.horizontalPadding}px;
    padding-right: ${props.horizontalPadding}px;
  ` : null}
`

const InnerContainer = styled.div`
  ${props => props.constrainWidth ? `
    max-width: ${props.constrainWidth}px;
  ` : null}
  width: 100%;
  ${props => props.withPadding ? `
    padding-top: 8px;
    padding-bottom: 8px;
  ` : ''}
`

const HeaderText = styled.h4`
  color: ${props => props.textColor} !important;
`

const CategoryRow = ({
  viewMoreThumbnailEnabled,
  swimlaneAssetLimit,
  category,
  idx, 
  setCategorySlideIndex,
  foregroundColor,
  carouselY,
  headerY,
  useGridView = false,
  constrainWidth
}) => {
  const { isMobile } = useIsMobile()
  const navigate = useNavigate()
  const windowWidth = getWindowWidth()
  const constrained = windowWidth > constrainWidth
  const isConstrainedOrMobile = isMobile || (constrainWidth && constrained)

  const hideHeaderText = category.hide_header_text

  const isCarousel = category.display_style === 'carousel'
  const isSpotlight = category?.display_style === 'spotlight'
  const isSwimlane = !isCarousel && !isSpotlight

  const fillBackground = category.override_background_color && category.custom_background_color?.hex
  const overrideBackgroundColor = (fillBackground && !isCarousel) ? category.custom_background_color?.hex : null
  const overrideForegroundColor = isCarousel ? 'white' : fillBackground ? getTextColor(overrideBackgroundColor) : foregroundColor
  const verticalPadding = (!isCarousel && category.vertical_padding_px) ? category.vertical_padding_px : null

  let assets = category.videos
  const includeViewMoreThumbnail = viewMoreThumbnailEnabled && category.total_assets > swimlaneAssetLimit && isSwimlane

  if (includeViewMoreThumbnail) {
    assets = [...assets, { is_category_ender: true }]
  }

  return category.videos.length > 0 ? (
    <Container
      id={`csod-category-id-${idx + 1}`}
      className={`
        csod-category-${category._id}
        csod-category-container csod-category-container-row-${idx + 1}
        ${!fillBackground ? carouselY : null}
        flex justify-center relative
        ${isCarousel ? 'bg-black' : ''}
      `}
      textColor={overrideForegroundColor}
      fillWidth={fillBackground || isCarousel}
      backgroundColor={overrideBackgroundColor}
      verticalPadding={verticalPadding}
      horizontalPadding={!isConstrainedOrMobile && category.horizontal_padding_px ? category.horizontal_padding_px : null}>
      <InnerContainer
        withPadding={!isCarousel} 
        constrainWidth={constrainWidth}
        className='csod-category-inner-container'>
        {(!useGridView && !hideHeaderText) ?
          <HeaderText
            textColor={overrideForegroundColor}
            className={`group flex items-center pointer csod-category-title csod-category-title-${idx + 1} 
              ${category._id === 'purchased_category_id' ? 'csod-purchase-category-title' : ''}
              ${headerY} ${category.header_alignment === 'centered'
                ? 'justify-center' : category.header_alignment === 'right' ? 'justify-end' : null}
            `}
            style={{
              paddingLeft: (getButtonWidth()+getSlidePadding())+'px',
              paddingRight: (getButtonWidth()+getSlidePadding())+'px'
            }}
            onClick={() => navigate(`/categories/${category.friendly_url_alias || category._id}`)}>
            <div className='csod-category-title-text'>{category.title}</div>
            <Icon icon='chevron_right' className='group-hover:translate-x-2 transition-all csod-category-header-arrow'/>
          </HeaderText>
          : null}
        {useGridView ?
          <div className='p4'>
            <VideoGrid
              videos={category.videos}
              category={category}/>
          </div> :
          <VideoCarousel
            centerContent={category.header_alignment === 'centered'}
            displayStyle={category.display_style}
            displaySize={category.row_size}
            textColor={overrideForegroundColor}
            initialSlide={category.slideIdx}
            isCarousel={isCarousel}
            isSwimlane={isSwimlane}
            isSpotlight={isSpotlight}
            onSlideChange={slideIndex => setCategorySlideIndex(idx, slideIndex)}>
            {assets.map(asset =>
              asset.is_category_ender
                ? <ViewMoreThumbnail category={category}/> 
                : <AssetThumbnail key={asset._id} asset={asset} category={category}/>
            )}
          </VideoCarousel>}
      </InnerContainer>
    </Container>
  ) : null
}

CategoryRow.propTypes = {
  category: PropTypes.object,
  idx: PropTypes.number,
  organization: PropTypes.object,
  theme: PropTypes.string
}

const carouselSpacingMap = {
  'compact': '',
  'standard': 'my2',
  'spacious': 'my3'
}

const headerSpacingMap = {
  'compact': '',
  'standard': 'mt2',
  'spacious': 'mt3'
}

const mapStateToProps = state => ({
  organization: state.organization,
  backgroundColor: state.organization.background_color,
  foregroundColor: state.organization.foreground_color,
  headerY: headerSpacingMap[state.organization.category_spacing],
  carouselY: carouselSpacingMap[state.organization.category_spacing],
  constrainWidth: state.organization.constrain_page_width,
  viewMoreThumbnailEnabled: state.organization.use_view_more_thumbnail_in_swimlanes,
  swimlaneAssetLimit: state.organization.swimlane_asset_limit
})

const mapDispatchToProps = dispatch => ({
  setCategorySlideIndex: bindActionCreators(setCategorySlideIndex, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(CategoryRow)
