import styled from "@emotion/styled"
import { connect } from "react-redux"
import useIsMobile from "hooks/use_is_mobile"
import { t } from "i18next"
import { useNavigate } from "react-router-dom"

const Container = styled.div`
  width: 100%;
  transition: transform .3s ease-in-out; 
  color: ${props => props.textColor} !important;
  padding-top: ${props => props.useSquareImage ? '100%' : props.usePosterImage ? '150%' : '56.25%'};
  ${({ shouldGrow }) => shouldGrow && `
    &:hover, &:focus {
      transform: scale(1.02);
      backface-visibility: hidden;
      -webkit-font-smoothing: subpixel-antialiased;
    }
  `}
 
`

const ViewMoreThumbnail = ({ category, textColor }) => {
  const { isMobile } = useIsMobile()
  const navigate = useNavigate()

  // Defaults
  let usePosterImage = false
  let useSquareImage = false

  // Landscape images
  let landscape = ['thumbnail', 'carousel', 'spotlight']
  if (landscape.includes(category.display_style)) {
    usePosterImage = false
    useSquareImage = false
  }
  else if (category.use_poster_images || category.display_style === 'poster') {
    usePosterImage = true
  }
  else if (category.display_style === 'square') {
    useSquareImage = true
  }

  return (
    <Container
      textColor={textColor}
      onClick={() => navigate(`/categories/${category.friendly_url_alias || category._id}`)}
      className='csod-view-all-in-category-thumbnail relative cursor-pointer'
      usePosterImage={usePosterImage} 
      useSquareImage={useSquareImage}
      shouldGrow={!isMobile}>
      <div className='absolute inset-0 text-xl border-2 border-gray-600'>
        <div className='csod-view-all-text-container font-medium flex flex-col items-center justify-center h-full'>
          <div className='csod-view-all-text flex items-center justify-center'>
            {t("buttons.viewAllInCategory")}
          </div>
          {/* Commenting out the count for now, as it can be inaccurate */}
          {/* {category.total_assets ? <div className='csod-view-all-count text-base opacity-50'>
            ({category.total_assets})
          </div> : null} */}
        </div>
      </div>
    </Container>
  )
}


const mapStateToProps = state => ({
  textColor: state.organization.text_color
})

export default connect(mapStateToProps)(ViewMoreThumbnail)