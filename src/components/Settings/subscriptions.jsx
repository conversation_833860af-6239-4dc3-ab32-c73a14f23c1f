import React, { useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from 'react-redux'
import { useTranslation } from 'react-i18next'
import SubscribeButton from '../Buttons/subscribe'
import { ButtonDropdown, Status, Button, Icon } from '@bitcine/cinesend-theme'
import { createBillingPortal, getAvailableSubscriptions, updateSubscription } from 'redux/payments/api'
import { formatDate } from 'helpers/format_date'

const Card = ({ children, className }) => {
  return (
    <div className={`bg-gray-700 p-4 mb-2 flex justify-between rounded ${className}`}>
      {children}
    </div>
  )
}

const Subscriptions = ({ allSubscriptions, availableSubscriptions, status, getAvailableSubscriptions, updateSubscription, language, createBillingPortal, portalStatus }) => {
  const { t } = useTranslation()
  useEffect(() => {
    getAvailableSubscriptions()
  }, [])

  const ManagedSubscriptions = () => {
    return (
      <div>
        <div className='flex justify-between items-center pb-1'>
          <h3>{t("settings.managedSubscriptions")}</h3>
        </div>
        <p className='pb1 mb1'>{t("settings.managedSubscriptionsDescription")}</p>
        {allSubscriptions.filter(opt => opt.external).map(subscription =>
          <Card key={subscription._id}>
            <div>
              <div className='bold'>{subscription.subscriberType.name}</div>
              <small>Purchased and billed externally</small>
            </div>
          </Card>
        )}
      </div>
    )
  }

  const ActiveSubscriptions = () => {
    return (
      <div className='border-top border-gray-500 pt-4 mt-4'>
        <div className='flex justify-between items-center pb-1'>
          <h3>{t("settings.activeSubscriptions")}</h3>
          <Button disabled={portalStatus === "PENDING"} type='tertiary' icon={'open_in_new'} onClick={() => {
            createBillingPortal()
          }}>
            {t('subscription.paymentInfo')}
          </Button>
        </div>
        <p className='pb1 mb1'>{t("settings.activeSubscriptionsDescription")}</p>
        {allSubscriptions.filter(opt => !opt.external).map(subscription =>
          <Card key={subscription._id}>
            <div>
              <div className='bold'>
                {subscription.subscriberType.name} - {subscription.type && subscription.type === 'recurring'
                  ? 'Recurring'
                  : 'One Time Payment'}
              </div>
              {subscription.type === 'recurring' &&
                <small>
                  {`${subscription.cancel_at_period_end ? 'Cancels: ' : 'Renews: '} 
                  ${formatDate(subscription.renews_at, language, t)}`}
                </small>
              }
              <div>
                <small>Purchased: {formatDate(subscription.created_at, language, t)}</small>
              </div>
            </div>
            {subscription.type === 'recurring' &&
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: subscription.cancel_at_period_end ? "Enable recurring" : "Cancel recurring",
                      onClick: () => updateSubscription(subscription.stripe_subscription_id, {
                        cancel: !subscription.cancel_at_period_end
                      }),
                      icon: subscription.cancel_at_period_end ? "refresh" : "cancel"
                    },
                  ],
                }}
              />}
          </Card>)}
      </div>
    )
  }

  const AvailableSubscriptions = () => {
    return (
      <div className='border-top border-gray-500 pt-4 mt-4'>
        <div className='flex justify-between items-center pb-1'>
          <h3>{t("settings.availableSubscriptions")}</h3>
        </div>
        <p className='pb1 mb1'>{t("settings.availableSubscriptionsDescription")}</p>
        {(status === 'COMPLETE' && availableSubscriptions.length <= 0)
          ? <p>No subscriptions available</p>
          : availableSubscriptions.map(subscription => (
            <Card key={subscription._id}>
              <div className='w-full'>
                {subscription.thumbnail_url &&
                  <div className="aspect-video mb-2 pb-2 border-b border-gray-500">
                    <img
                      src={subscription.thumbnail_url}
                      alt={subscription.name}
                      className="w-full h-full object-cover rounded"
                    />
                  </div>}
                <div className='flex justify-between w-full'>
                  <div>
                    <div className='font-bold'>{subscription.name}</div>
                    <div>{subscription.price_string}</div>
                  </div>
                  <SubscribeButton subscription={subscription} />
                </div>
              </div>
            </Card>
          ))
        }
      </div>
    )
  }

  return (
    <Status pending={status === 'PENDING'}>
      <>
        <ManagedSubscriptions/>
        <ActiveSubscriptions/>
        <AvailableSubscriptions/>
      </>
    </Status>

  )
}

const mapStateToProps = state => ({
  useLightTheme: state.organization.use_light_theme,
  allSubscriptions: state.payments.allSubscriptions,
  availableSubscriptions: state.payments.availableSubscriptions,
  status: state.payments.availableSubscriptionsStatus,
  portalStatus: state.payments.portalSessionStatus,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  getAvailableSubscriptions: bindActionCreators(getAvailableSubscriptions, dispatch),
  updateSubscription: bindActionCreators(updateSubscription, dispatch),
  createBillingPortal: bindActionCreators(createBillingPortal, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Subscriptions)
