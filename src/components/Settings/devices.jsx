import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import dayjs from 'dayjs'
import { deleteSubscriberDevice } from '/src/api/auth'
import { MdDevices, MdPhoneIphone, MdLaptop, MdTv, MdTablet } from 'react-icons/md'
import { Button } from '@bitcine/cinesend-theme'

const Card = styled.div`
  max-width: 600px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const Line = styled.div`
  font-size: 10px;
  line-height: 10px;
`

const computers = ['OSX', 'WINDOWS', 'LINUX']
const phones = ['IOS', 'IPHONE', 'WINDOWSPHONE', 'ANDROID']
const tvs = ['ROKU', 'COM.CINESEND', 'APPLETV']
const tablets = []

const Icon = ({ userAgent }) => {

  let component = <MdDevices size={'2em'}/>

  userAgent = userAgent.replace(/ /g,'').toUpperCase()
  computers.forEach(device => {
    if (userAgent.includes(device)) {
      component = <MdLaptop size={'2em'}/>
    }
  })
  phones.forEach(device => {
    if (userAgent.includes(device)) {
      component = <MdPhoneIphone size={'2em'}/>
    }
  })
  tvs.forEach(device => {
    if (userAgent.includes(device)) {
      component = <MdTv size={'2em'}/>
    }
  })
  tablets.forEach(device => {
    if (userAgent.includes(device)) {
      component = <MdTablet size={'2em'}/>
    }
  })

  return component
}

const AccessDevices = ({user, overDeviceLimit, deviceLimit, accessDevices, deleteSubscriberDevice, organization}) => {
  const { t } = useTranslation()
  
  const tvDeviceIDs = accessDevices
    .filter(device => {
      const userAgent = device.browser.user_agent.replace(/ /g, '').toUpperCase()
      return tvs.some(tv => userAgent.includes(tv))
    })
    .map(device => device._id)

  return (
    <>
      <h3>{t('accessDevices.loggedInDevices')}</h3>

      {deviceLimit && <p className='border-bottom border-gray-5 pb1 mb1'>
        {t("accessDevices.deviceLimitMessage", { deviceLimit })}
      </p>}

      {overDeviceLimit && <p className={'red bold small mt1'}>{t('accessDevices.overLimit')}</p>}

      {accessDevices.map((accessDevice, i) =>
        <Card key={i} className={'mt1 p1 flex items-center justify-between'}>
          <div className='col-12 flex items-center'>
            <div className='mx1'>
              <Icon userAgent={accessDevice.browser.user_agent}/>
            </div>
            <div className='ml1'>
              <span className='bold'>{accessDevice.device}</span>
              <Line>{t('accessDevices.location')}: {accessDevice.location}</Line>
              <Line>{t('accessDevices.details')}: {accessDevice.connection_details}</Line>
              <Line>{t('accessDevices.lastAccess')}: {dayjs.utc(accessDevice.last_login_at)
                .local().format('MMM D, YYYY HH:mm')}</Line>
            </div>
          </div>
          <i className='pointer material-icons delete-icon' onClick={() => {
            if (window.confirm(
              t('accessDevices.confirmDelete'))) {
              deleteSubscriberDevice(user._id, accessDevice._id)
            }
          }
          }>delete_outline</i>
        </Card>
      )}
      <div className='flex justify-end pt-2'>
        {tvDeviceIDs.length > 0 && <Button
          className={'cs-button'}
          style={{ background: organization.accent_color }} small onClick={() => {
            if (window.confirm(
              t('accessDevices.confirmClearTV'))) {
              tvDeviceIDs.forEach(deviceID => deleteSubscriberDevice(user._id, deviceID))
            }
          }}>
          Clear TV Sessions
        </Button>}
      </div>
      {accessDevices.length === 0 && <div className="center">
        <MdDevices size='4em'/>
        <p>{t('accessDevices.newDevicesAdded')}</p>
      </div>}
   </>
  )
}

AccessDevices.propDefaults = {
  accessDevices: [],
}

AccessDevices.propTypes = {
  user: PropTypes.object,
  accessDevices: PropTypes.array,
  deleteSubscriberDevice: PropTypes.func,
}

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization,
  overDeviceLimit: state.auth.over_device_limit,
  accessDevices: state.auth.accessDevices,
  deviceLimit: state.organization.login_restriction_count
})

const mapDispatchToProps = dispatch => ({
  deleteSubscriberDevice: bindActionCreators(deleteSubscriberDevice, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(AccessDevices)
