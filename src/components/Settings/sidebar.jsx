import React from "react"
import { connect } from "react-redux"
import { NavLink } from "react-router-dom"
import { useTranslation } from "react-i18next"
import styled from "styled-components"
import { MdSubscriptions, MdDevices, MdAccountCircle, MdMonetizationOn } from "react-icons/md"
import { FiMonitor } from "react-icons/fi"
import tvApps from "/src/helpers/tv_apps"
import useIsMobile from "hooks/use_is_mobile"

const LinkContainer = styled.div`
  max-width: ${(props) => (props.isMobile ? "60px" : "220px")};
  span {
    color: ${(props) => props.organization.foreground_color};
  }
  svg {
    color: ${(props) => props.organization.foreground_color};
  }
`

const Icon = styled.div`
  width: 26px;
  height: 22px;
`

const Sidebar = ({ organization, tvAppsEnabled, useMonetization, publicMode, authenticated }) => {
  const { t } = useTranslation()
  const { isMobile } = useIsMobile()
  const links = [
    {
      to: "/settings/pair",
      code: 'pair',
      text: t("userDropdown.watchOnTV"),
      icon: <FiMonitor size="1.8em" />,
      show: authenticated && tvAppsEnabled,
    },
    {
      to: "/settings/devices",
      code: 'devices',
      text: t("userDropdown.manageAccessDevices"),
      icon: <MdDevices size="1.8em" />,
      show: authenticated,
    },
    {
      to: "/settings/subscriptions",
      code: 'subscriptions',
      text: t("settings.subscriptions"),
      icon: <MdSubscriptions size="1.8em" />,
      show: authenticated,
    },
    {
      to: "/settings/purchase-history",
      code: 'purchases',
      text: t("settings.purchases"),
      icon: <MdMonetizationOn size='1.8em' />,
      show: authenticated && useMonetization
    },
    {
      to: "/settings/account",
      code: 'account',
      text: t("settings.account"),
      icon: <MdAccountCircle size="1.8em" />,
      show: authenticated,
    },
    {
      to: "/login",
      code: 'login',
      text: t("settings.register"),
      icon: <MdAccountCircle size="1.8em" />,
      show: !authenticated && publicMode,
    },
    {
      to: "/register",
      code: 'register',
      text: t("settings.register"),
      icon: <MdAccountCircle size="1.8em" />,
      show: !authenticated && publicMode,
    }
  ].filter((opt) => opt.show)
  return (
    <LinkContainer organization={organization} isMobile={isMobile}>
      {links
        .filter((opt) => opt.show)
        .map((link, index) => (
          <NavLink
            key={index}
            to={link.to}
            className={({ isActive }) =>
              `block col-12 px2 py1 mb1 rounded csod-settings-sidebar-item csod-settings-sidebar-${link.code} ${isActive ? 'bold' : ''
              }`
            }
            style={({ isActive }) =>
              isActive
                ? { backgroundColor: organization.accent_color }
                : undefined
            }
          >
            <div className="flex items-center">
              <Icon className="flex items-center">{link.icon}</Icon>
              {!isMobile && <span className="ml1 nowrap">{link.text}</span>}
            </div>
          </NavLink>
        ))}
    </LinkContainer>
  )
}

const mapStateToProps = (state) => ({
  organization: state.organization,
  publicMode: state.organization.enable_public_playback ?? false,
  authenticated: state.auth.status === 'AUTHENTICATED',
  subscriberTypesCount: state.auth.user.subscriber_types?.length,
  useMonetization: !!state.organization.use_monetization,
  tvAppsEnabled:
    tvApps.filter((app) => state.organization.tv_apps[app.code + "_enabled"])
      .length > 0,
})

export default connect(mapStateToProps)(Sidebar)
