import React, { useState } from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { useTranslation } from 'react-i18next'
import styled from 'styled-components'
import { toast } from "react-toastify"
import { Button, Icon } from '@bitcine/cinesend-theme'
import { updateName, updatePassword } from '/src/api/auth'
import LanguageDropdown from '../HeaderBar/language_dropdown'
import { createBillingPortal } from 'redux/payments/api'

const InputContainer = styled.input`
  background: none;
  color: ${props => props.useLightTheme ? '#000' : '#fff'};
  border: none;
  border-bottom: 1px solid ${props => props.useLightTheme ? '#000' : '#fff'};
  padding: 5px 0;
  font-size: 1.2em;
  outline: none;
  width: 100%;
  ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: ${props => props.useLightTheme ? '#000' : '#fff'};
    opacity: 1; /* Firefox */
  }
  :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: ${props => props.useLightTheme ? '#000' : '#fff'};
  }
  ::-ms-input-placeholder { /* Microsoft Edge */
    color: ${props => props.useLightTheme ? '#000' : '#fff'};
  }
`

const Field = ({ children }) => <div className='border-bottom border-gray-5 pb1 mb1 flex items-start justify-between'>{children}</div>

const Account = ({ user, organization, useLightTheme, updateName, updatePassword, createBillingPortal }) => {
  const { t } = useTranslation()

  // Name hooks
  const [editName, setEditName] = useState(false)
  const [name, setName] = useState(user.name)

  // Password hooks
  const [editPassword, setEditPassword] = useState(false)
  const [oldPassword, setOldPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [passwordError, setPasswordError] = useState(false)
  return (
    <div>
      <h3>{t("settings.account")}</h3>
      <p className='border-bottom border-gray-5 pb1 mb1'>{t("settings.accountDescription")}</p>
      <Field>
        <div className='flex-auto'>
          <div className='bold'>{t("settings.name")}</div>
          {editName ?
            <InputContainer
              useLightTheme={useLightTheme}
              value={name}
              onChange={e => setName(e.target.value)}
              onBlur={() => {
                if (name !== user.name) {
                  updateName(name)
                }
                else {
                  setEditName(false)
                }
              }}/> :
            <div>{user.name}</div>}
        </div>
        <Icon
          icon='edit'
          className='pt-2'
          onClick={() => setEditName(!editName)}
          tooltip={{ text: 'Click to edit', direction: 'left' }}/>
      </Field>
      <Field>
        <div>
          <div className='bold'>{t("settings.accountLanguage")}</div>
          <div>{user.active_language_code ? t(`languages.${user.active_language_code}`) : ''}</div>
        </div>
        <LanguageDropdown
          className={'pt-2'}
          icon={'edit'}
          iconOnly
          textColor={organization.foreground_color}/>
      </Field>
      <Field>
        <div>
          <div className='bold'>{t("settings.email")}</div>
          {user.email}
        </div>
      </Field>
      <Field>
        <div className='flex-auto'>
          <div className='bold'>{t("settings.password")}</div>
          {editPassword ?
            <div>
              <div>
                <InputContainer
                  type='password'
                  placeholder={t("settings.oldPassword")}
                  useLightTheme={useLightTheme}
                  value={oldPassword}
                  onChange={e => setOldPassword(e.target.value)}/>
              </div>
              {passwordError && <div className='red mt1'>{t("settings.passwordError")}</div>}
              <div className='mt1'>
                <InputContainer
                  type='password'
                  placeholder={t("settings.newPassword")}
                  useLightTheme={useLightTheme}
                  value={newPassword}
                  onChange={e => setNewPassword(e.target.value)}/>
              </div>
              <div className='mt1'>
                <InputContainer
                  type='password'
                  placeholder={t("settings.confirmPassword")}
                  useLightTheme={useLightTheme}
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}/>
              </div>
              {newPassword && confirmPassword && newPassword !== confirmPassword && <div className='red mt1'>{t("settings.passwordMismatch")}</div>}
              <div className='mt1 flex justify-end'>
                <Button
                  style={{ background: organization.accent_color }}
                  small
                  className={'cs-button'}
                  disabled={!oldPassword || !newPassword || newPassword !== confirmPassword}
                  onClick={() => updatePassword(oldPassword, newPassword, res => {
                    if (res.success) {
                      toast.info(t("settings.passwordSuccess"))
                      setPasswordError(null)
                      setNewPassword('')
                      setOldPassword('')
                      setConfirmPassword('')
                      setEditPassword(false)
                    }
                    else {
                      setPasswordError(true)
                    }
                  })}>
                  {t("home.submit")}
                </Button>
              </div>
            </div> :
            <div>************</div>}
        </div>
        <Icon
          icon='edit'
          className='pt-2'
          onClick={() => setEditPassword(!editPassword)}
          tooltip={{ text: 'Click to edit', direction: 'left' }}/>
      </Field>
    </div>
  )
}


const mapStateToProps = state => ({
  useLightTheme: state.organization.use_light_theme,
  organization: state.organization,
  user: state.auth.user,
  languages: state.organization.languages
})

const mapDispatchToProps = dispatch => ({
  updateName: bindActionCreators(updateName, dispatch),
  updatePassword: bindActionCreators(updatePassword, dispatch),
  createBillingPortal: bindActionCreators(createBillingPortal, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Account)
