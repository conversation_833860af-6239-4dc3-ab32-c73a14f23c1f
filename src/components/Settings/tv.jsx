import React, { useState } from "react"
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Status, Button } from '@bitcine/cinesend-theme'
import TVAppInstructions from '../TVAppInstructions'
import { pairCode } from '/src/api/auth'

const PairingCode = ({ organization, loading, success, pairCode, invalidPairingMessage }) => {
  const { t } = useTranslation()
  const navigate = useNavigate
  const [code, setCode] = useState('')
  return (
    <Status pending={loading}>
      <h3>{t("tv.watchOnMyTV")}</h3>
      <p className='border-bottom border-gray-5 pb1 mb1'>{t("tv.description")}</p>
      {success ?
        <>
          <h4>{t("tv.successfullyPaired")}</h4>
          <div className='center mt2'>
            <Button
              className={'cs-button'}
              style={{ background: organization.accent_color }} small onClick={() => navigate('/')}>
              Dashboard
            </Button>
          </div>
        </> :
        <>
          <TVAppInstructions organization={organization}/>
          <input
            className='cs-input col-12'
            placeholder={t("tv.enterPairingCode")}
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            onKeyPress={e => {
              if (e.key === 'Enter' && code) {
                pairCode(code)
              }
            }}/>
          <div className='flex justify-end mt2'>
            <Button
              className={'cs-button'}
              style={{ backgroundColor: organization.accent_color }}
              small
              disabled={!code}
              onClick={() => pairCode(code)}>
              {t("home.submit")}
            </Button>
          </div>
          {invalidPairingMessage && <small className='red bold mt2'>{invalidPairingMessage}</small>}
        </>
      }
    </Status>
  )
}

const mapStateToProps = state => ({
  organization: state.organization,
  loading: state.auth.pairingStatus === 'PENDING',
  success: state.auth.pairingStatus === 'SUCCESS',
  invalidPairingMessage: state.auth.pairingError
})

const mapDispatchToProps = dispatch => ({
  pairCode: bindActionCreators(pairCode, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(PairingCode)
