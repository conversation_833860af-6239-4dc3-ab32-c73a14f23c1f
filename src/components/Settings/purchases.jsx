import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { withTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import styled from '@emotion/styled'
import { Status } from "@bitcine/cinesend-theme"
import { HiOutlineReceiptRefund } from 'react-icons/hi'
import { MdOutlineLockClock, MdOutlinePlayCircleOutline, MdReceipt } from 'react-icons/md'
import { getPaymentHistory } from '/src/redux/payments/api'
import { useNavigate } from 'react-router-dom'

const Card = styled.div`
  max-width: 600px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const Line = styled.div`
  font-size: 10px;
  line-height: 14px;
`

const Link = styled.div`
  font-weight: bold;
  color: ${props => props.theme === 'light' ? '#000' : '#fff'};
  ${props => props.disabled ? '' : `
    :hover {
      color: ${props.color} !important;
    }
    cursor: pointer;
  `}
`

const Purchases = ({ t }) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const {
    purchaseHistory,
    processed,
    upcoming,
    status,
    theme,
    color,
    currencyCode,
    currencySymbol,
  } = useSelector(state => ({
    purchaseHistory: state.payments.history,
    processed: state.payments.processed,
    upcoming: state.payments.upcoming,
    status: state.payments.status,
    color: state.organization.accent_color,
    currencyCode: state.organization.currency,
    currencySymbol: state.organization.currency_symbol,
    theme: state.organization.use_light_theme ? 'light' : 'dark',
  }))

  useEffect(() => {
    dispatch(getPaymentHistory())
  }, [dispatch])

  return (
    <div>
      <h3>{t('settings.purchases')}</h3>
      {status === 'PENDING' && (
        <p className="border-bottom border-gray-5 pb1 mb1">{t('settings.invoicesLoading')}</p>
      )}
      <Status pending={status === "PENDING"}>
        <p className="border-bottom border-gray-5 pb1 mb1">Upcoming Payments</p>
        {status === 'COMPLETE' && upcoming.length === 0 && (
          <p className="border-bottom border-gray-5 pb1 mb1">{t('settings.noUpcomingInvoices')}</p>
        )}
        {upcoming.map((invoice, i) => (
          <Card key={i} className={'mt1 p1 col-12 flex items-center justify-between'}>
            <div>
              <div className='font-bold'>{invoice.invoice_number}</div>
              <Line>{`${currencySymbol}${invoice.amount_due} ${currencyCode}`}</Line>
              <Line>{t('purchases.renewsAt')}: {dayjs.utc(invoice.created).local().format('MMM D, YYYY HH:mm')}</Line>
              <Line>{t('purchases.status')}: {invoice.status}</Line>
            </div>
            <div className='pr2'>
              <MdOutlineLockClock size={'2em'} />
            </div>
          </Card>
        ))}
        
        <p className="border-bottom border-gray-5 pb1 mb1">Purchased Subscriptions</p>
        {status === 'COMPLETE' && processed.length === 0 && (
          <p className="border-bottom border-gray-5 pb1 mb1">{t('settings.subscriptionsNoneMade')}</p>
        )}
        {processed.map(invoice => (
          <Link
            key={invoice.id}
            theme={theme}
            color={color}
            disabled={!invoice.invoice_pdf}
            onClick={() => invoice.invoice_pdf && window.open(invoice.invoice_pdf, '_blank')}
          >
            <Card className={'mt1 p1 col-12 flex items-center justify-between'}>
              <div>
                <div className='font-bold'>{`Invoice #${invoice.invoice_number}`}</div>
                <Line>{`${currencySymbol}${invoice.amount_due} ${currencyCode}`}</Line>
                <Line>{t('purchases.purchasedAt')}: {dayjs.utc(invoice.created).local().format('MMM D, YYYY HH:mm')}</Line>
                <Line>{t('purchases.status')}: {invoice.status}</Line>
              </div>
              <div className='pr2'>
                {invoice.status === 'paid' ? <MdReceipt size={'2em'} /> : invoice.status === 'refunded' ? <HiOutlineReceiptRefund size={'2em'} /> : <MdOutlineLockClock size={'2em'} />}
              </div>
            </Card>
          </Link>
        ))}

        <p className="border-bottom border-gray-5 pb1 mb1">Asset Purchases</p>
        {status === 'COMPLETE' && purchaseHistory.length === 0 && (
          <p className="border-bottom border-gray-5 pb1 mb1">{t('settings.purchasesNoneMade')}</p>
        )}
        {purchaseHistory.map((item, i) => (
          <Link
            key={i}
            disabled={!item.is_playable}
            theme={theme}
            color={color}
            className={!item.is_playable ? 'muted' : ''}
            onClick={() => item.is_playable ? navigate(`/play?assetID=${item.asset_id}`) : navigate('/view/' + item.asset_id)}
          >
            <Card className={'mt1 p1 col-12 flex items-center justify-between'}>
              <div>
                <div className='font-bold'>{item.asset_title}</div>
                {item.price && <Line>{`${currencySymbol}${item.price} ${currencyCode}`}</Line>}
                <Line>{t('purchases.purchasedAt')}: {dayjs.utc(item.created_at).local().format('MMM D, YYYY HH:mm')}</Line>
                {item.refunded_at ? <Line>{t('purchases.refundedAt')}: {dayjs.utc(item.refunded_at).local().format('MMM D, YYYY HH:mm')}</Line> : item.is_expired ? <Line>{t('purchases.expiredAt')}: {dayjs.utc(item.expires_at).local().format('MMM D, YYYY HH:mm')}</Line> : item.expires_at ? <Line>{t('purchases.expiresAt')}: {dayjs.utc(item.expires_at).local().format('MMM D, YYYY HH:mm')}</Line> : <Line>{t('purchases.unplayed')}</Line>}
              </div>
              <div className='pr2'>
                {item.refunded_at ? <HiOutlineReceiptRefund size={'2em'} /> : item.is_expired ? <MdOutlineLockClock size={'2em'} /> : <MdOutlinePlayCircleOutline size={'2em'} />}
              </div>
            </Card>
          </Link>
        ))}
      </Status>
    </div>
  )
}

export default withTranslation()(Purchases)
