import React from 'react'
import { Elements, ElementsConsumer } from '@stripe/react-stripe-js'
import { connect } from 'react-redux'
import CheckoutForm from './checkout_form'
import { loadStripe } from '@stripe/stripe-js'
import config from '/src/config'

const InjectedCheckoutForm = ({ payments, organization }) => {
  const stripePromise = loadStripe(process.env.VITE_STRIPE_PUBLIC_KEY)
  return (
    <>
    {payments.modal_active &&
        <Elements stripe={stripePromise}>
          <ElementsConsumer>
            {({ elements, stripe }) => (
              <CheckoutForm elements={elements} stripe={stripe} organization={organization}/>
            )}
          </ElementsConsumer>
        </Elements>
    }
    </>
  )
}

const mapStateToProps = state => ({
  organization: state.organization,
  payments: state.payments
})

export default connect(mapStateToProps)(InjectedCheckoutForm)
