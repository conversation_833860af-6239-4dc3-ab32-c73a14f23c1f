import React, { useState, useEffect } from 'react'
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js'
import Button from '../Buttons/button'
import { Modal } from '@bitcine/cinesend-theme'
import { useDispatch, useSelector } from 'react-redux'
import { postPayment, setPaymentMessage, toggleModal, resetState } from '/src/redux/payments/api'
import { FiCheckCircle } from 'react-icons/fi'
import { useTranslation } from 'react-i18next'
import { POST_PAYMENTS } from '/src/redux/payments/types'
import { Status } from '@bitcine/cinesend-theme'
import { getAvailableSubscriptions } from 'redux/payments/api'
import { getVideo, getDashboard, getFeaturedCategory } from 'api/dashboard'

const ELEMENT_OPTIONS = {
  style: {
    base: {
      fontSize: '14px',
      color: '#424A50',
      '::placeholder': {
        color: '#BBC2C4',
      },
      lineHeight: '36px',
    },
    invalid: {
      color: '#e71957',
    },
  },
};

const CheckoutForm = () => {
  const stripe = useStripe()
  const elements = useElements()
  const dispatch = useDispatch()
  const { t } = useTranslation()

  const [form, setForm] = useState({
    name: '',
    postal: '',
    cardNumber: false,
    expiry: false,
    cvc: false
  })

  const { payments, organization } = useSelector(state => ({
    payments: state.payments,
    organization: state.organization,
  }))
  const { item, payment_status, isSubscription } = payments
  const { language, activeSectionID, filterGenreID, filterTagIDs } = useSelector(state => state.dashboard)
  const complete = payment_status === 'COMPLETE'

  useEffect(() => {
    return () => dispatch(resetState())
  }, [dispatch])

  const isFormComplete = () => {
    const { name, postal, cardNumber, expiry, cvc } = form
    return (
      name.trim() !== '' &&
      postal.trim() !== '' &&
      cardNumber &&
      expiry &&
      cvc
    )
  }

  const handleSubmit = async (event) => {
    event.preventDefault()
    const { name, postal } = form
    if (!stripe || !elements) {
      return
    }

    const cardElement = elements.getElement(CardNumberElement)
    const result = await stripe.createToken(cardElement)

    dispatch(setPaymentMessage(POST_PAYMENTS.REQUEST))

    if (!result.token) {
      dispatch(setPaymentMessage(POST_PAYMENTS.REJECTED))
      return
    }

    await dispatch(postPayment({
      stripeToken: result.token.id,
      item_id: item._id,
      price_id: isSubscription ? item.price_id : item.asset_price.id,
      name,
      postal,
      type: isSubscription ? 'subscription' : null
    }))

    //update asset and refresh dashboard after a purchase
    dispatch(getAvailableSubscriptions())
    dispatch(getVideo(item._id, language))
    dispatch(getFeaturedCategory(language, activeSectionID, filterGenreID, filterTagIDs))
    dispatch(getDashboard(language, activeSectionID, filterGenreID, filterTagIDs))
  }

  return (
    <Modal open onClose={() => dispatch(toggleModal())} width={3}
      header={t('asset.checkout')}
      theme={organization.use_light_theme ? 'light' : 'dark'}>
      <Status pending={payments.status === 'PENDING'} error={item.is_geoblocked} errorMessage="This asset is geoblocked from your location.">
        <div className="flex">
          <div className="col-6 mr4">
            <div dangerouslySetInnerHTML={{ __html: item.short_summary || item.description }} />
            {item.thumbnail_url && <img className="cs-img col-12" src={item.thumbnail_url} alt="thumbnail" />}
            {(item.title || item.name) && <p>{item.title || item.name}</p>}
            </div>
          <div className="col-6">
            {!complete ? (
              <form onSubmit={handleSubmit}>
                <label htmlFor="name">{t('asset.fullName')}</label>
                <input
                  id="name"
                  className="cs-input col-12 mb1 csod-input"
                  required
                  placeholder={t('asset.fullName')}
                  value={form.name}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                />
                <label htmlFor="postal">{t('asset.postalCode')}</label>
                <input
                  id="postal"
                  className="cs-input col-12 mb1 csod-input"
                  required
                  placeholder={t('asset.postalCode')}
                  value={form.postal}
                  onChange={(e) => setForm({ ...form, postal: e.target.value })}
                />
                <label htmlFor="cardNumber">{t('asset.cardNumber')}</label>
                <CardNumberElement
                  className="cs-input col-12 mb1 pt1 csod-input"
                  id="cardNumber"
                  options={ELEMENT_OPTIONS}
                  onChange={(e) => setForm({ ...form, cardNumber: e.complete })}
                />
                <label htmlFor="expiry">{t('asset.cardExpiration')}</label>
                <CardExpiryElement
                  className="cs-input col-12 mb1 pt1 csod-input"
                  id="expiry"
                  options={ELEMENT_OPTIONS}
                  onChange={(e) => setForm({ ...form, expiry: e.complete })}
                />
                <label htmlFor="cvc">{t('asset.cardVerification')}</label>
                <CardCvcElement
                  className="cs-input col-12 mb1 pt1 csod-input"
                  id="cvc"
                  options={ELEMENT_OPTIONS}
                  onChange={(e) => setForm({ ...form, cvc: e.complete })}
                />
                {payments.payment_message && <p>{payments.payment_message}</p>}
                <div className="flex justify-end">
                  <Button
                    className="cs-button mr1 mt2"
                    style={{ background: organization.accent_color }}
                    type="submit"
                    disabled={!isFormComplete()}
                  >
                    {isSubscription ?
                      "Subscribe"
                      :
                      t('asset.payWithPrice', { price: payments.item.asset_price.price, currency: organization.currency })
                    }
                  </Button>
                  <Button
                    className="cs-button mt2"
                    style={{ background: organization.accent_color }}
                    onClick={() => dispatch(toggleModal())}
                  >
                    {t('asset.cancelOrder')}
                  </Button>
                </div>
              </form>
            ) : (
              <div className="flex flex-column justify-between" style={{ height: '100%' }}>
                <div className="flex items-center justify-center center pt3 h-full">
                  <div className="flex items-center flex-col space-y-4">
                    <FiCheckCircle size="6rem" />
                    <div>{t("purchases.success")}</div>
                  </div>
                </div>
                <div className="flex justify-center mt-4">
                  {isSubscription ? null : !item.hide_play_button && (
                    <Button className="cs-button" onClick={() => dispatch(toggleModal())} >
                      {t("buttons.close")}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </Status>
    </Modal>
  )
}

export default CheckoutForm
