import React from 'react'
import { useTranslation } from 'react-i18next'
import tvApps from '/src/helpers/tv_apps'

const TVAppInstructions = ({ organization }) => {
  const { t } = useTranslation()
  const enabledTVApps = tvApps.filter(app => organization.tv_apps[app.code + '_enabled'])
  if (!enabledTVApps.length) return null
  return (
    <div className='mt1'>
      <div className='my1'>
        <b>{t("tv.step")}1: </b>{t("tv.stepOneA")} <b>{organization.tv_apps.tv_app_name}</b> {t("tv.stepOneB")}
        <ul>
          {enabledTVApps.map((tvApp, index) =>
            <li key={index}>{tvApp.name}</li>
          )}
        </ul>
      </div>
      <div className='my1'>
        <b>{t("tv.step")}2: </b>{t("tv.stepTwo")}
      </div>
    </div>
  )
}

export default TVAppInstructions
