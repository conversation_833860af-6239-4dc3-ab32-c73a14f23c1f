import React, { useState } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { rateAsset } from '/src/api/dashboard'
import { Icon } from '@bitcine/cinesend-theme'
import styled from '@emotion/styled'

const STAR_COUNT = 5

const Wrapper = styled.div`
  scale: 1;
  opacity: 0.5;

  color: ${props => props.textColor}${props => props.faded ? '77' : ''};
  ${props => props.rated ? `
    scale: 1.4;
    opacity: 1
  ` : ''}
`

const Stars = ({ asset, rateAsset, voucherID, fromPlaylist, foregroundColor, accentColor }) => {
  const [hoverIdx, setHoverIdx] = useState(0)
  return (
    <span className='inline-flex items-center csod-star-ratings-container space-x-2'>
      {Array.from(Array(STAR_COUNT)).map((v, i) => {
        const hovered = hoverIdx > i
        const rated = asset.rating >= (i + 1)
        return (
          <Wrapper
            key={i}
            className={`csod-single-star-rating-container transition-all`}
            textColor={hovered ? accentColor :rated ? foregroundColor : null}
            rated={rated}
            onMouseEnter={() => setHoverIdx(i + 1)}
            onMouseLeave={() => setHoverIdx(0)}>
            <Icon
              icon='star_rate'
              onClick={() => {
                if ((i+1) === asset.rating) {
                  rateAsset(asset._id, 0, asset.type, voucherID, fromPlaylist)
                } else {
                  rateAsset(asset._id, i + 1, asset.type, voucherID, fromPlaylist)
                }
              }}
              className={`csod-single-star-rating-button text-lg`}/>
          </Wrapper>
        )}
      )}
    </span>
  )
}

Stars.propTypes = {
  asset: PropTypes.object,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  organization: state.organization,
  accentColor: state.organization.accent_color,
  foregroundColor: state.organization.foreground_color
})

const mapDispatchToProps = dispatch => ({
  rateAsset: bindActionCreators(rateAsset, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Stars)
