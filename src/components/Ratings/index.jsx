import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import styled from '@emotion/styled'
import { rateAsset } from '/src/api/dashboard'
import Stars from './components/stars'
import { useParams } from "react-router-dom"

const Button = styled.button`
  background: ${props => props.useLightTheme ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.2)'};
  height: 40px;
  width: 40px;
  border-radius: 20px;
  color: ${props => props.active ? props.accent + 'bf' : '#fff'};
  border: 1px solid ${props => props.active ? props.accent + 'bf' : '#fff'};
  opacity: ${props => props.active ? '1' : '0.5'};
  cursor: pointer;
  transition: all 250ms;
  outline: none;
  i {
    font-size: 1.5em;
    color: ${props => props.active ? props.accent + 'bf' : props.useLightTheme ? 'black' : 'white'}
  }
  &:hover {
    opacity: 1;
    transform: scale(1.05);
  }
`

const Ratings = ({ asset, organization, rateAsset, fromPlaylist = false }) => {
  const { voucherID } = useParams()
  return !organization.rating_type ? null : (
    <span className='csod-ratings-container'>
      {organization.rating_type === 'thumbs-up' && (
        <Button
          className={`cs-button csod-rating-button ${asset.rating === 'thumbs-up' ? 'csod-rating-button-active' : ''}`}
          useLightTheme={organization.use_light_theme}
          active={asset.rating === 'thumbs-up'}
          accent={organization.accent_color}
          onClick={() => {
            rateAsset(asset._id, asset.rating === 'thumbs-up' ? null : 'thumbs-up', asset.type, voucherID, fromPlaylist)
          }}>
          <i className='material-icons'>thumb_up</i>
        </Button>
      )}
      {organization.rating_type === 'thumbs-up-down' && (
        <>
          <Button
            className={`cs-button csod-rating-button ${asset.rating === 'thumbs-up' ? 'csod-rating-button-active' : ''}`}
            useLightTheme={organization.use_light_theme}
            active={asset.rating === 'thumbs-up'}
            accent={organization.accent_color}
            onClick={() => {
              rateAsset(asset._id, asset.rating === 'thumbs-up' ? null : 'thumbs-up', asset.type, voucherID, fromPlaylist)
            }}>
            <i className='material-icons'>thumb_up</i>
          </Button>
          <Button
            useLightTheme={organization.use_light_theme}
            active={asset.rating === 'thumbs-down'}
            className={`cs-button ml1 csod-rating-button ${asset.rating === 'thumbs-down' ? 'csod-rating-button-active' : ''}`}
            accent={organization.accent_color}
            onClick={() => {
              rateAsset(asset._id, asset.rating === 'thumbs-down' ? null : 'thumbs-down', asset.type, voucherID, fromPlaylist)
            }}>
            <i className='material-icons'>thumb_down</i>
          </Button>
        </>
      )}
      {organization.rating_type === 'stars' && (
        <Stars asset={asset} voucherID={voucherID} fromPlaylist={fromPlaylist}/>
      )}
    </span>
  )
}

Ratings.propTypes = {
  asset: PropTypes.object,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  organization: state.organization
})

const mapDispatchToProps = dispatch => ({
  rateAsset: bindActionCreators(rateAsset, dispatch)
})


export default connect(mapStateToProps, mapDispatchToProps)(Ratings)
