import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import VideoCarousel from '../VideoCarousel'
import AssetThumbnail from '../AssetThumbnail'

const BonusContent = ({ asset, theme, parentVoucher = null }) => {
  return (
    <>
      <VideoCarousel
        theme={theme}>
        {asset.bonus_content.map(video =>
          <AssetThumbnail
            key={video._id}
            asset={video}
            parentVoucher={parentVoucher}/>
        )}
      </VideoCarousel>
    </>
  )
}

BonusContent.propTypes = {
  asset: PropTypes.object
}

const mapStateToProps = state => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps)(BonusContent)
