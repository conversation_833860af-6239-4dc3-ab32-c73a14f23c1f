import React, {useEffect, useState} from 'react'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const DateTimeCounter = ({ asset }) => {
  const [dateIsValid, setDateIsValid] = useState(false)
  const [timeDiff, setTimeDiff] = useState(false)
  let timer = null
  useEffect(() => {
    if (!asset.live_stream_date) return
    setValid()
    setTime()
    tick()
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [])
  const setValid = () => setDateIsValid(dayjs(asset.live_stream_date).isAfter(dayjs()))
  const setTime = () => setTimeDiff(dayjs().to(dayjs(asset.live_stream_date)))
  const tick = () => {
    timer = setTimeout(() => {
      setTime()
      tick()
    }, 1000)
  }
  return {
    counterText: !!asset.live_stream_date && dateIsValid ? timeDiff : null,
    isLiveActive: dayjs(asset.live_stream_date).subtract(30, 'minutes').isBefore(dayjs()),
  }
}

export default DateTimeCounter
