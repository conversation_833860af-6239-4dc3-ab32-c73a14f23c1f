import React from "react"
import styled from '@emotion/styled'

const Image = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: ${props => props.active ? 1 : 0};
  -webkit-transition: opacity 1.5s ease-in-out;
  -moz-transition: opacity 1.5s ease-in-out;
  -o-transition: opacity 1.5s ease-in-out;
  transition: opacity 1.5s ease-in-out;
  background: url("${props => props.url}");
  background-repeat: no-repeat;
  background-size: 100% auto;
  -webkit-mask-image: linear-gradient(180deg,#000 70%,transparent);
`

const BackgroundImage = ({ active, url, dashboard, main }) => {
  console.log("DASHBOARD", dashboard)
  return (
    <Image 
      className={main ? 'csod-main-background-image' : dashboard ? 'csod-featured-video-background-image' : 'csod-video-details-background-image'}
      { ...{ active, url }}/>
  )
}

export default BackgroundImage
