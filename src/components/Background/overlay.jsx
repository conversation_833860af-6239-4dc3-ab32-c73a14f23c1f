import React from "react"
import styled from '@emotion/styled'
import { connect } from 'react-redux'

const getGradient = (type, backgroundColor, opacityPercent) => {
  if (backgroundColor == 'white') {
    backgroundColor = '#ffffff'
  }
  if (backgroundColor == 'black') {
    backgroundColor = '#000000'
  }

  let opacity = opacityPercent / 100
  if (isNaN(opacity)) {
    opacity = 1
  }
  let bgRgb = (hex) => {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    let rgb = {}
    try {
       rgb = {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      };
    } catch (e) {
      return hex;
    }
    return `rgb(${rgb.r},${rgb.g},${rgb.b},${opacity})`
  }

  return {
    'main': `linear-gradient(${bgRgb(backgroundColor)}, rgba(0,0,0,0), ${bgRgb(backgroundColor)})`,
    'dashboard':
      `linear-gradient(to right, ${bgRgb(backgroundColor)},  rgba(0,0,0,0) 50%, rgba(0,0,0,0) 80%, ${bgRgb(backgroundColor)} 100%),
      linear-gradient(${bgRgb(backgroundColor)} 0%, rgba(0,0,0,0) 30%, rgba(0,0,0,0) 70%, ${bgRgb(backgroundColor)} 100%)`,
    'video':
      `linear-gradient(to right, ${bgRgb(backgroundColor)},  rgba(0,0,0,0) 50%, rgba(0,0,0,0) 80%, ${bgRgb(backgroundColor)} 100%), 
      linear-gradient(${bgRgb(backgroundColor)} 0%, rgba(0,0,0,0) 30%, rgba(0,0,0,0) 70%, ${bgRgb(backgroundColor)} 100%)`
  }[type]
}

const Gradient = styled.div`
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-image: ${props => props.backgroundGradient};
    background-repeat: no-repeat;
    background-size: 100% auto;
    z-index: 1;
`

const Background = ({ dashboard, main, gradient, backgroundColor, opacityPercent }) => {
  let type = main ? 'main' : dashboard ? 'dashboard' : 'video'
  gradient = getGradient(type, backgroundColor, opacityPercent)
  return (
    <Gradient
      className={main ? 'csod-main-overlay' : dashboard ? 'csod-featured-video-overlay' : 'csod-video-details-overlay'}
      backgroundGradient={gradient}/>
  )
}

const mapStateToProps = state => ({
  backgroundColor: state.organization.background_color,
  opacityPercent: state.organization.background_overlay_opacity
})

export default connect(mapStateToProps)(Background)
