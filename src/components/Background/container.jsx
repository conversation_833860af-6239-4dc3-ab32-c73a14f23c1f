import React from "react"
import styled from '@emotion/styled'
import { connect } from 'react-redux'

const Container = styled.div`
  position: absolute;
  width: ${props => !props.main && props.lightTheme ? '100%' : '100%'};
  ${props => !props.main && props.lightTheme ? 'right: 0;' : ''}
  height: 0;
  padding-top: 56.25%;
  z-index: -1;
  display: table;
`

const BackgroundContainer = ({ main, lightTheme, dashboard, className, children }) => 
  <Container { ...{ main, lightTheme, dashboard }} className={`csod-background-container ${className}`}>
    {children}
  </Container>

const mapStateToProps = state => ({
  lightTheme: state.organization.use_light_theme
})

export default connect(mapStateToProps)(BackgroundContainer)
