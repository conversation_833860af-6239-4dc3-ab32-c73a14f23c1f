import React from 'react'
import { connect } from "react-redux"
import Filters from '/src/components/Filters'
import SearchResults from '/src/components/SearchResults'

const IncludeSearchResults = ({ children, search }) =>
  <div className='csod-search-results-page-container'>
    {search.showFilters && <Filters/>}
    {search.showResultsPage ? (
      <SearchResults />
    ) : children}
  </div>

const mapStateToProps = state => ({
  search: state.search
})

export default connect(mapStateToProps)(IncludeSearchResults)
