import React, { useState } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import styled from "styled-components"
import { useTranslation } from "react-i18next"
import { Button, Status, Checkbox } from "@bitcine/cinesend-theme"
import { pinLogIn } from "/src/api/auth"
import useIsMobile from "hooks/use_is_mobile"

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  min-height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const PinLogin = ({
  accent,
  pinLogIn,
  invalidLoginMessage,
  terms,
  loading,
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { search } = useLocation()
  let urlParams = new URLSearchParams(search)

  const [pin, setPin] = useState(urlParams.get("pin"))
  const [checkedTerms, setCheckedTerms] = useState(false)
  const lang = urlParams.get("lang") || urlParams.get("language")
  const returnTo = urlParams.get("returnTo")
  const { isMobile } = useIsMobile()
  const handleLogin = () => {
    if (loginDisabled()) {
      return
    }
    pinLogIn(pin, lang, () => {
      if (typeof returnTo === "string") {
        setTimeout(() => {
          navigate(returnTo)
        }, 250)
      }
      else {
        navigate("/")
      }
    })
  }
  const loginDisabled = () => {
    return !pin || (terms.accept_required_upon_login && !checkedTerms)
  }
  return (
    <Card isMobile={isMobile} className={`${isMobile ? 'p3' : 'p4'} csod-pin-login-card box-shadow mx-auto`}>
      <Status pending={loading} height={165}>
        <h4 style={{ textAlign: "center" }} className="mb2 csod-pin-login-header">
          {t("home.pinLogin")}
        </h4>
        <input
          className="cs-input col-12 mt1 csod-pin-input"
          name="pin"
          type="integer"
          placeholder={t("home.pin")}
          value={pin}
          onChange={(e) => setPin(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === "Enter") {
              handleLogin()
            }
          }}
        />
        {terms.accept_required_upon_login && <div className='flex items-center mt1'>
          <Checkbox
            checked={checkedTerms}
            onChange={() => setCheckedTerms(!checkedTerms)}/>
          <small>Accept <span className='pointer bold' onClick={() => window.open(terms.url, '_blank')}>{terms.name}</span></small>
        </div>}
        <div className="flex justify-between items-center mt2">
          <Button
            style={{ background: accent }}
            className="cs-button accent csod-pin-submit-button nowrap w-full"
            small
            disabled={loginDisabled()}
            onClick={handleLogin}
          >
            {t("home.pinSubmit")}
          </Button>
        </div>
        {invalidLoginMessage && (
          <small className="red bold mt2">{invalidLoginMessage}</small>
        )}
      </Status>
    </Card>
  )
}

PinLogin.propTypes = {
  invalidLoginMessage: PropTypes.string,
  settings: PropTypes.object,
  loading: PropTypes.bool,
  terms: PropTypes.object
}

const mapStateToProps = (state) => ({
  loading: state.auth.pin_status === "LOGIN_PENDING",
  accent: state.organization.accent_color,
  invalidLoginMessage: state.auth.pin_error,
  settings: state.organization,
  terms: state.organization.terms_and_conditions,
})

const mapDispatchToProps = (dispatch) => ({
  pinLogIn: bindActionCreators(pinLogIn, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(PinLogin)
