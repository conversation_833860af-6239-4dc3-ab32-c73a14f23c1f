import React, { useEffect } from "react"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { bindActionCreators } from "redux"
import { ButtonDropdown, Icon } from "@bitcine/cinesend-theme/dist"
import { setFilterTagIDs } from "api/dashboard"
import useIsMobile from "hooks/use_is_mobile"
import updateSearchParams from "helpers/update_search_params"
import { useTranslation } from "react-i18next"

const TagFilter = ({ 
  tagFilter, 
  setFilterTagIDs,
  filterTagIDs = [],
  textColor, 
}) => {
  const { isMobile } = useIsMobile()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const selectedTag = tagFilter.tags.find(opt => filterTagIDs.includes(opt._id))
  useEffect(() => {
    // If there is no default filter or there is already a selected tag, back out
    if (!tagFilter.default_filter_tag_id || selectedTag) {
      return 
    }
    setFilterTagIDs([
      ...filterTagIDs.filter(tagID => !tagFilter.tag_ids.includes(tagID)),
      tagFilter.default_filter_tag_id
    ].join(","))
  }, [tagFilter])

  const handleTagClick = (tagID) => {
    const newTags = (selectedTag && selectedTag._id === tagID
      ? (tagFilter.include_clear_filter_tag_button ? filterTagIDs.filter(id => id !== tagID) : filterTagIDs)
      : [...filterTagIDs.filter(filterTagID => !tagFilter.tag_ids.includes(filterTagID)), tagID]).join(",")
    updateSearchParams(navigate, location, 'tagIDs', newTags)
    setFilterTagIDs(newTags)
  }

  const handleClearTagClick = () => {
    const newTags = filterTagIDs.filter(tagID => !tagFilter.tag_ids.includes(tagID)).join(",")
    updateSearchParams(navigate, location, 'tagIDs', newTags)
    setFilterTagIDs(newTags)
  }

  const tagLabel = selectedTag ? selectedTag.label : tagFilter.name

  return ( 
    <ButtonDropdown
      button={{
        component: <div style={{ color: textColor }} className={`
          csod-tag-filter-button csod-tag-filter-${tagFilter.name}
          ${isMobile ? 'mr-1' : 'mx-2'} border rounded-lg border-gray-300 px-4 py-1 flex items-center space-x-2 whitespace-nowrap text-[16px]`}>
          {tagLabel}
          <Icon icon='expand_more' className='text-gray-600 text-base'/>
        </div>
      }}
      dropdown={{
        style: {
          maxHeight: '75vh',
          overflowY: 'auto'
        },
        content: [
          ...tagFilter.tags.map((tag, index) => ({
            text: tag.label,
            icon: selectedTag && selectedTag._id === tag._id ? 'check_circle_outline' : null,
            breakAfter: tagFilter.include_clear_filter_tag_button && index === tagFilter.tags.length - 1,
            disabled: selectedTag && selectedTag._id === tag._id && !tagFilter.include_clear_filter_tag_button,
            onClick: () => handleTagClick(tag._id)
          })),
          {
            text: t('buttons.clearFilter'),
            onClick: () => handleClearTagClick(),
            hide: !tagFilter.include_clear_filter_tag_button
          }
        ].filter(opt => !opt.hide)
      }}/>
  )
}

const mapStateToProps = state => ({
  filterTagIDs: state.dashboard.filterTagIDs ? state.dashboard.filterTagIDs.split(",") : [],
  accentColor: state.organization.accent_color,
  themeColor: state.organization.theme_text_color,
  textColor: state.organization.header_text_color,
  authenticated: state.auth.status === 'AUTHENTICATED',
})

const mapDispatchToProps = dispatch => ({
  setFilterTagIDs: bindActionCreators(setFilterTagIDs, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(TagFilter)
