import React from "react"
import { connect } from "react-redux"
import CustomHeaderLinks from './custom_header_links'
import SectionLinks from './section_links'
import StandardHeaderLinks from './standard_header_links'

const Links = ({ authenticated, isSidebar = false, onVoucherPage }) =>
  <>
    {!isSidebar && !onVoucherPage && <SectionLinks/>}
    <CustomHeaderLinks isSidebar={isSidebar}/>
    {authenticated && <StandardHeaderLinks onVoucherPage={onVoucherPage} isSidebar={isSidebar}/>}
  </>

const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED'
})

export default connect(mapStateToProps)(Links)
