import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useNavigate } from "react-router-dom"
import styled from '@emotion/styled'
import { Link } from "react-router-dom"
import { HashLink } from "react-router-hash-link"

const ExternalLink = styled.div`
  cursor: pointer;
  font-weight: normal;
  color: ${props => props.textColor};
  :hover {
    color: ${props => props.accentColor};
  }
`

const Ctas = ({
  textColor,
  themeColor,
  accentColor,
  callToAction,
  authenticated,
  isSidebar = false
}) => {
  const navigate = useNavigate()
  return (
    <>
      {callToAction.enabled &&
        <>
          {callToAction.ctas.filter(cta => authenticated || !!cta.is_public).map(cta =>
            cta.url && cta.url.startsWith('#') ?
              <HashLink to={cta.url} key={cta._id}>
                <ExternalLink
                  textColor={isSidebar ? themeColor : textColor}
                  accentColor={accentColor}
                  className={`${isSidebar ? 'px-4' : 'mx-2'} csod-nav-link csod-nav-link-${cta.name} link`}>
                  <span>{cta.name}</span>
                </ExternalLink>
              </HashLink> :
            cta.type === 'link' ?
              <ExternalLink
                key={cta._id}
                textColor={isSidebar ? themeColor : textColor}
                accentColor={accentColor}
                className={`${isSidebar ? 'px-4' : 'mx-2'} csod-nav-link csod-nav-link-${cta.name} link`}
                onClick={() => {
                  if (cta.url.startsWith('#')) {
                    navigate(cta.url)
                  } else if (cta.opens_in_new_tab){
                    window.open(cta.url, '_blank')
                  } else {
                    window.location.href = cta.url
                  }
                }}>
                <span>{cta.name}</span>
              </ExternalLink> :
            cta.type === 'html' ?
              <Link to={`/cta/${cta._id}`} key={cta._id}>
                <ExternalLink
                  textColor={isSidebar ? themeColor : textColor}
                  accentColor={accentColor}
                  className={`${isSidebar ? 'px-4' : 'mx-2'} csod-nav-link csod-nav-link-${cta.name} link`}>
                  <span>{cta.name}</span>
                </ExternalLink>
              </Link> : null
          )}
        </>
      }
    </>
  )
}

Ctas.propTypes = {
  accentColor: PropTypes.string,
  textColor: PropTypes.string,
}

const mapStateToProps = state => ({
  callToAction: state.organization.call_to_action,
  accentColor: state.organization.accent_color,
  textColor: state.organization.header_text_color,
  themeColor: state.organization.theme_text_color,
  authenticated: state.auth.status === 'AUTHENTICATED'
})

export default connect(mapStateToProps)(Ctas)
