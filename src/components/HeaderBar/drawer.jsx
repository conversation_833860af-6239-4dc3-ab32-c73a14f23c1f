import React, { useState } from "react"
import { connect } from "react-redux"
import { Icon } from '@bitcine/cinesend-theme'
import Sidebar from '../Sidebar'
import getTextColor from "helpers/get_text_color"

const Drawer = ({ iconColor, onVoucherPage }) => {
  const [sidebarOpen, showSidebar] = useState(false)
  return (
    <>
      <Icon
        icon='menu'
        onClick={() => showSidebar(true)}
        className='link csod-drawer-button ml-2'
        style={{ color: iconColor }}/>
      {sidebarOpen && <Sidebar onVoucherPage={onVoucherPage} onOutsideClick={() => showSidebar(false)}/>}
    </>
  )
}

const mapStateToProps = state => ({
  iconColor: state.organization.header_color ? getTextColor(state.organization.header_color) : state.organization.theme_text_color
})

export default connect(mapStateToProps)(Drawer)

