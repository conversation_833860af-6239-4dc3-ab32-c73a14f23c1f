import React, { useEffect } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import { logOut } from "/src/api/auth"
import styled from '@emotion/styled'
import tvApps from '/src/helpers/tv_apps'
import { updateSearchTerm } from '/src/api/search'
import MobileLanguageDropdown from "./mobile_language_dropdown"
import * as CookieConsent from 'vanilla-cookieconsent'

const Link = styled.div`
  cursor: pointer;
  font-weight: normal;
  color: ${props => props.themeColor};
  :hover {
    color: ${props => props.accentColor};
  }
`

const Controls = ({
  user,
  authenticated,
  publicMode,
  selfRegister,
  themeColor,
  accentColor,
  logOut,
  tvAppsEnabled,
  loginRestrictionsEnabled,
  useMonetization,
  showCookieConsent,
  updateSearchTerm,
  onVoucherPage
}) => {
  const { t } = useTranslation()
  const location = useLocation()
  const navigate = useNavigate()

  //Whenever the history is changed we want to clear the search input
  //The search results will no longer overlap the destination page
  useEffect(() => {
    updateSearchTerm("")
  }, [location])

  return (
    <>
      {user && tvAppsEnabled &&
        <Link
          themeColor={themeColor}
          accentColor={accentColor}
          onClick={() => navigate('/settings/pair')}
          className='pl2 link csod-nav-link csod-nav-link-watch-on-tv'>
          {t("userDropdown.watchOnTV")}
        </Link>}
      <MobileLanguageDropdown/>
      {authenticated && loginRestrictionsEnabled && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => navigate('/settings/devices')}
        className='pl2 link csod-nav-link csod-nav-link-devices'>
      {t('userDropdown.manageAccessDevices')}
      </Link>}
      {authenticated && useMonetization && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => navigate('/settings/purchase-history')}
        className='pl2 link csod-nav-link csod-nav-link-purchase-history'>
        {t('userDropdown.purchases')}
      </Link>}
      {authenticated && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => navigate('/settings/account')}
        className='pl2 link csod-nav-link csod-nav-link-account'>
        {t('userDropdown.account')}
        </Link>}
      {showCookieConsent && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        className='pl2 link csod-nav-link csod-nav-link-cookies'
        onClick={() => CookieConsent.show(true)}>
        {t('userDropdown.manageCookies')}
      </Link>}
      {!authenticated && publicMode && !onVoucherPage && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => navigate('/login')}
        className='pl2 link csod-nav-link csod-nav-link-login'>
        {t('userDropdown.logIn')}
        </Link>}
      {!authenticated && publicMode && selfRegister && !onVoucherPage && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => navigate('/register')}
        className='pl2 link csod-nav-link csod-nav-link-register'>
        {t('userDropdown.register')}
        </Link>}
      {user && authenticated && <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => logOut()}
        className='pl2 link csod-nav-link csod-nav-link-logout'>
        {t("userDropdown.logOut")}
      </Link>}
    </>
  )
}

Controls.propTypes = {
  user: PropTypes.object,
  logOut: PropTypes.func,
  color: PropTypes.string,
  pending: PropTypes.bool,
  theme: PropTypes.string,
  tvAppsEnabled: PropTypes.bool,
  loginRestrictionsEnabled: PropTypes.bool,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  user: state.auth.user,
  accentColor: state.organization.accent_color,
  themeColor: state.organization.theme_text_color,
  pending: state.auth.status === 'PENDING',
  tvAppsEnabled: tvApps.filter(app => state.organization.tv_apps[app.code + '_enabled']).length > 0,
  loginRestrictionsEnabled: state.organization.login_restrictions_enabled,
  organization: state.organization,
  useMonetization: state.organization.use_monetization,
  authenticated: state.auth.status === 'AUTHENTICATED',
  publicMode: state.organization.enable_public_browsing ?? false,
  selfRegister: state.organization.allow_account_registrations ?? false,
  showCookieConsent: state.organization.show_cookie_consent
})

const mapDispatchToProps = dispatch => ({
  logOut: bindActionCreators(logOut, dispatch),
  updateSearchTerm: bindActionCreators(updateSearchTerm, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Controls)
