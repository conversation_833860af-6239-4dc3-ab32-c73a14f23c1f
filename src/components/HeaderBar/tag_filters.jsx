import React from "react"
import { connect } from "react-redux"
import TagFilter from './tag_filter'

const TagFilters = ({ 
  tagFilters
}) => {
  if (tagFilters.length === 0) {
    return null
  }
  return (
    <>
      {tagFilters.map((tagFilter, index) => <TagFilter key={index} tagFilter={tagFilter}/>)}
    </>
  )
}

const mapStateToProps = state => ({
  tagFilters: state.organization.tag_filters
})

export default connect(mapStateToProps)(TagFilters)
