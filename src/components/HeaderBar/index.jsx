import React, { useEffect, useState } from "react"
import { connect } from "react-redux"
import styled from '@emotion/styled'
import SearchInput from '../SearchInput'
import Logo from './logo'
import Links from './links'
import Controls from './controls_desktop'
import LanguageDropdown from './language_dropdown'
import Drawer from './drawer'
import TagFilters from "./tag_filters"
import useIsMobile from "hooks/use_is_mobile"
import LoginButton from "./login_button"
import { useParams } from "react-router-dom"

const Bar = styled.div`
  position: relative;
  top: 0;
  height: ${props => props.headerHeight}px;
  width: 100%;
  ${props => props.headerColor && `background-color: ${props.headerColor};`}
  ${props => props.textColor && `color: ${props.textColor};`}
`

const HeaderBar = ({
  publicMode,
  authenticated,
  rightJustify,
  headerHeight,
  headerColor,
  textColor,
  organization,
  searching,
  theme
}) => {
  const [showLogo, setShowLogo] = useState(true)
  useEffect(() => {
    if (!isMobile) {
      return
    }
    if (searching) {
      setShowLogo(false)
    }
    else {  
      setTimeout(() => {
        setShowLogo(true)
      }, 500)
    }
  }, [searching])
  const { isMobile, isTablet } = useIsMobile()
  const { voucherID, videoID } = useParams()
  const onVoucherPage = voucherID || false
  const onLandingPage = videoID || onVoucherPage
  const showLeftLinks = !isMobile && !rightJustify
  const hideItems = searching && (isMobile || isTablet)
  return (
    <Bar
      headerHeight={headerHeight}
      className={`csod-navbar flex items-center z1 ${isMobile ? 'px2 justify-end' : 'px4 justify-between'}`}
      headerColor={headerColor}
      textColor={textColor}>
      {(showLogo || showLeftLinks) && <div className="flex items-center flex-auto" style={{ height: '100%' }}>
        <div style={{ height: '100%' }} className={`items-center py1 ${showLogo ? '' : '-translate-y-40'}`}>
          <Logo />
        </div>
        {showLeftLinks && <div className='ml-3 flex items-center'>
          <Links onVoucherPage={onVoucherPage}/>

        </div>}
      </div>}
      <div className={`flex items-center ${isMobile ? 'w-full' : ''} justify-end`}>
        {!isMobile && rightJustify && !hideItems && <Links/>}
        {!isMobile && !hideItems && !onLandingPage && <TagFilters/>}
        {!isMobile && !hideItems && <LanguageDropdown textColor={organization.header_text_color} className='mx-2'/>}
        {!authenticated && publicMode && !isMobile && !onVoucherPage && <LoginButton />}
        {(authenticated || publicMode || !onVoucherPage) && <>
          {!onVoucherPage && <SearchInput/>}
          {!isMobile && <Controls onVoucherPage={onVoucherPage} />}
        </>}
        {isMobile && <Drawer onVoucherPage={onVoucherPage} />}
      </div>
    </Bar>
  )
}

const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED',
  publicMode: state.organization.enable_public_browsing ?? false,
  rightJustify: state.organization.call_to_action.right_justify_header_links,
  headerHeight: state.organization.header_height,
  backgroundColor: state.organization.header_color,
  textColor: state.organization.header_text_color,
  organization: state.organization,
  searching: state.search.showSearchInput,
  theme: state.organization.use_light_theme ? 'light' : 'dark',
})

export default connect(mapStateToProps)(HeaderBar)
