import React, { useEffect } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { getAssetList } from '/src/api/dashboard'

const ExternalLink = styled.div`
  cursor: pointer;
  font-weight: normal;
  color: ${props => props.textColor};
  :hover {
    color: ${props => props.accentColor};
  }
`

const Links = ({
  textColor,
  accentColor,
  themeColor,
  listEnabled,
  mylist,
  getAssetList,
  isSidebar = false
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  useEffect(() => {
    if(listEnabled && mylist.status !== 'READY') {
      getAssetList()
    }
  }, [listEnabled, getAssetList])
  const list = mylist.items && mylist.items.length > 0 ? mylist.items : []
  const count = list.reduce((cur, group) => cur + group.list.length, 0)
  return (
    <>
      {listEnabled &&
        <ExternalLink
          textColor={isSidebar ? themeColor : textColor}
          accentColor={accentColor}
          onClick={() => navigate('/list')}
          className={`csod-nav-link link ${isSidebar ? 'px-4' : 'mx-2'}`}>
          {t("myList.label")}
          {count > 0 && ` (${count})`}
        </ExternalLink>
      }
    </>
  )
}

Links.propTypes = {
  accentColor: PropTypes.string,
  textColor: PropTypes.string,
  listEnabled: PropTypes.bool
}

const mapStateToProps = state => ({
  user: state.auth.user,
  accentColor: state.organization.accent_color,
  textColor: state.organization.header_text_color,
  themeColor: state.organization.theme_text_color,
  listEnabled: state.organization.lists_enabled,
  mylist: state.dashboard.mylist
})

const mapDispatchToProps = dispatch => ({
  getAssetList: bindActionCreators(getAssetList, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Links)
