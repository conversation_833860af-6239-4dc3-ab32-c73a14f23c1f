import React, { useState } from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { Icon } from "@bitcine/cinesend-theme/dist"
import updateSearchParams from "helpers/update_search_params"
import { useLanguage } from "contexts/language_context"

const Link = styled.div`
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  font-weight: normal;
  color: ${props => props.theme === 'light' ? '#000' : '#fff'};
  :hover {
    color: ${props => props.color};
  }
`

const Checked = styled.span`
  font-size: 16px;
  color: ${props => props.color};
`

const MobileLanguageDropdown = ({
  color,
  theme,
  languages,
}) => {
  const { activeLanguage, setActiveLanguage } = useLanguage()
  const [showLanguages, setShowLanguages] = useState(false)
  const { t } = useTranslation()
  let location = useLocation()
  const navigate = useNavigate()

  const onLanguageClick = language => {
    setActiveLanguage(language)
    updateSearchParams(navigate, location, "language", language)
  }

  return (
    <>
      {
        languages.length > 1 &&
        <Link
          theme={theme}
          color={color}
          onClick={() => setShowLanguages(!showLanguages)}
          className='pl2 link csod-nav-link csod-nav-link-language-selector flex items-center space-x-2'>
          <div>{t(`languages.${activeLanguage}`)}</div>
          <Icon icon={'language'} className='text-sm csod-mobile-language-icon'/>
          <div className='csod-mobile-language-icon-placeholder hidden'/>
          <Icon icon={'chevron_right'} className={`text-sm transition-transform ${showLanguages ? 'rotate-90' : ''}`}/>
        </Link>
      }
      {
        showLanguages &&
        <>
          {languages.map(language =>
            <Link
              theme={theme}
              color={color}
              onClick={() => onLanguageClick(language)}
              className='pl3 link csod-nav-link'>
              <span className='flex items-center'>
                {language === activeLanguage && <Checked className='material-icons mr1' color={color}>check_circle</Checked>}
                {t(`languages.${language}`)}
              </span>
            </Link>
          )}
        </>
      }
    </>
  )
}

MobileLanguageDropdown.propTypes = {
  user: PropTypes.object,
  color: PropTypes.string,
  pending: PropTypes.bool,
  theme: PropTypes.string,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  user: state.auth.user,
  color: state.organization.accent_color,
  pending: state.auth.status === 'PENDING',
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  languages: state.organization.languages,
  organization: state.organization,
})

export default connect(mapStateToProps)(MobileLanguageDropdown)
