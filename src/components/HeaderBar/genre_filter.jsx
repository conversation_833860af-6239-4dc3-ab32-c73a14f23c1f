import React, { useState } from "react"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { bindActionCreators } from "redux"
import { ButtonDropdown, Icon } from "@bitcine/cinesend-theme"
import { setFilterGenreID } from "api/dashboard"
import styled from "@emotion/styled"
import updateSearchParams from "helpers/update_search_params"

const Link = styled.div`
  cursor: pointer;
  font-weight: normal;
  color: ${props => props.themeColor};
  :hover {
    color: ${props => props.accentColor};
  }
`

const Checked = styled.span`
  font-size: 16px;
  color: ${props => props.accentColor};
`

const GenreFilter = ({
  textColor,
  themeColor,
  accentColor,
  genres,
  filterGenreID,
  setFilterGenreID,
}) => {
  const location = useLocation()
  const [showGenres, setShowGenres] = useState(false)
  const genre = genres.find(opt => opt._id === filterGenreID)
  const activeGenreLabel = genre ? genre.label : 'All Genres'
  const navigate = useNavigate()

  const handleGenreClick = (genreID) => {
    setFilterGenreID(genreID)
    updateSearchParams(navigate, location, 'genreID', genreID)
  }

  const handleClearGenreClick = () => {
    setFilterGenreID(null)
    updateSearchParams(navigate, location, 'genreID', null)
  }

  const DesktopButtonDropdown = () => 
    <ButtonDropdown
      button={{
        component: <div
          className='csod-genre-filter-button border border-gray-300 rounded-lg py-1 px-4 mr-1 cursor-pointer flex items-center space-x-2 whitespace-nowrap'
          style={{ color: textColor }}>
          <div>{activeGenreLabel}</div>
          <Icon icon='expand_more' className='text-base text-gray-600'/>
        </div>
      }}
      dropdown={{
        content: <div className='overflow-y-scroll max-h-[75vh]'>
          {
            [{
              label: 'All Genres',
              onClick: () => handleClearGenreClick(),
              breakAfter: true
            },
            ...genres.map((genre, index) => ({
              ...genre,
              onClick: () => handleGenreClick(genre._id),
            }))].map((genre, index) =>
              <div key={index} className={`
                  ${genre.breakAfter ? 'border-b' : ''}
                  cursor-pointer px-4 py-1 hover:bg-gray-200 flex items-center space-x-2 dark:hover:bg-gray-700
                `}
                onClick={genre.onClick}>
                {filterGenreID === genre._id ? <Icon icon='check_circle_outline' className='text-sm'/> : null}
                <div>{genre.label}</div>
              </div>
            )
          }
        </div>
        
        
      }}/>
  
  const MobileSlidedown = () =>
    <>
      <Link
        themeColor={themeColor}
        accentColor={accentColor}
        onClick={() => setShowGenres(!showGenres)}
        className='pl2 link csod-nav-link csod-nav-link-genre-selector flex items-center space-x-2'>
        <div>{activeGenreLabel}</div>
        <Icon icon={showGenres ? 'chevron_left' : 'chevron_right'} className='text-sm'/>
      </Link>
      {
        showGenres &&
        <>
          {genres.map(genre =>
            <Link
              themeColor={themeColor}
              accentColor={accentColor}
              onClick={() => handleGenreClick(genre._id)}
              className='pl3 link csod-nav-link'>
              <span className='flex items-center'>
                {filterGenreID === genre._id && <Checked className='material-icons mr1' accentColor={accentColor}>check_circle</Checked>}
                {genre.label}
              </span>
            </Link>
          )}
        </>
      }
    </>
  return <DesktopButtonDropdown/>
}

const sortByKey = (array, key) => {
  return array.sort(function(a, b) {
    return a[key].trim() > b[key].trim() ? 1 : -1
  });
}

const mapStateToProps = state => ({
  textColor: state.organization.header_text_color,
  accentColor: state.organization.accent_color,
  themeColor: state.organization.theme_text_color,
  genres: sortByKey(state.organization.genres, 'label'),
  filterGenreID: state.dashboard.filterGenreID,
  color: state.organization.accent_color,
})

const mapDispatchToProps = dispatch => ({
  setFilterGenreID: bindActionCreators(setFilterGenreID, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(GenreFilter)
