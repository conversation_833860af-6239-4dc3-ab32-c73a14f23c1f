import React, { useEffect } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import { ButtonDropdown, Icon } from "@bitcine/cinesend-theme"
import { logOut } from "/src/api/auth"
import tvApps from '/src/helpers/tv_apps'
import { updateSearchTerm } from '/src/api/search'
import * as CookieConsent from 'vanilla-cookieconsent'

const Controls = ({ authenticated, publicMode, selfRegister, textColor, logOut, pending, tvAppsEnabled, loginRestrictionsEnabled, theme, useMonetization, updateSearchTerm, showCookieConsent, onVoucherPage }) => {
  const { t } = useTranslation()
  const location = useLocation()
  const navigate = useNavigate()

  //Whenever the history is changed we want to clear the search input
  //The search results will no longer overlap the destination page
  useEffect(() => {
    updateSearchTerm("")
  }, [location])

  const content = [
      {
        text: t("userDropdown.watchOnTV"),
        icon: "tv",
        onClick: () => navigate('/settings/pair'),
        show: authenticated && tvAppsEnabled
      },
      {
        text: t("userDropdown.manageAccessDevices"),
        icon: "devices",
        onClick: () => navigate('/settings/devices'),
        show: authenticated && loginRestrictionsEnabled
      },
      {
        text: t("settings.subscriptions"),
        onClick: () => navigate('/settings/subscriptions'),
        icon: 'subscriptions',
        show: authenticated
      },
      {
        text: t("userDropdown.purchases"),
        onClick: () => navigate('/settings/purchase-history'),
        icon: "monetization_on",
        show: authenticated && useMonetization
      },
      {
        text: t("userDropdown.account"),
        icon: "account_circle",
        onClick: () => navigate('/settings/account'),
        show: authenticated
      },
      {
        text: t('userDropdown.manageCookies'),
        icon: "cookie",
        disabled: pending,
        onClick: () => CookieConsent.show(true),
        show: showCookieConsent
      },
      {
        text: t("userDropdown.logOut"),
        icon: "exit_to_app",
        disabled: pending,
        onClick: () => logOut(),
        show: authenticated
      },
      {
        text: t("userDropdown.logIn"),
        icon: "login",
        disabled: pending,
        onClick: () => navigate('/login'),
        show: !authenticated && publicMode && !onVoucherPage
      },
      {
        text: t("userDropdown.register"),
        icon: "account_circle",
        disabled: pending,
        onClick: () => navigate('/register'),
        show: !authenticated && publicMode && selfRegister && !onVoucherPage
      }
    ].filter(opt => opt.show)

  // If there's a scenario where no options are available to show, we return null
  if (content.length === 0) {
    return null;
  }
  
  return (
    <ButtonDropdown
      theme={theme}
      button={{
        component: <Icon icon='settings' className='csod-navbar-settings ml-2' style={{ color: textColor }}/>
      }}
      dropdown={{
        content
      }}/>
  )
}

Controls.propTypes = {
  user: PropTypes.object,
  logOut: PropTypes.func,
  textColor: PropTypes.string,
  pending: PropTypes.bool,
  theme: PropTypes.string,
  tvAppsEnabled: PropTypes.bool,
  loginRestrictionsEnabled: PropTypes.bool
}

const mapStateToProps = state => ({
  user: state.auth.user,
  authenticated: state.auth.status === 'AUTHENTICATED',
  publicMode: state.organization.enable_public_browsing ?? false,
  selfRegister: state.organization.allow_account_registrations ?? false,
  textColor: state.organization.header_text_color,
  pending: state.auth.status === 'PENDING',
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  tvAppsEnabled: tvApps.filter(app => state.organization.tv_apps[app.code + '_enabled']).length > 0,
  loginRestrictionsEnabled: state.organization.login_restrictions_enabled,
  useMonetization: state.organization.use_monetization,
  showCookieConsent: state.organization.show_cookie_consent
})

const mapDispatchToProps = dispatch => ({
  updateSearchTerm: bindActionCreators(updateSearchTerm, dispatch),
  logOut: bindActionCreators(logOut, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Controls)
