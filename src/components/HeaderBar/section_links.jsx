import React from "react"
import { connect } from "react-redux"
import { useNavigate } from "react-router-dom"
import styled from '@emotion/styled'
import { bindActionCreators } from 'redux'
import { setActiveSectionID } from '/src/api/dashboard'
import convertToURLFriendly from "helpers/convert_to_url_friendly"
import { setFilterGenreID } from "api/dashboard"

const SectionLink = styled.div`
  cursor: pointer;
  color: ${props => props.textColor};
  :hover {
      color: ${props => props.accentColor};
  }
`

const SectionLinks = ({ 
  sections, 
  themeColor, 
  textColor,
  accentColor,
  activeSectionID,
  setActiveSectionID,
  setFilterGenreID,
  isSidebar = false,
  onSectionClick = null
}) => {
    const navigate = useNavigate()
    return (<>
      {sections && sections.map((sec, index) =>
        <SectionLink
          key={sec._id}
          textColor={isSidebar ? themeColor : textColor}
          accentColor={accentColor}
          className={`
            csod-nav-link csod-nav-section-link link whitespace-nowrap
            ${index === 0 ? 'mr-2' : 'mx-2'}
            ${sec._id === activeSectionID ? 'font-extrabold active-section' : ''}
          `}
          onClick={() => {
            setActiveSectionID(sec._id)
            setFilterGenreID(null)
            navigate(`/sections/${convertToURLFriendly(sec.title)}`)
            if (typeof onSectionClick === "function") {
              onSectionClick()
            }
          }}
        >
          <span>{sec.title}</span>
        </SectionLink>)}      
    </>)
}

const mapStateToProps = state => ({
  sections: state.organization.dashboard_sections,
  activeSectionID: state.dashboard.activeSectionID,
  accentColor: state.organization.accent_color,
  themeColor: state.organization.theme_text_color,
  textColor: state.organization.header_text_color
})

const mapDispatchToProps = dispatch => ({
  setActiveSectionID: bindActionCreators(setActiveSectionID, dispatch),
  setFilterGenreID: bindActionCreators(setFilterGenreID, dispatch)
})


export default connect(mapStateToProps, mapDispatchToProps)(SectionLinks)
