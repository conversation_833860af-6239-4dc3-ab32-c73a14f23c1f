import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useNavigate, useParams } from "react-router-dom"
import { bindActionCreators } from 'redux'
import { setActiveSectionID } from '/src/api/dashboard'
import { setFilterGenreID } from "api/dashboard"

const Logo = ({ user, name, logoUrl, linkURL, setActiveSectionID, setFilterGenreID }) => {
  const { voucherID } = useParams()
  const onVoucherPage = voucherID || null
  const navigate = useNavigate()

  return (
    <img
      className={`mr1 csod-navbar-logo ${!onVoucherPage && 'pointer'}`}
      onClick={() => {
        // remove the active section from the dashboard state
        setActiveSectionID('')
        setFilterGenreID('')
        if (linkURL) {
          window.location.href = linkURL
        }
        else if (onVoucherPage) {
          return false
        }
        else if (user) {
          navigate('/')
        }
        else {
          navigate('/')
        }
      }}
      style={{ height: '100%', paddingTop: '8px', paddingBottom: '8px', objectFit: 'contain' }}
      src={logoUrl}
      alt={name}/>
  )
}

Logo.propTypes = {
  name: PropTypes.string,
  logoUrl: PropTypes.string,
  theme: PropTypes.string
}

const mapDispatchToProps = dispatch => ({
  setActiveSectionID: bindActionCreators(setActiveSectionID, dispatch),
  setFilterGenreID: bindActionCreators(setFilterGenreID, dispatch)
})

const mapStateToProps = state => ({
  user: state.auth.user,
  name: state.organization.name,
  logoUrl: state.organization.header_logo_url,
  linkURL: state.organization.header_logo_link_url,
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps, mapDispatchToProps)(Logo)
