import React from "react"
import { connect } from "react-redux"
import styled from '@emotion/styled'
import GenreFilter from "./genre_filter"
import TagFilters from "./tag_filters"
import useIsMobile from "hooks/use_is_mobile"
import SectionLinks from "./section_links"

const Bar = styled.div`
  position: relative;
  top: 0;
  min-height: ${props => props.headerHeight}px;
  width: 100%;
  ${props => props.headerColor && `background-color: ${props.headerColor};`}
  ${props => props.textColor && `color: ${props.textColor};`}
`

const SecondaryHeaderBar = ({
  headerHeight,
  headerColor,
  textColor,
  activeSectionID,
  sections,
}) => {
  const activeSection = sections.find(item  => item._id === activeSectionID)
  const showGenreFilter = activeSection?.show_genre_filter
  const { isMobile } = useIsMobile()
  return (
    <Bar
      headerHeight={headerHeight}
      className={`csod-subnavbar flex items-center justify-between ${isMobile ? 'px2' : 'px4'} overflow-y-scroll`}
      headerColor={headerColor}
      textColor={textColor}>
      <div className="border-t flex flex-wrap py-2 items-center flex-auto justify-between" style={{ height: '100%' }}>
        {isMobile && <div className='flex items-center'><SectionLinks/></div>}
        <div className='flex items-center'>
          {showGenreFilter && <GenreFilter/>}
          {isMobile && <TagFilters/>}
        </div>
      </div>
    </Bar>
  )
}

const mapStateToProps = state => ({
  headerHeight: state.organization.header_height,
  headerColor: state.organization.header_color,
  backgroundColor: state.organization.header_color,
  textColor: state.organization.header_text_color,
  sections: state.organization.dashboard_sections,
  activeSectionID: state.dashboard.activeSectionID,
})

export default connect(mapStateToProps)(SecondaryHeaderBar)
