import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { ButtonDropdown, Icon } from "@bitcine/cinesend-theme"
import { useNavigate } from "react-router-dom"
import updateSearchParams from "helpers/update_search_params"
import { useLanguage } from "contexts/language_context"

const LanguageDropdown = ({
  className,
  organization,
  textColor,
  theme,
  languages,
  icon = null,
  iconOnly = false,
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { activeLanguage, setActiveLanguage } = useLanguage()
  let location = useLocation()
  if (languages.length < 2) {
    return null
  }
  icon = icon || organization.language_icon

  return (
    <ButtonDropdown
      theme={theme}
      button={{
        component: iconOnly ? 
          <Icon icon={icon} style={{ color: textColor }} className={`csod-nav-icon ${className}`}/>
            :
          <div className={`${className} flex items-center space-x-2 border px-4 py-1 rounded-lg border-gray-300 text-[16px] csod-language-dropdown`}>
            {activeLanguage ? <div className='csod-active-language-label'>{t(`languages.${activeLanguage}`)}</div> : null}
            <Icon icon={icon} style={{ color: textColor }} className={`csod-nav-icon csod-language-icon`}/>
            <div className='csod-nav-icon-language-placeholder hidden cursor-pointer'/>
          </div>
      }}
      dropdown={{
        content: languages.map(language => ({
          text: t(`languages.${language}`),
          onClick: () => {
            setActiveLanguage(language)
            updateSearchParams(navigate, location, "language", language)
          },
          icon: activeLanguage === language ? "check_circle" : null,
          disabled: activeLanguage === language
        }))
      }}/>
  )
}

LanguageDropdown.propTypes = {
  user: PropTypes.object,
  textColor: PropTypes.string,
  theme: PropTypes.string,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  user: state.auth.user,
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  languages: state.organization.languages,
  organization: state.organization
})

export default connect(mapStateToProps)(LanguageDropdown)
