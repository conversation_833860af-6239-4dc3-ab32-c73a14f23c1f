import React from 'react'
import styled from '@emotion/styled'

const widths = {
  large: 970,
  medium: 728,
  small: 320
}

const heights = {
  large: 90,
  medium: 90,
  small: 100
}

const Wrapper = styled.div`
  margin: 15px auto;
  position: relative;
  width: ${props => widths[props.size]}px;
  height: ${props => heights[props.size]}px;
  cursor: pointer;
`

const Inner = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #555;
  background-image: url("${props => props.url}");
  background-position: center center;
  background-size: cover;
`

const Badge = styled.div`
  z-index: 1;
  font-size: 0.65em;
  line-height: 1;
  padding: 2px 4px;
  opacity: 0.25;
  position: absolute;
  top: 0;
  right: 0;
  background: #fff;
  color: #000;
  border-bottom-left-radius: 5px;
`

class AdImages extends React.Component {
  constructor(props) {
    super(props)
    this.timer = null
    this.state = {
      activeAdIdx: 0
    }
  }
  componentDidMount() {
    if (this.props.ads.length > 1) {
      this.tick()
    }
  }
  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  }
  tick = () => {
    this.timer = setTimeout(() => {
      if ((this.state.activeAdIdx + 1) > (this.props.ads.length - 1)) {
        this.setState({
          activeAdIdx: 0
        })
      } else {
        this.setState({
          activeAdIdx: this.state.activeAdIdx + 1
        })
      }
      this.tick()
    }, 15000)
  }
  render() {
    const ad = this.props.ads[this.state.activeAdIdx]
    return (
      <Wrapper size={this.props.size} onClick={() => {
        window.open(ad.url, '_blank')
      }}>
        <Badge>AD</Badge>
        <Inner url={ad['image_'+this.props.size]} />
      </Wrapper>
    )
  }
}

export default AdImages