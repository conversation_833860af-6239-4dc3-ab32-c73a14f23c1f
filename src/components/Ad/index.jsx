import React, { useEffect, useState } from 'react'
import { connect } from "react-redux"
import AdImages from './AdImages'

const Ad = ({ location, ads }) => {
  const [size, setSize] = useState('large')
  useEffect(() => {
    parseSize()
    window.addEventListener("resize", parseSize)
    return () => {
      window.removeEventListener("resize", parseSize)
    }
  }, [])
  const parseSize = () => {
    const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
    if (width <= 767) {
      setSize('small')
    }
    if (width > 767 && width <= 1100) {
      setSize('medium')
    }
    if (width > 1100) {
      setSize('large')
    }
  }
  const list = ads[location]
  const hasAds = ads.status === 'READY' && list.length > 0
  return hasAds ? (
    <AdImages ads={list} size={size} />
  ) : null
}

const mapStateToProps = state => ({
  ads: state.ads
})

export default connect(mapStateToProps)(Ad)