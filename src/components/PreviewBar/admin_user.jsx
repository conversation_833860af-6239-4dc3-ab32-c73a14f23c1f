import React from "react"
import { connect } from "react-redux"
import Avatar from './avatar'

const AdminUser = ({ adminUser, organizationName }) =>
  <div className='ml3 flex items-center'>
    <Avatar url={adminUser.photo_url} name={adminUser.full_name}/>
    <div className='ml2 mr3 flex flex-column' style={{ lineHeight: '14px' }}>
      <span>
        {adminUser.full_name}
      </span>
      <span className='light'>
        {organizationName}
      </span>
    </div>
  </div>

const mapStateToProps = state => ({
  adminUser: state.auth.admin.user,
  organizationName: state.organization.name,
})

export default connect(mapStateToProps)(AdminUser)
