import React from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import Toggle from 'react-toggle'
import { toggleUnpublishedChanges } from '/src/api/preview'
import "react-toggle/style.css"

const ShowUnpublishedChanges = ({ showUnpublishedChanges, toggleUnpublishedChanges }) =>
  <label className='flex items-center'>
    <Toggle
      checked={showUnpublishedChanges}
      onChange={() => toggleUnpublishedChanges()}/>
    <span className='ml1 light'>Show draft changes</span>
  </label>

const mapStateToProps = state => ({
  showUnpublishedChanges: state.auth.admin.show_unpublished_changes
})

const mapDispatchToProps = dispatch => ({
  toggleUnpublishedChanges: bindActionCreators(toggleUnpublishedChanges, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(ShowUnpublishedChanges)
