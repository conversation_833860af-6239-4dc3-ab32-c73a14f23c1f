import React from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import styled from '@emotion/styled'
import { togglePreviewBar } from '/src/api/preview'
import Exit from './exit'
import Mode from './mode'
import Subscriptions from './subscriptions'
import Toggle from './toggle'
import AdminUser from './admin_user'
import { useLocation } from "react-router"

const Container = styled.div`
  position: fixed;  
  top: 0;
  width: 100%;
  z-index: 2;
`

const Bar = styled.div`
  height: 64px;
  background-color: white;
  color: black;
`

const PreviewBar = ({ theme, togglePreviewBar, isMinimized, showSubscriptions, areCategoriesLoading }) => {
  const location  = useLocation()
  const path = location.pathname || ""
  return (
    <Container>
      {!isMinimized &&
        <Bar className={`flex items-center justify-between`}>
          <div className="flex items-center" style={{ height: '100%' }}>
            <Exit/>
            <Mode assetID={path.startsWith("/view/") ? path.split("/view/")[1] : null}/>
            {showSubscriptions && <Subscriptions/>}
            {areCategoriesLoading && path === "/" && <span className='ml2 small italic'>Loading...</span>}
          </div>
          <div className='flex items-center'>
            <Toggle/>
            <AdminUser/>
          </div>
        </Bar>}
      <div className='col-12 center'>
        <span 
          className="material-icons pointer bg-white px1"
          style={{ color: "gray", borderRadius: "0px 0px 4px 4px", boxShadow: "2px 4px 2px -2px gray" }}
          onClick={() => togglePreviewBar()}>
          {isMinimized ? "expand_more" : "expand_less"}
        </span>
      </div>
    </Container>
  )
}

const mapStateToProps = state => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  showSubscriptions: state.auth.admin.preview_mode === 'subscriber',
  areCategoriesLoading: state.auth.admin.preview_mode !== 'voucher' && state.dashboard.categories.status !== 'READY',
  isMinimized: state.preview.is_minimized,
})

const mapDispatchToProps = dispatch => ({
  togglePreviewBar: bindActionCreators(togglePreviewBar, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(PreviewBar)
