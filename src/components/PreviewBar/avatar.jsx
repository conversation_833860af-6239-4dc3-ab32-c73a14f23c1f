import React from "react"
import styled from "styled-components"

const AvatarContainer = styled.div`
  border-radius: 9999px;
  border: lightgray solid 1px;
  padding: 2px;
  height: 40px;
  width: 40px;
  justify-content: center;
  color: rgb(75 85 99);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
`

const Avatar = ({ url = null, name = "" }) =>
  <AvatarContainer className='flex items-center'>
    {url
      ? <img src={url} alt={name} style={{ borderRadius: "9999px" }}/>
      : <span className=''>{name.split(" ").map((n)=>n[0]).join("")}</span>}
  </AvatarContainer>

export default Avatar