import React from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { ButtonDropdown } from '@bitcine/cinesend-theme'
import { updatePreviewMode } from '/src/api/preview'

const Mode = ({ previewMode, updatePreviewMode, assetID = null }) =>
  <div className='px3 border-right border-gray-5 flex items-center pointer' style={{ height: '100%' }}>
    <ButtonDropdown
      button={{
        minimal: true,
        type: 'neutral',
        text: <div className="flex items-center">
          Preview as: {previewMode}
          <span className="material-icons" style={{ color: "lightgray" }}>expand_more</span>
        </div>
      }}
      dropdown={{
        content: [
          {
            text: "Myself",
            icon: "verified_user",
            onClick: () => updatePreviewMode("admin"),
            show: true
          },
          {
            text: "Voucher",
            icon: "local_activity",
            onClick: () => updatePreviewMode("voucher", assetID),
            show: !!assetID
          },
          {
            text: "Subscriber",
            icon: "people",
            onClick: () => updatePreviewMode("subscriber"),
            show: true
          }
        ].filter(opt => opt.show)
      }}/>
  </div>

const mapStateToProps = state => ({
  previewMode: state.auth.admin.preview_mode === "subscriber"
    ? "Subscriber"
    : state.auth.admin.preview_mode === "voucher"
      ? "Voucher"
      : state.auth.admin.preview_mode === "admin" ? "Myself" : "N/A"
})

const mapDispatchToProps = dispatch => ({
  updatePreviewMode: bindActionCreators(updatePreviewMode, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Mode)
