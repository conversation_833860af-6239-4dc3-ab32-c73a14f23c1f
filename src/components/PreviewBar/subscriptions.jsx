import React, { useState } from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { ButtonDropdown, Checkbox, Button } from '@bitcine/cinesend-theme'
import { updatePreviewSubscriberTypes } from '/src/api/preview'

const Subscriptions = ({ availableSubscriberTypes, subscriberTypeIDs, updatePreviewSubscriberTypes }) => {
  const [selectedIDs, setSelectedIDs] = useState(subscriberTypeIDs)
  return (
    <div className='px3 border-right border-gray-5 flex items-center pointer' style={{ height: '100%' }}>
      <ButtonDropdown
        button={{
          type: 'neutral',
          minimal: true,
          text: <div className='flex items-center'>Subscriptions: {
            subscriberTypeIDs.length === 0
              ? 'None'
              : subscriberTypeIDs.length === availableSubscriberTypes.length
                ? 'All'
                : subscriberTypeIDs.length > 1
                  ? `${subscriberTypeIDs.length} selected`
                  : availableSubscriberTypes.find(sub => sub._id === subscriberTypeIDs[0]).name}
            <span className="material-icons" style={{ color: "lightgray" }}>expand_more</span>
          </div>
        }}
        dropdown={{
          clickCloses: false,
          classToClose: 'apply-button',
          content: <div className='p2'>
            <div
              className={`flex flex-column ${availableSubscriberTypes.length > 12 ? 'overflow-auto' : ''}`}
              style={{ maxHeight: 'calc(100vh - 200px)' }}>
              {availableSubscriberTypes.map(subscription =>
                <div key={subscription._id} className='csod-subscription-checkbox'>
                  <Checkbox
                    checked={selectedIDs.includes(subscription._id)}
                    onChange={() => setSelectedIDs(selectedIDs.includes(subscription._id)
                      ? selectedIDs.filter(id => id !== subscription._id)
                      : [...selectedIDs, subscription._id])}
                    label={subscription.name}/>
                </div>
              )}
            </div>
            <div className='flex justify-end mt2'>
              <Button className='cs-button apply-button' small white onClick={() => updatePreviewSubscriberTypes(selectedIDs)}>Apply</Button>
            </div>
          </div>
        }}/>
    </div>
  )
}

const mapStateToProps = state => ({
  subscriberTypeIDs: state.auth.admin.subscriber_type_ids || [],
  availableSubscriberTypes: state.auth.admin.available_subscriber_types,
})

const mapDispatchToProps = dispatch => ({
  updatePreviewSubscriberTypes: bindActionCreators(updatePreviewSubscriberTypes, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(Subscriptions)
