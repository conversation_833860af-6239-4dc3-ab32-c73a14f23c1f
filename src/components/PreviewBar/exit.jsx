import React from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { MdOutlineClose } from 'react-icons/md'
import { logOut } from '/src/api/auth'

const Exit = ({ logOut }) =>
  <div
    className='px3 border-right border-gray-5 flex items-center pointer'
    style={{ height: '100%' }}
    onClick={() => logOut()}>
    <div className='text-neutral-600 flex items-center'>
      <MdOutlineClose/>
      <span className='ml2'>Exit Preview Mode</span>
    </div>
  </div>

const mapDispatchToProps = dispatch => ({
  logOut: bindActionCreators(logOut, dispatch),
})

export default connect(null, mapDispatchToProps)(Exit)
