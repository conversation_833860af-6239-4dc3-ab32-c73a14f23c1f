import React from 'react'
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import CineSendPlayer from "@bitcine/cinesend-player"
import styled from '@emotion/styled'
import { Status } from '@bitcine/cinesend-theme'
import ErrorWrapper from '../AssetPlayer/error_wrapper'
import { getLiveStreamStatus, getLiveStreamEndpoints } from '/src/api/live'
import { createLog } from '/src/api/session'
import config from '/src/config'
import Offline from './offline'

const InnerContainer = styled.div`
  ${props => props.fillViewport ? `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    ` : `
    width: 100%;
    aspect-ratio: 16 / 9;
    background: #111111;
    `}
`

class LiveStreamPlayer extends React.Component {

  _playerID = 'cinesend-ondemand-live-player-id'
  _timer = null

  state = {
    is_online: false,
    is_disconnected: false,
    loading: true,
    error: null,
    errorMessage: null
  }

  componentDidMount() {
    this.pollLiveStatus()
  }

  componentWillUnmount() {
    this.restoreUserway()
    clearInterval(this._timer)
  }

  pollLiveStatus = () => {

    let timeToken = 0
    let interval = 10 // seconds

    const shouldCheckLiveStatus = () => {

      timeToken += interval

      let now = new Date()
      let liveStreamStarted = (now >= Date.parse(this.props.video.live_stream_date))
      let liveStreamEnded = (now >= Date.parse(this.props.video.live_stream_end_date))

      // If the stream is not online but the live stream should have started, let's check live status.
      if (!this.state.is_online && liveStreamStarted) {
        return true
      }

      // If the stream is online but the live stream should have ended, let's check live status.
      if (this.state.is_online && liveStreamEnded) {
        return true
      }

      // We'll also check every thirty seconds if the stream is online, in case the stream starts or ends outside the designated windows.
      if (timeToken % 30 === 0) {
        return true
      }

      return false

    }

    this._timer = setInterval(() => {
      if (shouldCheckLiveStatus()) {
        this.checkLiveStatus()
      }
    }, interval * 1000)

    this.checkLiveStatus()
  }

  checkLiveStatus = () => {
    this.props.getLiveStreamStatus(
      this.props.video.live_mux_id,
      res => {

        // If it's now online and we don't have a session yet, retrieve endpoints and build a session.
        if (res.is_online && !this.state.activeSessionID) {
          this.props.getLiveStreamEndpoints(this.props.video.live_mux_id, this.props.voucherID)
        }

        this.setState({ is_online: res.is_online, is_disconnected: res.is_disconnected, loading: false, error: null })
      },
      error => {
        this.setState({ errorMessage: error, loading: false })
      }
    )
  }

  onPlayerEvent(log) {
    this.props.createLog(this.props.config.session_logs_url, {
      ...log,
      sessionID: this.state.activeSessionID,
      timestamp: Date.now()
    })
    if (log.logType === 'player_loaded') {
      this.hideUserway()
    }
    if (log.logType === 'media_end') {
      this.restoreUserway()
    }
    if (log.logType === 'media_error') {
      this.setState({
        error: log.error
      })
    }
  }

  hideUserway() {
    // Don't hide if we have multiple components (not full-screen)
    if (!this.props.fillViewport) {
      return
    }
    try {
      window.UserWay.iconVisibilityOff();
    } catch (e) {}
  }

  restoreUserway() {
    try {
      window.UserWay.iconVisibilityOn();
    } catch (e) {}
  }

  isOnline() {
    return this.state.is_online
        && this.props.endpoints
        && this.props.config
        && this.props.endpoints.videos[0].source.hls
  }

  render() {
    return (
      <InnerContainer
        fillViewport={this.props.fillViewport}
        className={`csod-livestream-player-container`}>
        {!this.state.loading && <Status
          error={this.state.errorMessage || this.props.error}
          errorMessage={this.state.errorMessage || this.props.error}>
          {this.isOnline() ?
            <ErrorWrapper
              error={this.state.error}
              device={this.props.device}
              recommendation={this.props.device.recommendation}>
              <CineSendPlayer
                playerCode={this.props.config.player_code}
                chromecastReceiver={this.props.config.chromecast_receiver_code}
                downgradeQualityOnLowDeviceRobustness={this.props.config.downgrade_quality_on_low_device_robustness}
                disableResumePlayback={true}
                debug={process.env.VITE_ENV !== 'production'}
                playerID={this._playerID}
                activeIndex={0}
                config={{}}
                endpoints={this.props.endpoints}
                fillViewport={this.props.fillViewport}
                autoPlay={true}
                onPlayerEvent={log => this.onPlayerEvent(log)}
                onVideoChange={video => this.setState({ activeSessionID: video.source.session_id })}
                onVideosComplete={() => {}}/>
            </ErrorWrapper> :
            <Offline
              video={this.props.video}
              isDisconnected={this.state.is_disconnected}
              isMobile={this.props.isMobile}/>
          }
        </Status>}
      </InnerContainer>
    )
  }
}

const mapStateToProps = (state, props) => {
  const video = props.voucherID ? state.session.landingDetails.video : state.dashboard.video
  return {
    video,
    userID: props.voucherID ? state.session.landingDetails.video.voucher_id : state.auth.user ? state.auth.user._id : null,
    organization: state.organization,
    playerCode: state.organization.player_code,
    endpoints: state.live_stream.endpoints,
    config: state.live_stream.config,
    device: state.live_stream.device,
    error: state.live_stream.error,
    fillViewport: !video.is_audience_interaction_enabled
  }
}

const mapDispatchToState = dispatch => ({
  getLiveStreamStatus: bindActionCreators(getLiveStreamStatus, dispatch),
  getLiveStreamEndpoints: bindActionCreators(getLiveStreamEndpoints, dispatch),
  createLog: bindActionCreators(createLog, dispatch)
})

export default connect(mapStateToProps, mapDispatchToState)(LiveStreamPlayer)
