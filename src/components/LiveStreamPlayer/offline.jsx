import React from 'react'
import styled from '@emotion/styled'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'

const Wrapper = styled.div`
  position: relative;
  padding-top: 56.25%;
  width: 100%;
  background-image: url("${props => props.image}");
  background-size: cover;
  background-position: center center;
  min-height: 280px;
`

const Inner = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0,0,0,0.6);
  padding: 20px;
`

const Badge = styled.span`
  background: #000;
  color: #fff;
  text-transform: uppercase;
  padding: 4px 8px;
  font-size: 0.9em;
`

const CenteredText = styled.div`
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  ${props => props.isMobile && `font-size: 12px;`}
`

const Offline = ({ video, isMobile, isDisconnected }) => {
  const { t } = useTranslation()
  const startingSoon = video.live_stream_date && dayjs(video.live_stream_date).diff(dayjs(), 'hours', true) < 1
  return (
    <Wrapper image={video.thumbnail_url} className='csod-live-player-offline-wrapper border border-gray-2 rounded'>
      <Inner className='csod-live-player-offline-label'>
        <Badge className='csod-live-player-offline-badge'>
          {video.live_stream_event_title}: <span className='csod-live-player-offline-text'>{t("liveStream.offline")}</span>
        </Badge>
        <CenteredText isMobile={isMobile} className='csod-live-player-offline-text-wrapper flex flex-column items-center justify-center'>
          {isDisconnected ? <h4 className='center csod-live-player-offline-starting-soon'>{t("liveStream.sourceDisconnected")}</h4> :
            startingSoon
              ? <h4 className='center csod-live-player-offline-starting-soon'>{t("liveStream.startingSoon")}</h4>
              : <h4 className='center csod-live-player-offline-check-back-later'>{t("liveStream.checkBackLater")}</h4>}
          <p className='center bold csod-live-player-offline-subheader-text'>{isDisconnected ? t("liveStream.isDisconnected") : t("liveStream.subtext")}</p>
        </CenteredText>
      </Inner>
    </Wrapper>
  )
}

export default Offline