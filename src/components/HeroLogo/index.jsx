import React from 'react'
import { connect } from "react-redux"
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'

const Container = styled.div`
  width: 80%;
  max-width: 600px;
  height: auto;
`

const Logo = styled.img`
  cursor: pointer;
  width: 100%;
  height: auto;
`

const HeroLogo = ({ logoUrl, name }) =>
  { 
    const navigate = useNavigate()
    return (<Container className='py4 mx-auto'>
    <Logo src={logoUrl} alt={name} onClick={() => navigate('/')}/>
  </Container>)}

const mapStateToProps = state => ({
  name: state.organization.name,
  logoUrl: state.organization.hero_logo_url
})

export default connect(mapStateToProps)(HeroLogo)
