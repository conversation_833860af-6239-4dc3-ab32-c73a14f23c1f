import React, { useState } from "react"
import { useTranslation } from 'react-i18next'
import { connect } from "react-redux"
import { Modal, Button } from '@bitcine/cinesend-theme'

const ActiveBulletin = ({ activeBulletin, organization }) => {
  const { t } = useTranslation()
  const acknowledged = localStorage.getItem('acknowledged-bulletin-' + activeBulletin._id) || false
  const [showBulletin, setShowBulletin] = useState(!acknowledged)
  if (!showBulletin) return null
  const dismiss = () => {
    localStorage.setItem('acknowledged-bulletin-' + activeBulletin._id, true)
    setShowBulletin(false)
  }
  return (
    <Modal open onClose={dismiss} width={3} title={activeBulletin.title} theme={organization.use_light_theme ? 'light' : 'dark'}>
      <div>
        {activeBulletin.information && <div dangerouslySetInnerHTML={{ __html: activeBulletin.information }}/>}
        {activeBulletin.content && <div dangerouslySetInnerHTML={{ __html: activeBulletin.content }}/>}
        <div className='flex justify-end'>
          <Button
            className={'cs-button'}
            style={{ background: organization.accent_color }}
            onClick={dismiss}>
            {t("buttons.dismiss")}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

const mapStateToProps = state => ({
  activeBulletin: state.organization.active_bulletin,
  organization: state.organization
})

export default connect(mapStateToProps)(ActiveBulletin)
