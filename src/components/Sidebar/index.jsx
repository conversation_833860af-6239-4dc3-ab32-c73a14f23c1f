import React, { useEffect, useRef } from "react"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { logOut } from "/src/api/auth"
import styled from '@emotion/styled'
import Links from '../HeaderBar/links'
import Controls from '../HeaderBar/controls_mobile'
import { GoChevronRight } from 'react-icons/go'

const Bar = styled.div`
  position: fixed;
  right: 0;
  top: 0;
  z-index: 3;
  height: 100vh;
  width: 220px;
  background: ${props => props.useLightTheme ? "rgba(255, 255, 255, 1)" : "rgba(15, 15, 15, 1)" };
  .link {
    border-bottom: solid 1px;
    border-color: ${props => props.useLightTheme ? "rgba(150, 150, 150, 1)" : "rgba(255, 255, 255, 0.5)" };
    margin-bottom: 8px;
    padding-bottom: 8px;
  }
  overflow-y: scroll;
`

const Sidebar = ({ onOutsideClick, useLightTheme, themeColor, onVoucherPage }) => {
  const wrapperRef = useRef(null)
  useEffect(() => {
     const handleClick = event => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        onOutsideClick()
      }
    }
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick)
  }, [wrapperRef, onOutsideClick]);
  return (
    <Bar ref={wrapperRef} useLightTheme={useLightTheme} className='z1 py2 border-l border-gray-300'>
      <div className='link'>
        <GoChevronRight color={themeColor} className='pointer pl2' size='2em' onClick={onOutsideClick}/>
      </div>
      <Links isSidebar onVoucherPage={onVoucherPage}/>
      <Controls onVoucherPage={onVoucherPage} />
    </Bar>
  )
}

const mapStateToProps = state => ({
  useLightTheme: state.organization.use_light_theme,
  themeColor: state.organization.theme_text_color
})

const mapDispatchToProps = dispatch => ({
  logOut: bindActionCreators(logOut, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Sidebar)
