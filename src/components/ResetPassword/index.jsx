import React, { useState } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import styled from "styled-components"
import { useTranslation } from 'react-i18next'
import { resetPassword } from '/src/api/auth'
import { Button } from '@bitcine/cinesend-theme'
import useIsMobile from "hooks/use_is_mobile"

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const ResetPassword = ({ accent, back, lang, resetPassword, reset, useLightTheme }) => {
  const { t } = useTranslation()
  const [email, setEmail] = useState('')
  const { isMobile } = useIsMobile()
  return (
    <Card isMobile={isMobile} className={`${isMobile ? 'p3' : 'p4'} csod-reset-password-card box-shadow mx-auto`}>
      {reset.isSuccess ? (
        <>
          <span style={{ color: accent }}>{t("home.pleaseCheckYourInbox")}</span>
        </>
      ) : (
        <>
          <h4 style={{ textAlign: 'center' }} className='csod-reset-password-header'>{t("home.resetPassword")}</h4>
          <input
            className='cs-input col-12 mt1'
            placeholder={t("home.emailAddress")}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onKeyPress={e => {
              if (e.key === 'Enter' && email) {
                resetPassword(email, lang)
              }
            }}/>
          {reset.error && <small className='red mb2'>{reset.error}</small>}
          <div className='flex justify-between items-center mt2'>
            {typeof back === "function" && <Button
              disabled={reset.isSubmitting}
              className='cs-button csod-reset-password-back-button link small'
              style={{ color: useLightTheme ? '#000' : '#fff' }}
              onClick={() => back()}>&larr; {t("home.back")}
            </Button>}
            <Button
              className={`cs-button csod-reset-password-button`}
              style={{ background: accent }}
              disabled={!email || reset.isSubmitting}
              small
              onClick={() => resetPassword(email, lang)}>
              {t("home.reset")}
            </Button>
          </div>
        </>
      )}
    </Card>
  )
}

ResetPassword.propTypes = {
  resetPassword: PropTypes.func,
  reset: PropTypes.object,
  useLightTheme: PropTypes.bool
}

const mapStateToProps = state => ({
  accent: state.organization.accent_color,
  reset: state.auth.reset,
  useLightTheme: state.organization.use_light_theme
})

const mapDispatchToProps = dispatch => ({
  resetPassword: bindActionCreators(resetPassword, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(ResetPassword)
