import React, { useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import Select from 'react-select'
import Slider from 'rc-slider'
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { Checkbox } from '@bitcine/cinesend-theme'
import { updateFilter, applyFilters, clearFilters, updateCustomFilter } from '/src/api/search' 
import { getCountries } from '/src/api/utils'
import { darkStyles} from '/src/helpers/dark_styles'
import 'rc-slider/assets/index.css'
import 'rc-tooltip/assets/bootstrap.css'

const createSliderWithTooltip = Slider.createSliderWithTooltip
const Range = createSliderWithTooltip(Slider.Range)

const ApplyButton = styled.button`
  background: ${props => props.organization.background_color}66;
  color: ${props => props.organization.foreground_color};
  border-color: ${props => props.accent}bf;
  :hover, :focus {
    background: ${props => props.accent}3f;
    border-color: ${props => props.accent};
  }
`

const CancelButton = styled.button`
  background: ${props => props.organization.background_color}66;
  color: ${props => props.organization.foreground_color};
  border-color: rgba(255,255,255,0.2);
  :hover, :focus {
    background: ${props => props.organization.background_color}66;
    border-color: rgba(255, 255, 255, 0.8);
  }
`

const Filter = styled.div`
  // min-width: 240px;
  width: 100%;
  // display: inline-block;
  .rc-slider-track {
    background-color: ${props => props.accent} !important;
  }
  .rc-slider-handle {
    border-color: ${props => props.accent} !important;
  }
  .rc-slider-handle-dragging {
    box-shadow: none !important;
  }
`

const SearchInput = ({ 
  search,
  theme = 'dark',
  organization,
  getCountries, 
  utils, 
  updateFilter,
  applyFilters,
  clearFilters,
  accent,
  updateCustomFilter,
  checkboxes,
  dropdowns
}) => {
  const { t } = useTranslation()
  useEffect(() => {
    getCountries()
  }, [getCountries])
  const half = Math.ceil(dropdowns.length / 2)
  const dropdowns1 = dropdowns.slice(0, half)
  const dropdowns2 = dropdowns.slice(half, dropdowns.length)
  return (
    <div className='px4 py2 csod-filters-container'>
      <div className={`csod-filter-outter-container max-width-4 mx-auto p2 border rounded ${theme === 'dark' ? 'border-gray-1' : 'border-gray-4'}`}>
        <div className='clearfix mxn2'>
          <div className={`sm-col sm-col-12 px2 ${dropdowns2.length > 0 ? `md-col-4` : `md-col-6`}`}>
            <Filter accent={accent} className='mb3 csod-duration-filter '>
              <label className='cs-label csod-filter-label'>{t("search.duration")}</label>
              <div className='pt1 px1'>
                <Range 
                  min={0} 
                  max={240} 
                  value={search.duration} 
                  onChange={values => {
                    updateFilter('duration', values)
                  }}
                  allowCross={false}
                  tipFormatter={value => `${value}min`} />
              </div>
            </Filter>
            {checkboxes.map((field, i) =>
              <Filter key={i} className={`mt2 csod-checkbox-filter-${field.label.replace(/ /g, '')}`}>
                <Checkbox
                  label={field.label}
                  className='flex items-center csod-filter-checkbox'
                  checked={search.customFields[field._id] || false}
                  onChange={() => updateCustomFilter(field._id, search.customFields[field._id] ? false : true)} />
              </Filter>  
            )}
          </div>
          <div className={dropdowns2.length > 0 ? `sm-col sm-col-12 md-col-8 px2` : `sm-col sm-col-12 md-col-6 px2`}>
            <div className='clearfix mxn2'>
              <div className={dropdowns2.length > 0 ? `sm-col sm-col-12 md-col-6 px2` : `sm-col sm-col-12 px2`}>
                <Filter className='csod-countries-filter'>
                  <label className='cs-label csod-filter-label'>{t("search.countries")}</label>
                  <Select
                    isMulti
                    styles={theme === 'dark' ? darkStyles : {}}
                    options={utils.countries.list}
                    isLoading={!utils.countries.list.length}
                    value={search.countries}
                    className={'col-12 csod-filter-select-input'}
                    onChange={values => {
                      updateFilter('countries', values || [])
                    }}
                  />
                </Filter>
                {dropdowns1.map((field, i) =>
                  <Filter key={i} className={`mt2 csod-dropdown-filter-${field.label.replace(/ /g, '')}`}>
                    <label className='cs-label csod-filter-label'>{field.label}</label>
                    <Select
                      isMulti
                      styles={theme === 'dark' ? darkStyles : {}}
                      options={field.options.map(opt => {
                        return {
                          label: opt.label,
                          value: opt._id
                        }
                      })}
                      value={search.customFields[field._id] || null}
                      className={'col-12 csod-filter-select-input'}
                      onChange={values => {
                        updateCustomFilter(field._id, values)
                      }}
                    />
                  </Filter>
                )}
              </div>
              {dropdowns2.length > 0 && (
                <div className='sm-col sm-col-12 md-col-6 px2'>
                  {dropdowns2.map((field, i) =>
                    <Filter key={i} className={`${i === 0 ? 'mt0' : 'mt2'} csod-dropdown-filter-${field.label.replace(/ /g, '')}`}>
                      <label className='cs-label csod-filter-label'>{field.label}</label>
                      <Select
                        isMulti
                        styles={theme === 'dark' ? darkStyles : {}}
                        options={field.options.map(opt => {
                          return {
                            label: opt.label,
                            value: opt._id
                          }
                        })}
                        value={search.customFields[field._id] || null}
                        className={'col-12 csod-filter-select-input'}
                        onChange={values => {
                          updateCustomFilter(field._id, values)
                        }}
                      />
                    </Filter>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className='flex justify-end mt2 csod-filter-buttons-container'>
          <CancelButton
            organization={organization}
            className='cs-button center small csod-filter-cancel-button'
            onClick={clearFilters}>
            {t("search.clear")}
          </CancelButton>
          <ApplyButton 
            organization={organization}
            accent={accent}
            className='ml2 cs-button center small csod-filter-apply-button' 
            onClick={applyFilters}>
            {t("search.apply")}
          </ApplyButton>
        </div>
      </div>
    </div>
  )
}

const mapStateToProps = state => ({
  search: state.search,
  utils: state.utils,
  accent: state.organization.accent_color,
  organization: state.organization,
  checkboxes: state.organization.custom_fields.filter(f => f.is_searchable && f.type === 'boolean'),
  dropdowns: state.organization.custom_fields.filter(f => f.is_searchable && f.type === 'dropdown')
})

const mapDispatchToProps = dispatch => ({
  getCountries: bindActionCreators(getCountries, dispatch),
  updateFilter: bindActionCreators(updateFilter, dispatch),
  applyFilters: bindActionCreators(applyFilters, dispatch),
  clearFilters: bindActionCreators(clearFilters, dispatch),
  updateCustomFilter: bindActionCreators(updateCustomFilter, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(SearchInput)
