import { useEffect, useState } from "react"
import { Icon } from "@bitcine/cinesend-theme"

const ImageLoader = ({ className = '', url, onLoad, alt }) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageDimensions, setImageDimensions] = useState({ width: null, height: null })
  const imageStyle = imageLoaded ? {} : { display: 'none' }

  // Get image size
  var img = document.createElement('img')
  img.src = url

  useEffect(() => {
    var poll = setInterval(function () {
      if (img.naturalWidth) {
        clearInterval(poll)
        setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight })
      }
    }, 10)
  }, [])

  var placeholderStyle = {
    width: imageDimensions.width ? `${imageDimensions.width}px` : '100%',
    aspectRatio: `${imageDimensions.width} / ${imageDimensions.height}`,
    maxWidth: '100%',
  }

  return (
    <>
      {!imageLoaded &&
        <div
          style={placeholderStyle}
          className='bg-gray-800 animate-pulse w-full'>
          <div className='flex items-center justify-center w-full h-full'>
            <Icon icon='movie' className='opacity-50 text-gray-500 text-4xl'/>
          </div>
        </div>}
      <img
        className={className}
        src={url}
        style={imageStyle}
        alt={alt}
        onLoad={() => {
          setImageLoaded(true)
          onLoad()
        }}/>
    </>
  )
}

export default ImageLoader