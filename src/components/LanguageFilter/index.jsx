import React from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import styled from 'styled-components'
import { ButtonDropdown } from "@bitcine/cinesend-theme"
import { setLanguageCodeFilter } from "/src/api/dashboard"
import { getButtonWidth, getSlidePadding } from '../VideoCarousel/utils'

const ButtonWrapper = styled.div`
  .cs-button {
    width: ${props => props.width};
    color: ${props => props.textColor};
    background: rgba(0,0,0,0.2) !important;
    border-color: ${props => props.main ? `${props.accentColor}bf` : 'rgba(255,255,255,0.2)'} !important;
    border-radius: ${props => props.borderRadius}px;
    :hover {
      background: ${props => props.main ? `${props.accentColor}3f` : 'rgba(0,0,0,0.2)'} !important;
      border-color: ${props => props.main ? props.accentColor : 'rgba(255,255,255,0.8)'} !important;
    }
  }
`

const LanguageFilter = ({ accentColor, textColor, borderRadius, theme, languages, activeLanguageCodeFilter, setLanguageCodeFilter }) => {
  const { t } = useTranslation()
  return (
    languages.length < 1 ? null :
      <ButtonWrapper
        accentColor={accentColor}
        textColor={textColor}
        borderRadius={borderRadius}
        style={{paddingRight: (getButtonWidth()+getSlidePadding())+'px'}}>
        <ButtonDropdown
          theme={theme}
          button={{
            className: 'cs-button csod-filter-languages-dropdown-button',
            text: <span className='csod-filter-languages-dropdown-button-text'>Filter By Language</span>
          }}
          dropdown={{
            content: [
              ...[{
                text: <span className='csod-filter-languages-clear-text'>Clear</span>,
                onClick: () => setLanguageCodeFilter(null),
                show: !!activeLanguageCodeFilter
              }],
              ...languages.map(language => ({
                text: t(`languages.${language}`),
                onClick: () => setLanguageCodeFilter(activeLanguageCodeFilter === language ? null : t(`languages.${language}`)),
                icon: activeLanguageCodeFilter === t(`languages.${language}`) ? "check_circle" : null,
                show: true
              }))
            ].filter(opt => opt.show)
          }}/>
      </ButtonWrapper>
  )
}

LanguageFilter.propTypes = {
  accentColor: PropTypes.string,
  textColor: PropTypes.string,
  theme: PropTypes.string
}

const mapStateToProps = state => ({
  accentColor: state.organization.accent_color,
  textColor: state.organization.text_color,
  borderRadius: state.organization.button_border_radius,
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  languages: state.organization.languages,
  activeLanguageCodeFilter: state.dashboard.activeLanguageCodeFilter
})

const mapDispatchToProps = dispatch => ({
  setLanguageCodeFilter: bindActionCreators(setLanguageCodeFilter, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(LanguageFilter)
