import React from 'react'
import { useNavigate } from "react-router-dom"
import Button from './button'

const CallToActionButton = ({ cta }) => {
  const navigate = useNavigate()
  return (<Button
    key={cta._id}
    className={`cs-button csod-cta-button`}
    onClick={() => {
      if (cta.type === 'link') {
        if (cta.url.startsWith('#')) {
          navigate(cta.url)
        }
        else if (cta.opens_in_new_tab) {
          window.open(cta.url, '_blank')
        }
        else {
          window.location.href = cta.url
        }
      }
      else {
        navigate(`/cta/${cta._id}`)
      }
    }}>
    <span className='csod-cta-button-text-container'>
      {cta.name}
    </span>
  </Button>
  )
}
export default CallToActionButton
