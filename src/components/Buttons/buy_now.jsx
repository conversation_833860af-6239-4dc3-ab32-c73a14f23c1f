import React from 'react'
import Button from './button'
import { connect } from 'react-redux'
import { withTranslation } from 'react-i18next'
import { bindActionCreators, compose } from 'redux'
import { useNavigate } from "react-router-dom"
import { toggleModal } from '/src/redux/payments/api'

const BuyNowButton = ({ asset, toggleModal, t, organization, user, small = false }) => {
  const navigate = useNavigate()
  return (<>
    {asset.asset_price && !asset.is_playable &&
      <Button
        key={asset._id}
        disabled={asset.is_purchased}
        className={`cs-button csod-cta-button ${small ? 'small' : ''}`}
        onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          if (!user) {
            return navigate('/login')
          }
          toggleModal(asset)
        }}>
        <span className="csod-cta-button-text-container">
          {asset.is_purchased ? t('asset.purchased') : asset.publish_at && !asset.is_playable ?
            t('asset.preorderNowFor', { assetPrice: asset.asset_price.price })
            :
            t('asset.watchNowFor', { assetPrice: asset.asset_price.price })
          }
        </span>
      </Button>
    }
  </>)
}
const mapDispatchToProps = dispatch => ({
  toggleModal: bindActionCreators(toggleModal, dispatch)
})

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization
})

export default compose(withTranslation(), connect(mapStateToProps, mapDispatchToProps))(BuyNowButton)
