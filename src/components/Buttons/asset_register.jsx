import React from 'react'
import Button from './button'
import { connect } from 'react-redux'
import { withTranslation } from 'react-i18next'
import { bindActionCreators, compose } from 'redux'
import { toggleModal } from '/src/redux/payments/api'
import { assetRegister } from 'redux/dashboard/api'

const AssetRegisterButton = ({ asset, t, assetRegister, status }) =>
  <>
    <Button
      key={asset._id}
      disabled={asset.is_registered || status === 'PENDING'}
      className={`cs-button csod-register-button`}
      onClick={async e => {
        e.preventDefault()
        e.stopPropagation()
        assetRegister({ asset_id: asset._id })
      }}>
      <span className="csod-register-button-text-container">
        {status === 'PENDING'
          ? t('asset.assetRegisterPending')
          : asset.is_registered
            ? t('asset.assetRegistered')
            : t('asset.assetRegister')}
      </span>
    </Button>
  </>

const mapDispatchToProps = dispatch => ({
  toggleModal: bindActionCreators(toggleModal, dispatch),
  assetRegister: bindActionCreators(assetRegister, dispatch)
})

const mapStateToProps = state => ({
  user: state.auth.user,
  status: state.dashboard.register_status
})

export default compose(withTranslation(), connect(mapStateToProps, mapDispatchToProps))(AssetRegisterButton)
