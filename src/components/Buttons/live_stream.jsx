import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from "react-router-dom"
import DateTimeCounter from '../DateTimeCounter'
import Button from './button'

const LiveButton = ({ asset, small = false, parentVoucher = null, authenticated = false }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { voucherID } = useParams()
  const liveLink = voucherID ? '/live/' + asset._id + '/' + voucherID : '/live/' + asset._id
  const { counterText, isLiveActive } = DateTimeCounter({ asset })
  // does not acccount for authenticated user who may not have access to the stream.
  const userHasAccessToAsset = asset.enable_public_playback || parentVoucher || authenticated
  return (
    <Button
      className={`cs-button csod-live-button ${isLiveActive ? '' : 'disabled'}`}
      disabled={!isLiveActive || !userHasAccessToAsset}
      onClick={() => {
        navigate(liveLink)
      }}>
      <span className='csod-live-button-text-container'>
        {asset.live_stream_event_title &&
          <span className='csod-live-button-event-title'>
            {`${asset.live_stream_event_title}:`}&nbsp;
          </span>}
        <span className='csod-live-button-text'>{t("buttons.live")}</span>
        {counterText && <span>&nbsp;{counterText}</span>}
      </span>
    </Button>
  )
}

export default LiveButton
