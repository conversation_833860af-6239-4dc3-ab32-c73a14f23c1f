import React from "react"
import { connect } from "react-redux"
import styled from '@emotion/styled'
import useIsMobile from "hooks/use_is_mobile"

const Button = styled.button`
  width: ${props => props.width};
  ${props => props.isMobile ? 'min-width: auto;' : ''}
  color: ${props => props.textColor};
  background: rgba(0,0,0,0.2);
  border-color: ${props => props.main ? `${props.accentColor}bf` : 'rgba(255,255,255,0.2)'};
  border-radius: ${props => props.borderRadius}px;
  :hover {
    background: ${props => props.main ? `${props.accentColor}3f` : 'rgba(0,0,0,0.2)'};
    border-color: ${props => props.main ? props.accentColor : 'rgba(255,255,255,0.8)'};
  }
  :focus {
    background: ${props => props.main ? `${props.accentColor}3f` : 'rgba(0,0,0,0.2)'};
    border-color: ${props => props.main ? props.accentColor : 'rgba(255,255,255,0.8)'};
  }
`

const BrandedButton = ({ organization, onClick, className, children, disabled = false, width = 'max-content', main = false }) => {
  const { isMobile } = useIsMobile()
  return (
    <Button
      main={main}
      disabled={disabled}
      accentColor={organization.accent_color}
      // textColor={organization.text_color}
      borderRadius={organization.button_border_radius}
      width={width}
      isMobile={isMobile}
      className={`cs-button csod-button ${className}`}
      onClick={onClick}>
      {children}
    </Button>
  )
}


const mapStateToProps = state => ({
  organization: state.organization
})

export default connect(mapStateToProps)(BrandedButton)
