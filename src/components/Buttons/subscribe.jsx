import React from 'react'
import { connect } from 'react-redux'
import { withTranslation } from 'react-i18next'
import { bindActionCreators, compose } from 'redux'
import { toggleModal } from '/src/redux/payments/api'
import Button from './button'

const SubscribeButton = ({ subscription, toggleModal, t }) =>
  <>
     {subscription.price &&
    <Button
      key={subscription._id}
      className={`cs-button csod-cta-button`}
      onClick={e => {
        e.preventDefault()
        e.stopPropagation()
        toggleModal(subscription, true)
      }}>
      <span className="csod-cta-button-text-container">
        {t('subscription.subscribeNow')}
      </span>
    </Button>
    }
  </>

const mapDispatchToProps = dispatch => ({
  toggleModal: bindActionCreators(toggleModal, dispatch)
})

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization
})

export default compose(withTranslation(), connect(mapStateToProps, mapDispatchToProps))(SubscribeButton)