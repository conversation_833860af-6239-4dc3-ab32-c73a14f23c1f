import React from "react"
import { useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import Button from './button'

const TrailerButton = ({ asset }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  return !!asset.trailer_id ? (
    <Button
      className={`cs-button csod-play-trailer-button`}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
        if (asset.voucher_id) {
          navigate(`/${asset.voucher_id}/assets/${asset.trailer_id}?autoPlay=true`)
        }
        else {
          navigate(`/play?assetID=${asset.trailer_id}&autoPlay=true`)
        }
      }}>
      <span className='csod-watch-trailer-button-text'>{t("buttons.watchTrailer")}</span>
    </Button>
  ) : null
}


export default TrailerButton
