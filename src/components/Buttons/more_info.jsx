import React from "react"
import { useNavigate } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import Button from './button'

const MoreInfoButton = ({ asset, small = false, className = "" }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  return (
    <Button
      className={`${className} cs-button csod-more-info-button ${small ? "small" : ""}`}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
        navigate(`/view/${asset.friendly_url_alias || asset._id}`)
      }}>
      <span>{t("buttons.moreInfo")}</span>
    </Button>
  )
}

export default MoreInfoButton
