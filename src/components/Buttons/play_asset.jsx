import React, { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { connect } from "react-redux"
import { useTranslation } from "react-i18next"
import { clickPlay } from "/src/helpers/click_play"
import { isAssetAvailable } from "/src/helpers/is_asset_available"
import Button from "./button"

const PlayButton = ({
  asset,
  small = false,
  latestPlaybackVideoID,
  parentVoucher = null,
  parentSeries = null,
  setActive = null,
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [playButtonActive, setPlayButtonActive] = useState(
    isAssetAvailable(asset, parentVoucher, parentSeries)
  )

  const availableAt = asset.publish_at || asset.timed_event_starts_at

  const pollCurrentDate = () => {
    // If we have now reached the playable_at or timed_event_starts_at datetime (plus a 5 second buffer), let's trust the value and set the asset to available.
    var date = new Date().getTime() - 5000
    if (date >= Date.parse(availableAt)) {
      setPlayButtonActive(true)
      if (typeof setActive === "function") {
        setActive()
      }
    } else {
      // Poll every 10 seconds if we haven't reached the new time yet.
      setTimeout(() => {
        pollCurrentDate()
      }, 10000)
    }
  }

  useEffect(() => {
    // Start polling on the current date if this will be available soon.
    if (availableAt && !playButtonActive && !asset.hide_play_button) {
      pollCurrentDate()
    }
  }, [])

  return (
    <Button
      main={true}
      className={`cs-button csod-play-button ${small ? "small" : ""}`}
      disabled={!playButtonActive}
      onClick={(e) => {
        if (!playButtonActive) return
        e.stopPropagation()
        e.preventDefault()
        clickPlay({
          navigate,
          asset,
          parentVoucher,
          parentSeries,
          latestPlaybackVideoID,
          clickType: "button",
        })
      }}
    >
      {playButtonActive ? (
        <span className="csod-play-button-text">{t("buttons.play")}</span>
      ) : (
        <span className="csod-not-available-button-text">
          {t("buttons.notAvailable")}
        </span>
      )}
    </Button>
  )
}

const mapStateToProps = (state) => ({
  organization: state.organization,
  latestPlaybackVideoID: state.session.latestPlaybackVideoID,
})

export default connect(mapStateToProps)(PlayButton)
