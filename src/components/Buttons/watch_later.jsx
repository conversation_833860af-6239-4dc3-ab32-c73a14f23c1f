import React from "react"
import { connect } from "react-redux"
import { bindActionCreators } from "redux"
import { useTranslation } from "react-i18next"
import styled from "@emotion/styled"
import { MdAddCircleOutline, MdRemoveCircleOutline } from "react-icons/md"
import { addAssetToList, removeAssetFromList } from "/src/api/dashboard"
import Button from "./button"

const Icon = styled.span`
  position: absolute;
  top: 8px;
`

const ListButton = ({
  asset,
  addAssetToList,
  removeAssetFromList,
  loading,
  myList
}) => {
  const assetIsInList =
    myList.filter(group => group.list.filter(myListAsset => myListAsset._id === asset._id).length > 0).length > 0

  const { t } = useTranslation()

  return (
    <Button
      key={asset._id}
      disabled={loading}
      className={`cs-button csod-add-to-list-button`}
      style={{ minWidth: "170px" }}
      onClick={() => {
        if (assetIsInList) {
          removeAssetFromList(asset.type, asset._id)
        } else {
          addAssetToList(asset.type, asset._id)
        }
      }}
    >
      <span className="csod-watch-later-text flex items-center nowrap">
        <Icon className="csod-watch-later-icon">
          {assetIsInList ? (
            <MdRemoveCircleOutline size={"1.5em"} />
          ) : (
            <MdAddCircleOutline size={"1.5em"} />
          )}
        </Icon>
        {loading ? (
          assetIsInList ? (
            <span className="csod-watch-later-removing-text pl3">
              {t("buttons.removingFromWatchLater")}
            </span>
          ) : (
            <span className="csod-watch-later-adding-text pl3">
              {t("buttons.addingToWatchLater")}
            </span>
          )
        ) : (
          <span
            className={`csod-watch-later-button-text ${
              assetIsInList ? "csod-watch-later-remove" : "csod-watch-later-add"
            } pl3`}
          >
            {t("buttons.watchLater")}
          </span>
        )}
      </span>
    </Button>
  )
}

const mapStateToProps = (state) => ({
  loading: state.dashboard.mylist.isAddingOrRemoving,
  organization: state.organization,
  myList: state.dashboard.mylist.items,
})

const mapDispatchToProps = (dispatch) => ({
  addAssetToList: bindActionCreators(addAssetToList, dispatch),
  removeAssetFromList: bindActionCreators(removeAssetFromList, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(ListButton)
