import React, { useState } from "react"
import { useTranslation } from 'react-i18next'
import { connect } from "react-redux"
import Button from './button'
import MainDetails from "components/AssetDetails/main_details"
import { Modal } from "@bitcine/cinesend-theme"

const MoreInfoModalButton = ({ asset, parentSeries, organization, className }) => {
  const [showModal, setShowModal] = useState(null)
  const { t } = useTranslation()
  return (
    <Button
      className={`csod-more-info-button small ${className}`}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
        setShowModal(true)
      }}>
      <span>{t("buttons.moreInfo")}</span>
      {showModal &&
        <Modal 
          className='csod-more-info-modal'
          header={parentSeries.title}
          onClose={() => setShowModal(false)}>
          <MainDetails
            parentSeries={parentSeries}
            asset={{
              ...asset,
              type: 'video'
            }}
            isModal/>
        </Modal>}
    </Button>
  )
}

const mapStateToProps = state => ({
  organization: state.organization
})

export default connect(mapStateToProps)(MoreInfoModalButton)
