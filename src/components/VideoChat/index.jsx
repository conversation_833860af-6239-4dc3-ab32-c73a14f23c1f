import React from 'react'
import PropTypes from 'prop-types'
import { connect } from "react-redux"
//import { ZoomMtg } from '@zoomus/websdk'
//import config from '/src/config'

// NOTE: Zoom currently not working, so let's just open a link.
const VideoChat = ({ videochat }) => {
  // useEffect(() => {
  //   ZoomMtg.init({
  //     leaveUrl: videochat.leaveUrl,
  //     isSupportAV: true,
  //     success: () => {
  //       ZoomMtg.join({
  //         ...videochat.config,
  //         success: () => {
  //           console.log('success!')
  //         },
  //         error: res => {
  //           console.log(res)
  //         }
  //       })
  //     }
  //   })
  // }, [])
  return (
    <div>Zoom Meeting?</div>
  )
}

VideoChat.propTypes = {
  videochat: PropTypes.object
}

const mapStateToProps = state => ({
  videochat: state.videochat
})

export default connect(mapStateToProps)(VideoChat)
