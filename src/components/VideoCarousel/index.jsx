import React, { useEffect, useRef, useState } from 'react'

import { 
  calcItemWidth, 
  determineSlidesToShow,
  getButtonWidth,
  getSlidePadding,
  getWindowWidth,
  shouldShowPrevButton,
  shouldShowNextButton
} from './utils'

import {
  OuterTrack,
  InnerTrack,
  List,
  Item,
  PrevButton,
  NextButton,
  CarouselDots
} from './components'
import {connect} from "react-redux";
import useIsMobile from 'hooks/use_is_mobile';

const TIMEOUT = 10000

const VideoCarousel = ({
  children,
  displayStyle,
  displaySize,
  initialSlide,
  centerContent = false,
  isCategoryView = null, 
  constrainWidth = null,
  isCarousel = false,
  isSpotlight = false,
  isSwimlane = true,
  backgroundColor,
  foregroundColor,
  accentColor,
  textColor
}) => {
  const { isMobile } = useIsMobile()
  const [slidesToShow, setSlidesToShow] = useState(isCarousel ? 1 : 4)
  const [trackWidth, setTrackWidth] = useState(constrainWidth || 0)
  const [slideIdx, setSlideIdx] = useState(0)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const trackRef = useRef(null)
  const scrollRef = useRef(null)
  
  const setItemParams = () => {
    if (trackRef && trackRef.current) {
      setTrackWidth(trackRef.current.offsetWidth)
    }
    setSlidesToShow(determineSlidesToShow(displayStyle, displaySize, constrainWidth, isMobile))
  }

  const isMobileCarousel = isCarousel && isMobile
  let buttonWidth = getButtonWidth()
  if (isMobileCarousel) {
    buttonWidth = 0
  }
  const itemWidth = calcItemWidth(trackWidth - (buttonWidth * 2), slidesToShow)

  const windowWidth = getWindowWidth()
  const isConstrained = windowWidth > constrainWidth

  // Only show preview thumbnails if we are not being constrained and it's a swimlane.
  const showPreviewThumbnails = !isConstrained && isSwimlane
  const usePadding = showPreviewThumbnails || isMobileCarousel
  const outerPadding = usePadding ? 0 : buttonWidth
  const innerPadding = usePadding ? buttonWidth : 0
  const arrowColor = (isCarousel || !isConstrained) ? 'white' : (textColor || foregroundColor)

  const setNewScrollPosition = position => {
    if (scrollRef.current) {
      scrollRef.current.scrollLeft = position
    }
  }

  const onButtonClick = difference => {
    setNewScrollPosition(scrollPosition + difference)
  }

  const onSlideSelect = slideIndex => {
    setNewScrollPosition(slideIndex * itemWidth)
  }

  const onScroll = () => {
    if (scrollRef.current) {
      if (scrollRef.current.scrollLeft !== scrollPosition) {
        setScrollPosition(scrollRef.current.scrollLeft)
      }
      const index = Math.floor(scrollRef.current.scrollLeft / itemWidth)
      setSlideIdx(index)
    }
  }

  useEffect(() => {
    setScrollPosition(initialSlide * itemWidth)
    window.addEventListener("resize", setItemParams)
    return () => {
      window.removeEventListener("resize", setItemParams)
    }
  }, [trackRef])

  useEffect(() => {
    setItemParams()
  }, [windowWidth])

  // CAROUSEL EFFECTS
  useEffect(() => {
    const interval = setInterval(() => {
      if (isCarousel) {
        setIsTransitioning(true)
        setTimeout(() => {
          const reset = ((slideIdx + 1 ) / children.length) > 1
          setTimeout(() => {
            setIsTransitioning(false)
          }, reset ? 1000 : 500)
          onSlideSelect((slideIdx + 1) % children.length)
        }, 1000)
      }
    }, TIMEOUT)
    return () => clearInterval(interval)
  }, [children, slideIdx, isCarousel])

  // SCROLL LISTENER
  useEffect(() => {
    const scrollRefElement = scrollRef.current
    if (scrollRefElement) {
      scrollRefElement.addEventListener("scroll", onScroll)
      return () => {
        scrollRefElement.removeEventListener("scroll", onScroll)
      };
    }
  }, [])

  const unfilledRow = children.length < slidesToShow

  return (
    <OuterTrack
      className={`csod-category-row ${unfilledRow ? 'csod-unfilled-row' : ''} ${centerContent ? 'justify-center' : ''}`} 
      ref={trackRef}
      paddingX={outerPadding}
      // paddingBottom={isCarousel ? 64 : 0}
      paddingBottom={0}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}>
      {!isMobile && <PrevButton
        arrowColor={arrowColor}
        backgroundColor={backgroundColor}
        visible={shouldShowPrevButton(scrollPosition, itemWidth)}
        width={buttonWidth} 
        onClick={() => onButtonClick(-itemWidth)}
        padding={getSlidePadding()}
        isCategoryView={isCategoryView}/>}
      {!isMobile && <NextButton
        arrowColor={arrowColor}
        visible={shouldShowNextButton(scrollPosition, itemWidth, slidesToShow, children.length)}
        backgroundColor={backgroundColor}
        width={buttonWidth} 
        onClick={() => onButtonClick(itemWidth)}
        padding={getSlidePadding()}
        isCategoryView={isCategoryView}/>}
      <InnerTrack
        className='csod-category-row-inner-track snap-x snap-mandatory'
        ref={scrollRef}
        padding={innerPadding}
        isCarousel={isCarousel}
        isMobile={isMobile}>
        <List
          useTransition={!isCarousel}
          className='csod-category-row-list'>
          {children.map((child, i) => 
            <Item
              key={'inner_' + child.key + '_' + i}
              width={itemWidth}
              padding={isCarousel ? 0 : getSlidePadding()}
              className={`
                csod-category-item csod-category-item-asset-id-${child.props?.asset?._id}
                transition-opacity duration-1000 ${isTransitioning ? 'opacity-0' : ''}
                snap-start ${isCarousel ? 'snap-always' : ''}
                ${isMobile && !isCarousel && !isSpotlight ? 'scroll-mx-4' : ''}
                ${!isConstrained && !isMobile && !isCarousel && !isSpotlight ? 'scroll-mx-16' : ''}
              `}>
              {child}
            </Item>
          )}
        </List>
      </InnerTrack>
      {isCarousel &&
        <CarouselDots
          slides={children}
          activeIndex={slideIdx}
          accentColor={accentColor}
          onChange={slideIdx => onSlideSelect(slideIdx)}/>}
    </OuterTrack>
  )
}

VideoCarousel.defaultProps = {
  displayStyle: 'thumbnail',
  displaySize: 'medium',
  initialSlide: 0
}

const mapStateToProps = state => ({
  backgroundColor: state.organization.background_color,
  foregroundColor: state.organization.foreground_color,
  accentColor: state.organization.accent_color,
  constrainWidth: parseInt(state.organization.constrain_page_width)
})

export default connect(mapStateToProps)(VideoCarousel)