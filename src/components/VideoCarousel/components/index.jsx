import React from 'react'
import styled from '@emotion/styled'
import { MdKeyboardArrowLeft, MdKeyboardArrowRight } from 'react-icons/md'
import { Icon } from '@bitcine/cinesend-theme'

const OuterTrack = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  padding-left: ${props => props.paddingX}px;
  padding-right: ${props => props.paddingX}px;
  padding-bottom: ${props => props.paddingBottom}px;
  ${props => props.backgroundColor ? `background-color: ${props.backgroundColor};` : null}
  /* Hide scrollbar for Chrome, Safari and Opera */
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
`

const InnerTrack = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  scroll-behavior: smooth;
  overflow-x: scroll;
  padding-left: ${props => props.padding}px;
  padding-right: ${props => props.padding}px;
  ${props => props.isCarousel && props.isMobile ? 'margin-bottom: 24px;' : ''}
  /* Hide scrollbar for Chrome, Safari and Opera */
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  ${props => !props.isCarousel ? 'overflow: scroll;' : ''}
`

const List = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;
  position: relative;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  
  overflow: unset; 
`

const Item = styled.li`
  transform-style: preserve-3d;
  backface-visibility: hidden;
  flex: 1 1 auto;
  position: relative;
  width: ${props => props.width}px;
  padding: ${props => props.padding}px;
  overflow: hidden;
`

const ScrollButton = styled.button`
  z-index: 2;
  display: block;
  opacity: 1;
  width: ${props => props.width}px;
  position: absolute;
  top: ${props => props.padding}px;
  bottom: ${props => props.padding}px;
  margin-top: 0px;
  border: none;
  outline: none;
  cursor: pointer;
  transition: opacity 300ms;
  font-size: 14px;
  background: transparent;

  svg {
    position: absolute;
    top: ${props => props.isCategoryView ? '65%' : '50%'};
    left: 50%;
    transform: translate(-50%, -90%);
    transition: all 300ms;
    opacity: 0.40;
  }

  &:hover {
    svg {
      opacity: 1;
    }
  }
  
  color: ${props => props.arrowColor};
  ${props => props.dir === 'prev' ? `
    left: 0;
    // ${props.useBlackHover && `
    //   background: linear-gradient(90deg, black 20%, rgba(0,0,0,0) 100%);
    //   &:hover {
    //     svg {
    //       transform: translate(-60%, -90%);
    //     }
    //   }`}
    // ` : `
    right: 0;
    // ${props.useBlackHover && `
    //   background: linear-gradient(90deg, rgba(0,0,0,0) 20%, black 100%);
    //   &:hover {
    //     svg {
    //       transform: translate(-40%, -90%);
    //     }
    //   }`}
  `};
`

const isBlack = color => {
  return ["#000000", "black"].includes(color) || !color
}

const PrevButton = ({ visible, arrowColor, onClick, width, padding, isCategoryView, backgroundColor }) => {
  return !visible ? null : (
    <ScrollButton 
      className='csod-category-prev-button'
      dir='prev'
      arrowColor={arrowColor}
      useBlackHover={isBlack(backgroundColor)}
      width={width}
      onClick={onClick}
      padding={padding} 
      visible
      isCategoryView={isCategoryView}>
      <MdKeyboardArrowLeft size={'4em'}/>
    </ScrollButton>
  )
}

const NextButton = ({ visible, arrowColor, onClick, width, theme, padding, isCategoryView, backgroundColor }) => {
  return !visible ? null : (
    <ScrollButton 
      className='csod-category-next-button'
      dir='next'
      arrowColor={arrowColor}
      useBlackHover={isBlack(backgroundColor)}
      width={width}
      onClick={onClick}
      theme={theme}
      padding={padding} 
      visible
      isCategoryView={isCategoryView}>
      <MdKeyboardArrowRight size={'4em'}/>
    </ScrollButton>
  )
}

const CarouselDots = ({ slides, activeIndex, accentColor, onChange }) => {
  return (
    <div className='absolute bottom-0 left-0 right-0 pb-4 opacity-70 flex items-center justify-center space-x-2'>
      {slides.map((slide, index) => 
        <Icon
          key={index}
          icon='circle'
          style={{ color: activeIndex === index ? accentColor : null }}
          className={`
            text-white text-2xs cursor-pointer hover:opacity-100 hover:scale-125 transition-all
            ${activeIndex !== index ? 'opacity-70' : ''}
          `}
          onClick={() => onChange(index)}/>
      )}
    </div>
  )
}
export {
  OuterTrack,
  InnerTrack,
  List,
  Item,
  PrevButton,
  NextButton,
  CarouselDots
}