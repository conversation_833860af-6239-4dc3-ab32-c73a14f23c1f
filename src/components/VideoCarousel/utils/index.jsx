import React from 'react'

// Adjust these values to change the width
// of the next or previous slides preview
const SLIDE_PREVIEW_WIDTH_DESKTOP = 58
const SLIDE_PREVIEW_WIDTH_MOBILE = 16
const SLIDE_PADDING_DESKTOP = 4
const SLIDE_PADDING_MOBILE = 2
const MO<PERSON>LE_WIDTH = 768

// Adjust these values to change the width of posters, thumbnails, and squares, of each size
const SMALL_SIZE = 300
const MEDIUM_SIZE = 360
const LARGE_SIZE = 420
const EXTRA_LARGE_SIZE = 480
const EXTRA_EXTRA_LARGE_SIZE = 540
const THUMBNAIL_MULTIPLIER = 1
const POSTER_MULTIPLIER = 1 / 2
const SQUARE_MULTIPLIER = 2 / 3

// Let's say 1080 is the perfect width for this stuff
const SCALE_VALUE = 1800

const cloneChildren = (children) => {
  const childArray = React.Children.toArray(children)
  return childArray
}

const calcItemWidth = (trackWidth, itemsToShow, floor = false) => {
  let gapWidth = getSlidePadding() * (itemsToShow - 1)
  if (floor) {
    return Math.floor(trackWidth / itemsToShow - gapWidth)
  }
  return Math.round(trackWidth / itemsToShow - gapWidth)
}

const getWindowWidth = () => {
  return window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
}

const getItemWidth = (displayStyle, displaySize, windowWidth) => {
  const size = displaySize === 'extra_extra_large'
    ? EXTRA_EXTRA_LARGE_SIZE
    : displaySize === 'extra_large'
      ? EXTRA_LARGE_SIZE : displaySize === 'large'
        ? LARGE_SIZE : displaySize === 'small'
          ? SMALL_SIZE : MEDIUM_SIZE
  const multiplier = displayStyle === 'square'
    ? SQUARE_MULTIPLIER : displayStyle === 'poster'
      ? POSTER_MULTIPLIER : THUMBNAIL_MULTIPLIER
  const scaleValue = getScaleValue(windowWidth)
  return size * multiplier * scaleValue
}

const getScaleValue = windowWidth => {
  // When we look at bigger widths it's okay for content to be bigger. So let's apply a scaling value.
  if (windowWidth > SCALE_VALUE) {
    return windowWidth / SCALE_VALUE
  }
  return 1
}

const determineSlidesToShow = (displayStyle, displaySize, constrainWidth = 0, isMobile = false) => {
  if (displayStyle === 'carousel' || displayStyle === 'spotlight') {
    return 1
  }
  if (isMobile) {
    return {
      thumbnail: 2,
      poster: 3,
      square: 3
    }[displayStyle] || 2
  }
  let windowWidth = getWindowWidth()
  if (constrainWidth && constrainWidth < windowWidth) {
    windowWidth = constrainWidth
  }
  const itemWidth = getItemWidth(displayStyle, displaySize, windowWidth)
  const flooredItemsToShow = Math.floor(windowWidth / itemWidth)
  return Math.max(flooredItemsToShow, 1)
}

const getButtonWidth = (multiplier = 1) => {
  if (getWindowWidth() <= MOBILE_WIDTH) return (SLIDE_PREVIEW_WIDTH_MOBILE * multiplier)
  return (SLIDE_PREVIEW_WIDTH_DESKTOP * multiplier)
}

const getSlidePadding = () => {
  if (getWindowWidth() <= MOBILE_WIDTH) return SLIDE_PADDING_MOBILE
  return SLIDE_PADDING_DESKTOP
}

const convertRemToPixels = (rem) => {
  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);
}

const shouldShowPrevButton = (scrollPosition, itemWidth) => {
  // when "scroll-mx-16" is applied to the parent it sets 4rem (64px) scroll margin
  const offsetDelta = convertRemToPixels(4);
  return scrollPosition > offsetDelta
}

const shouldShowNextButton = (scrollPosition, itemWidth, slidesToShow, itemsCount) => {
  const endPosition = (itemWidth * slidesToShow) + scrollPosition
  // Requires 6 px buffer
  return (endPosition - 6) <= ((itemsCount - 1) * itemWidth)
}

export {
  cloneChildren,
  calcItemWidth,
  determineSlidesToShow,
  getButtonWidth,
  getWindowWidth,
  getSlidePadding,
  shouldShowPrevButton,
  shouldShowNextButton
}