import { Status } from "@bitcine/cinesend-theme"
import VideoGrid from "components/VideoGrid"
import useIsMobile from "hooks/use_is_mobile"
import { useTranslation } from "react-i18next"

export default function GroupedVideoGrid({
  pending,
  error,
  assets,
  organization,
  header,
  headerClass,
  emptyClass = 'csod-dashboard-genre-empty',
  emptyTranslation = 'dashboard.noContentAvailable'
}) {
  const { isMobile } = useIsMobile()
  const { t } = useTranslation()
  const displayStyle = {
    videos: { title: 'videos', display_style: organization.video_display_style ?? 'thumbnail' },
    events: { title: 'events', display_style: organization.event_display_style ?? 'thumbnail' },
    albums: { title: 'albums', display_style: organization.audio_display_style ?? 'thumbnail' },
    playlists: { title: 'playlists', display_style: organization.playlist_display_style ?? 'thumbnail' },
    series: { title: 'series', display_style: organization.series_display_style ?? 'thumbnail' }
  }
  // Show title if multiple groups have lists
  const showTitle = Array.isArray(assets) ? assets.reduce((cur, group) => cur + (group.list.length > 0 ? 1 : 0), 0) > 1 : false
  return (
    <Status pending={pending} error={error} className={pending ? 'h-screen' : ''}>
      <div className={`${isMobile ? 'px2' : 'px4'} py2`}>
        <h3 className={`${headerClass} mb-2`}>{header}</h3>
        <div className='space-y-4'>
          {assets && assets.filter(group => group.list.length > 0).map((group, i) => (
            <div key={i}>
              {group.list && group.list.length > 0 && (
                <>
                  {showTitle && <div className='text-lg font-medium'>{t("assetTypes." + group.title)} ({group.list.length})</div>}
                  <VideoGrid videos={group.list} category={displayStyle[group.title.toLowerCase()]} />
                </>
              )}
            </div>
          ))}
          {assets && assets.every((group) => (group.list && group.list.length === 0)) && (
            <div className='flex w-full h-full items-center justify-center'>
              <div className={emptyClass}> {t(emptyTranslation)}</div>
            </div>
          )} 
        </div>
      </div>
    </Status>
  )
}