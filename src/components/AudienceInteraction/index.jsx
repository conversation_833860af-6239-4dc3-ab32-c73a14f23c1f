import React from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import NameWrapper from './components/name_wrapper'
import InteractionsWrapper from './components/interactions_wrapper'
import { authUser } from '/src/api/live'

const AudienceInteraction = ({ voucherID, chatUserID, username, identifier, asset, isMobile }) => 
  <div className='csod-audience-interactions-wrapper border border-gray-2 p2 rounded h-full col-12'>
    <NameWrapper
      voucherID={voucherID}
      username={username}
      includeAskQuestions={asset.is_ask_question_enabled}
      includeChat={asset.is_live_chat_enabled}>
      <InteractionsWrapper
        voucherID={voucherID}
        username={username}
        chatUserID={chatUserID}
        identifier={identifier}
        asset={asset}
        includeAskQuestions={chatUserID && asset.is_ask_question_enabled}
        includeChat={asset.is_live_chat_enabled}
        isMobile={isMobile}/>
    </NameWrapper>
  </div>

const mapStateToProps = (state, props) => {
  if (props.voucherID) {
    const video = state.session.landingDetails.video
    return {
      chatUserID: props.voucherID,
      identifier: video.voucher_code,
      username: video.live_interaction_username
    }
  }
  if (props.loggedIn) {
    return {
      chatUserID: state.auth.user._id,
      identifier: state.auth.user.email,
      username: state.auth.user.name
    }
  }

  return {
    username: "Guest"
  }
}

const mapDispatchToProps = dispatch => ({
  authUser: bindActionCreators(authUser, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(AudienceInteraction)
