import React, { useState } from 'react'
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { askQuestion } from '/src/api/live'

const InputContainer = styled.div`
  .cs-input {
    background: black;
    color: white;
  }
`

const AskButton = styled.button`
  min-width: 70px;
  // max-width: 60px;
  background: ${props => props.useLightTheme ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.2)'};
  color: ${props => props.useLightTheme ? 'black' : 'white'};
  border-color: ${props => props.accent}bf;
  :hover {
    background: ${props => props.accent}3f;
    border-color: ${props => props.accent};
  }
  :focus {
    background: none;
  }
`

const AskQuestion = ({ identifier, askQuestion, asset, organization, username, company }) => {
  const { t } = useTranslation()
  const [question, setQuestion] = useState("")
  const [isBusy, setIsBusy] = useState(false)
  return (
    <div className='csod-ask-a-question-wrapper flex items-center'>
      <InputContainer className='csod-ask-question-input-container flex-auto pr1'>
        <input 
          className='cs-input col-12 csod-ask-question-input' 
          placeholder={t("liveStream.typeQuestion")}
          value={question} 
          onChange={e => setQuestion(e.target.value)}
          disabled={isBusy}/>
      </InputContainer>
      <AskButton
        className={`cs-button center csod-ask-button`}
        useLightTheme={organization.use_light_theme}
        accent={organization.accent_color}
        disabled={isBusy || question.length === 0}
        onClick={() => {
          setIsBusy(true)
          askQuestion(asset._id, question, identifier, `${username}${company ? ` (${company})` : ''}`, () => {
            setIsBusy(false)
            setQuestion("")
          })
        }}>
        {t("liveStream.ask")}
       </AskButton>
    </div>
  )
}

const mapStateToProps = (state, props) => ({
  organization: state.organization
})

const mapDispatchToState = dispatch => ({
  askQuestion: bindActionCreators(askQuestion, dispatch)
})

export default connect(mapStateToProps, mapDispatchToState)(AskQuestion)
