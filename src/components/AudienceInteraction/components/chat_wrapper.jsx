import React, { useEffect, useState } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { StreamChat } from 'stream-chat'
import { Status } from '@bitcine/cinesend-theme'
import LiveChat from './chat'
import config from '/src/config'
import { authUser } from '/src/api/live'

const client = new StreamChat(process.env.VITE_GETSTREAM_KEY)

const ChatWrapper = ({ chatUserID, asset, identifier, authUser, username, isMobile, user, voucherID = null }) => {
  const [isReady, setIsReady] = useState(false)
  useEffect(() => {
    if (isReady) {
      return
    }

    if (user || voucherID) {
      authUser(chatUserID, token => {
        const chatUser = {
          id: chatUserID,
          name: username,
          identifier,
          assetID: asset._id
        }
        client.connectUser(chatUser, token).then(() => {
          setIsReady(true)
        })
      })
    } 
    else {
      client.connectAnonymousUser().then(() => {
        setIsReady(true)
      })
    }
    return () => client.disconnectUser()
  }, [])
  return (
    <Status pending={!isReady}>
      {isReady &&
        <LiveChat
          voucherID={voucherID}
          client={client}
          channelID={asset._id}
          isMobile={isMobile}/>}
    </Status>
  )
}

const mapStateToProps = (state, props) => ({
  user: state.auth.user
})

const mapDispatchToProps = dispatch => ({
  authUser: bindActionCreators(authUser, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(ChatWrapper)
