import React, { useEffect } from "react" 
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import { Status } from '@bitcine/cinesend-theme'
import { getQuestionsByAsker } from "/src/api/live"

const Questions = ({ asset, identifier, getQuestionsByAsker, questions }) => {
  const { t } = useTranslation()
  useEffect(() => {
    getQuestionsByAsker(asset._id, identifier)
  }, [asset, identifier, getQuestionsByAsker])
  return (
    <div className='csod-questions-wrapper flex-auto flex flex-column overflow-scroll p1 flex border border-gray-2 rounded mt1 mb2'>
      <Status pending={questions === null}>
        {questions && questions.length === 0 &&
          <div className='flex flex-column items-center justify-center p2 h-full'>
            <div className='material-icons' style={{ fontSize: '48px' }}>question_answer</div>
            {t("liveStream.noQuestionsAsked")}
          </div>
        }
        {
          questions && questions.map(question =>
            <div key={question._id} className='pb1'>
              <div className='muted'>{dayjs.utc(question.created_at).local().format('MMM D, YYYY HH:mm')}</div>
              <div>{question.value}</div>
            </div>)
        }
      </Status>
    </div>
  )
}

const mapStateToProps = state => ({
  questions: state.live_stream.asked_questions
})

const mapDispatchToProps = dispatch => ({
  getQuestionsByAsker: bindActionCreators(getQuestionsByAsker, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Questions)
