import React from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import Name from './name'
import { updateVoucherDetails } from '/src/api/live'

const NameWrapper = ({ username, voucherID = null, updateVoucherDetails, children, ...props }) =>
  username
    ? children
    : <Name {...props} onSubmit={(name) => updateVoucherDetails(voucherID, name)}/>

const mapDispatchToProps = dispatch => ({
  updateVoucherDetails: bindActionCreators(updateVoucherDetails, dispatch)
})

export default connect(null, mapDispatchToProps)(NameWrapper)
