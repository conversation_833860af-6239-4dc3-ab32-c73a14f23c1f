import React, { useState } from 'react'
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { Status } from '@bitcine/cinesend-theme'

const InputContainer = styled.div`
  .cs-input {
    background: black;
    color: white;
  }
`

const SubmitButton = styled.button`
  min-width: 70px;
  // max-width: 60px;
  background: ${props => props.useLightTheme ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.2)'};
  color: ${props => props.useLightTheme ? 'black' : 'white'};
  border-color: ${props => props.accent}bf;
  :hover {
    background: ${props => props.accent}3f;
    border-color: ${props => props.accent};
  }
  :focus {
    background: none;
  }
`

const Name = ({ onSubmit, organization, includeAskQuestions, includeChat }) => {
  const [name, setName] = useState('')
  const [company, setCompany] = useState('')
  const [isPending, setIsPending] = useState(false)
  const { t } = useTranslation()
  return (
    <Status pending={isPending}>
      <h4 className="csod-name-details-header mb2 pb1 border-bottom border-gray-2">{includeAskQuestions && includeChat
        ? t("liveStream.joinChatAndQuestions")
        : includeAskQuestions
          ? t("liveStream.joinQuestions")
          : includeChat
            ? t("liveStream.joinChat")
            : ''
      }</h4>
      <InputContainer className='csod-ask-question-input-container col-12'>
        <input 
          className='cs-input col-12 csod-live-chat-name-input' 
          placeholder={t("liveStream.yourName")}
          value={name} 
          onChange={e => setName(e.target.value)}/>
      </InputContainer>
      <InputContainer className='csod-ask-question-input-container-secondary col-12 mt2'>
        <input 
          className='cs-input col-12 csod-live-chat-secondary-input' 
          placeholder={t("liveStream.yourCompany")}
          value={company} 
          onChange={e => setCompany(e.target.value)}/>
      </InputContainer>
      <div className='csod-ask-button-container flex justify-end mt2'>
        <SubmitButton
          className={`cs-button csod-ask-button`}
          useLightTheme={organization.use_light_theme}
          accent={organization.accent_color}
          onClick={() => {
            setIsPending(true)
            let username = name + (company ? ` (${company})` : '')
            onSubmit(username)
          }}
          disabled={name.length === 0}>
          {t("liveStream.join")}
        </SubmitButton>
      </div>
    </Status>
  )
}

const mapStateToProps = (state) => ({
  organization: state.organization
})

export default connect(mapStateToProps)(Name)
