import React from 'react'
import { connect } from "react-redux"
import { Cha<PERSON>, Channel, Thread, Window } from 'stream-chat-react'
import { MessageList } from 'stream-chat-react'
import 'stream-chat-react/dist/css/index.css'
import styled from '@emotion/styled'
import ChatMessage from "./chat_message"
import ChatMessageInput from "./chat_message_input"

const Container = styled.div`
  // .str-chat-channel {
  //   max-height: 100%;
  // }
  ${props => props.isMobile ? `
    height: 300px;
  ` : `
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 56px;
    height: 100%;
  `}

  // If mobile cap at 400px. Otherwise, height of header
  .str-chat {
    height: 100%;
  }

  .str-chat__fileupload-wrapper {
    display: none;
  }

  // Disable kebab option for received messages
  div > div.str-chat__message-inner > div.str-chat__message-text > div.str-chat__message-simple__actions > div.str-chat__message-simple__actions__action.str-chat__message-simple__actions__action--options > svg {
    display: none;
  }

  // Enable kebab option for self messages
  div.str-chat__message--me > div.str-chat__message-inner > div.str-chat__message-text > div.str-chat__message-simple__actions > div.str-chat__message-simple__actions__action.str-chat__message-simple__actions__action--options > svg {
    display: block;
  }

  .str-chat__li--single {
    margin-bottom: 40px !important;
  }
  
  .str-chat__li--bottom {
    margin-bottom: 40px !important;
  }

  .str-chat.livestream.dark {
    background: #1a1a1a;
  }

  .dark.str-chat .str-chat__message-simple-text-inner {
    background: hsla(0,0%,100%,.05);
    color: #fff;
  }
`

const LiveChat = ({ client, channelID, theme, headerHeight, isMobile = false, loggedIn, voucherID = null }) => {
  const channel = client.channel('livestream', channelID)
  const messageActions = ['delete', 'edit', 'react', 'reply']
  const heightContent = 228 + headerHeight
 
  return (
    <Container isMobile={isMobile} heightContent={heightContent}>
      <Chat client={client} theme={'livestream ' + theme}>
        <Channel channel={channel}>
          <Window>
            <MessageList messageActions={messageActions} Message={() => <ChatMessage />}/>
            {(loggedIn || voucherID) && <ChatMessageInput />}
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </Container>
  )
}

const mapStateToProps = (state, props) => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  headerHeight: parseInt(state.organization.header_height),
  loggedIn: !!state.auth.user
})

export default connect(mapStateToProps)(LiveChat)