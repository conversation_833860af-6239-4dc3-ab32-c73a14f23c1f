import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import LiveChat from './chat_wrapper'
import AskQuestions from './ask_questions'

const QUESTIONS_TAB = 'questions_tab'
const LIVE_CHAT_TAB = 'live_chat_tab'

const InteractionsWrapper = ({
  includeAskQuestions,
  includeChat,
  ...props
}) => {
  const [tab, setTab] = useState(includeChat ? LIVE_CHAT_TAB : QUESTIONS_TAB)
  const { t } = useTranslation()
  return (
    <div className="csod-audience-interactions-inner-wrapper flex flex-column h-full relative">
      {includeAskQuestions && includeChat ? 
        <div className='csod-chat-tabs-wrapper p1 border border-gray-1 rounded flex items-center z2'>
          <div
            onClick={() => setTab(LIVE_CHAT_TAB)}
            className={`col-6 h-full flex items-center justify-center center rounded csod-ask-a-question-label
            ${tab === LIVE_CHAT_TAB ? 'bold bg-gray-1' : 'pointer'}`}>
            {t("liveStream.chatWithAttendees")}
          </div>
          <div
            onClick={() => setTab(QUESTIONS_TAB)}
            className={`col-6 h-full flex items-center justify-center center rounded csod-live-chat-label
            ${tab === QUESTIONS_TAB ? 'bold bg-gray-1' : 'pointer'}`}>
            {t("liveStream.askAQuestion")}
          </div>
        </div>
        : includeAskQuestions
          ? <h3 className='csod-ask-a-question-label border-bottom border-gray-2 mb1 pb1'>{t("liveStream.askAQuestion")}</h3>
          : <h3 className='csod-live-chat-label border-bottom border-gray-2 mb1 pb1'>{t("liveStream.chatWithAttendees")}</h3>
      }
      {includeAskQuestions && tab === QUESTIONS_TAB && <AskQuestions {...props}/>}
      {includeChat && tab === LIVE_CHAT_TAB && <LiveChat {...props}/>}
    </div>
  )
}

export default InteractionsWrapper
