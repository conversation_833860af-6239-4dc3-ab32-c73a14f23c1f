import React from "react"
import { connect } from "react-redux"
import styled from "styled-components"
import { Button } from '@bitcine/cinesend-theme'
import { use } from "i18next"
import useIsMobile from "hooks/use_is_mobile"
import { useLocation } from "react-router-dom"

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const ExternalLogin = ({ externalAuthentication, invalidLoginMessage, organization }) => {
  const { search } = useLocation() 
  const params = new URLSearchParams(search)
  const lang = params.get("lang") || params.get("language")
  let url = externalAuthentication.client_url
  if (lang) {
    url += `&state=lang=${lang}`
  }
  const { isMobile } = useIsMobile()
  if(window.location.pathname !== '/login') {
    localStorage.setItem('external_redirect_path', window.location.pathname)
  }
  return (
    <Card isMobile={isMobile} className={`${isMobile ? 'p3' : 'p4'} csod-login-card box-shadow mx-auto`}>
      <h4 style={{ textAlign: 'center' }} className='mb2 csod-external-login-header-text'>{externalAuthentication.header_text}</h4>
      <div className='mt2 center csod-external-login-button-container'>
        <Button
          style={{ background: organization.accent_color }}
          className='cs-button mx-auto csod-login-button csod-external-login-button'
          onClick={() => {
            if (true || window.confirm("Confirm redirect to: " + url)) {
              window.location.href = url
            }
          }}>
          <span className='csod-external-login-button-text'>{externalAuthentication.button_text}</span>
        </Button>
        {externalAuthentication.signup_url &&
          <>
            <span className='csod-or-text-container'>
              <span className='block csod-or-text my2'>OR</span>
            </span>
            <Button
              style={{ background: organization.accent_color }}
              className='cs-button mx-auto csod-signup-button csod-external-signup-button'
              onClick={() => {
                if (true || window.confirm("Confirm redirect to: " + url)) {
                  window.location.href = externalAuthentication.signup_url
                }
              }}>
              <span className='csod-external-signup-button-text'>Sign Up</span>
            </Button>
          </>}
        {invalidLoginMessage && <div className='small red bold mt2'>{invalidLoginMessage}</div>}
        <div className='csod-external-login-div-1'/>
      </div>
    </Card>
  )
}

const mapStateToProps = state => ({
  externalAuthentication: state.organization.external_authentication,
  invalidLoginMessage: state.auth.error,
  organization: state.organization
})

export default connect(mapStateToProps)(ExternalLogin)
