import { Icon, Status as StatusComponent } from "@bitcine/cinesend-theme"

export default function Status(props) {
  const error = props.error || props.empty
  if (error) {
    let errorMessage = props.errorMessage
      || props.emptyMessage?.message
      || 'An error occurred. Please contact customer support.'
    return (
      <div className='csod-error-wrapper h-screen w-full flex flex-col items-center justify-center space-y-4 text-small'>
        <Icon icon='error_outline' className='text-error-600 text-3xl'/>
        <div>{errorMessage}</div>
      </div>
    )
  }
  return <StatusComponent {...props}/>
}