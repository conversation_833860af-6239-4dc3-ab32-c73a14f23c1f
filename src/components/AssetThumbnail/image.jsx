import React from "react"
import styled from '@emotion/styled'
import useIsMobile from "hooks/use_is_mobile"

const ImageContainer = styled.div`
  width: 100%;
  
  ${props => props.aspectRatio ? `
    aspect-ratio: ${props.aspectRatio};
  ` : ``}
  padding-top: ${props => props.isFullWidthThumbnail ? props.paddingTop + '%' : props.useSquareImage ? '100%' : props.usePosterImage ? '150%' : '56.25%'};
  // transition: transform .3s ease-in-out;
  ${props => props.isSwimlane && `border-radius: 2px;`}
  background: url(${props => `"${props.url}"`});
  background-size: ${props => (props.usePosterImage || props.useSquareImage) ? '100% 100%' : props.isFullWidthThumbnail ? 'contain' : 'cover'}; 
  background-repeat: no-repeat;
  background-position: center;
  .overlay-hidden {
    ${props => props.isClickable ? 'display: none;' : ''}
  }
  ${props => props.isClickable ? `
    &:focus {
      outline: solid rgba(0,0,0,0.2);
    }
  ` : ''}
  &:hover, &:focus {
    .hover-action {
      display: inline-block;
    }
  }
  font-size: ${props => props.fontSize}px;
  font-weight: ${props => props.fontWeight};
  line-height: ${props => props.lineHeight}px;
`

const Image = ({ asset, category = {}, isClickable, children }) => {

  const { isMobile } = useIsMobile()

  // Defaults
  let usePosterImage = false
  let useSquareImage = false

  // Landscape images
  let landscape = ['thumbnail', 'carousel', 'spotlight']
  if (landscape.includes(category.display_style)) {
    usePosterImage = false
    useSquareImage = false
  }
  else if (category.use_poster_images || category.display_style === 'poster') {
    usePosterImage = true
  }
  else if (category.display_style === 'square') {
    useSquareImage = true
  }

  const fontDetails = getFontDetails(category)
  const isCarousel = category?.display_style === 'carousel'
  const isSpotlight = category?.display_style === 'spotlight'
  const isSwimlane = !isCarousel && !isSpotlight
  const isMobileCarousel = isMobile && isCarousel
  const isFullWidthThumbnail = category?.carousel_style === 'full_width_thumbnail'
  const [originWidth, originHeight] = category?.carousel_aspect_ratio?.includes('/') ? category?.carousel_aspect_ratio?.split('/') : [16, 9]

  return (
    <ImageContainer
      className={`
        csod-asset-thumbnail-image 
        
        ${isMobileCarousel ? 'absolute top-0 left-0 right-0' : 'relative'}
      `}
      aspectRatio={isCarousel ? category?.carousel_aspect_ratio : null}
      isSwimlane={isSwimlane}
      isMobileCarousel={isCarousel && isMobile}
      url={isFullWidthThumbnail ? asset.full_width_thumbnail_url : asset.thumbnail_url}
      useSquareImage={useSquareImage}
      usePosterImage={usePosterImage}
      fontSize={fontDetails.size}
      fontWeight={fontDetails.weight}
      lineHeight={fontDetails.height}
      isClickable={isClickable}
      isFullWidthThumbnail={isFullWidthThumbnail}
      paddingTop={originHeight / originWidth * 100}
    >
      {children}
    </ImageContainer >
  )
}

function getFontDetails(category) {
  if (category.display_style === 'carousel') {
    return {
      size: 22,
      weight: '500',
      height: 26
    }
  }
  if (category.row_size === 'large') {
    return {
      size: 18,
      weight: '500',
      height: 22
    }
  }
  if (category.row_size === 'small') {
    return {
      size: 10,
      weight: 'bold',
      height: 16
    }
  }
  return {
    size: 12,
    weight: 'bold',
    height: 20
  }
}

export default Image
