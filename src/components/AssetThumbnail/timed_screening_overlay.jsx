import React from "react"
import dayjs from 'dayjs'

const TimedScreeningOverlay = ({ asset }) =>
  asset.is_upcoming ?
    <span className={`block csod-timed-event-starts-at-text csod-timed-event-asset-id-${asset._id}`}>
      This timed screening begins on {dayjs.utc(asset.starts_at).local().format('MMM D, YYYY')} at {dayjs.utc(asset.starts_at).local().format('h:mma')}.
    </span> :
    <span className={`block csod-timed-event-ended-text csod-timed-event-asset-id-${asset._id}`}>
      This timed screening has ended.
    </span>


export default TimedScreeningOverlay
