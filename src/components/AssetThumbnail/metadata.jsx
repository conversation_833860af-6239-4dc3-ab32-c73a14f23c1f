import React from "react"
import { connect } from 'react-redux'
import parseHTML from "helpers/parse_html"
import { Poster } from "@bitcine/cinesend-theme"
import Button from "components/Buttons/button"
import styled from "@emotion/styled"
import { useTranslation } from "react-i18next"

const Wrapper = styled.div`
  ${props => `
    background-color: ${props.backgroundColor};
    color: ${props.textColor};
  `}
`

const Metadata = ({
  asset,
  extraDetails,
  isMobile = false,
  isTablet = false,
  showTitle = true,
  includePoster = false,
  parentSeries = null,
  isSwimlane = true,
  isCarousel = false,
  isSpotlight = false,
  backgroundColor,
  foregroundColor,
  useReadMore = false,
  onReadMoreClick = null
}) => {
  const { t } = useTranslation()
  return (
    <Wrapper
      backgroundColor={isSpotlight ? backgroundColor : null}
      textColor={isCarousel ? 'white' : isSpotlight ? foregroundColor: null}
      className={`
        h-full w-full flex
        ${isSpotlight && isMobile ? 'space-x-4' : 'flex-col'}
        ${isMobile ? '' : isCarousel ? 'justify-center' : ''}
        ${isMobile && isCarousel ? 'px2' : ''}
        ${isSpotlight
            ? 'csod-category-spotlight-metadata cursor-auto'
            : isCarousel
              ? 'csod-category-carousel-metadata'
              : 'csod-category-video-subheader-container'}
        ${isSpotlight ? 'p-6' : isCarousel ? 'pt-6' : ''}
        ${parentSeries ? 'csod-series-episode-subheader-container' : ''}`}>
      {includePoster && asset.poster_image_url &&
        <div className='w-1/3 mb-4'>
          <Poster url={asset.poster_image_url}/>
        </div>}
      <div className={`${!isSwimlane ? 'space-y-2 space-x-2' : ''}`}>
        {showTitle && <b 
          className={
            `csod-category-video-title max-w-md
            ${parentSeries ? 'csod-category-series-title' : 'csod-category-movie-title'}
            ${!isSwimlane ? 'text-2xl' : 'truncate'}
            ${isSpotlight && isTablet ? 'text-base' : ''}
          `}
          style={{ display: 'inline-block' }}>
          {asset.title}
        </b>}
        {asset.subheader && <small
          className={
            `block bold csod-category-video-subheader
            ${parentSeries ? '' : 'truncate'}
            ${!isSwimlane ? 'text-base' : ''}
          `}>
          {parseHTML(asset.subheader ?? "")}
        </small>}
        {parentSeries && asset.logline && <small
          className={
            `inline-block bold csod-category-video-subheader
            ${parentSeries ? '' : 'truncate'}
            ${!isSwimlane ? 'text-base' : ''}
          `}>
          {parseHTML(asset.logline ?? "")}
        </small>}
        {extraDetails && <small
          className={`block csod-category-video-description text-sm max-w-md`}>
          <div className={`
            ${isSpotlight && !isMobile  ? 'line-clamp-3' : ''}
            ${isCarousel && isMobile ? 'line-clamp-3' : ''}
            ${isTablet ? 'text-xs' : ''}
          `}>
            {parseHTML(extraDetails)}
          </div>
        </small>}
        {useReadMore && isMobile && isSpotlight && <Button className='mt-4' onClick={onReadMoreClick}>{t("buttons.readMore")}</Button>}
      </div>
      {useReadMore && !(isMobile && isSpotlight) && <Button className='mt-4' onClick={onReadMoreClick}>{t("buttons.readMore")}</Button>}
    </Wrapper>
  )
}


const mapStateToProps = state => ({
  backgroundColor: state.organization.background_color,
  foregroundColor: state.organization.foreground_color
})

export default connect(mapStateToProps)(Metadata)
