import React from "react"
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import { setUtilValue } from '/src/api/utils'
import { clearFilters } from '/src/api/search'
import { formatDateFromNow } from '/src/helpers/format_date'

const HeaderWrapper = styled.div`
  font-size: 2.5em;
  color: white;
  z-index: 2;
  position: absolute;
  top: 16px;
  left: 16px;
`

const EpisodeNumber = styled.div`

`

const PlayDisabled = styled.div`
  text-shadow: 0px 6px 8px rgba(25, 50, 47, 0.2);
`

const PlayableAt = styled.div`
  font-size: 12px;
  font-weight: bold;
  color: black;
  padding: 4px 12px 4px 12px;
  border-radius: 15px;
  background-color: white;
  box-shadow: 0px 6px 8px rgba(25, 50, 47, 0.2);
`

const HeaderOverlay = ({ asset, parentSeries = null, language }) => {
  const { t } = useTranslation()
  const episodeNumber = parentSeries && asset.episode_number && (parseInt(asset.episode_number) > 0)
  return (
    <HeaderWrapper className='flex items-center csod-asset-thumbnail-header-overlay space-x-2'>
      {!!episodeNumber &&
        <EpisodeNumber>
          {asset.episode_number}
        </EpisodeNumber>
      }
      {asset.is_geoblocked && <PlayDisabled className='csod-asset-play-disabled'>
        {/*Add unplayable icon for locked content*/}
        <i className="material-icons">vpn_lock</i>
      </PlayDisabled>}
      {asset.publish_at && <PlayableAt className='csod-asset-playable-at'>{formatDateFromNow(asset.publish_at, language, t)}</PlayableAt>}
    </HeaderWrapper>
  )
}

const mapStateToProps = state => ({
  playbackPositions: state.session.playbackPositions,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  setUtilValue: bindActionCreators(setUtilValue, dispatch),
  clearFilters: bindActionCreators(clearFilters, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(HeaderOverlay)
