import React from "react"
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import styled from '@emotion/styled'
import { setUtilValue } from '/src/api/utils'
import { clearFilters } from '/src/api/search'
import Image from './image'
import Description from './description'
import MoreInfoModalButton from "/src/components/Buttons/more_info_modal"
import BuyNowButton from '/src/components/Buttons/buy_now'
import PlayButton from '/src/components/Buttons/play_asset'
import HeaderOverlay from "./header_overlay"
import PlayOverlay from "./play_overlay"
import { assetTypes } from "helpers/constants"
import MoreInfo from "components/Buttons/more_info"
import useIsMobile from "hooks/use_is_mobile"

const ThumbnailWrapper = styled.div`
  position: relative;
  width: 100%;
  &:hover .csod-asset-play-disabled {
    display: none;
  }
  &:hover .csod-asset-playable-at {
    display: none;
  }
`

const Details = styled.div`
  position: absolute;
  top: 0px;
  height: 100%;
  width: 100%;
  z-index: 100;
  transition: transform .3s ease-in-out;
  color: white;
  ${({ darkOverlayOnHover }) => darkOverlayOnHover && `
    background-color: rgba(0,0,0,0.6);
  `}
`

const Play = styled.div`
  position: absolute;
  bottom: 8px;
  right: 8px;
`

const ProgressBar = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2;
`

const Progress = styled.div`
  display: block;
  height: 100%;
  background-color: ${props => props.accent};
  width: ${props => props.progress}%;
`

const Thumbnail = ({
  asset,
  category = {},
  isClickable,
  setIsClickable,
  parentVoucher = null,
  parentSeries = null,
  containerRef,
  organization,
  playbackPositions,
  addPlayButton = false
}) => {
  const { isMobile } = useIsMobile()
  let progress = false
  if (playbackPositions[asset._id] && asset.length_in_ms) {
    progress = Math.min(parseInt(playbackPositions[asset._id] / asset.length_in_ms * 100), 100)
  }
  const showSmallPlayButton = (category?.show_play_button_on_hover && asset.is_playable)
  
  const hoverDetailsEnabled = category?.hover_display_value !== 'none' || organization?.hover_details !== 'none'
  const darkOverlayOnHover = hoverDetailsEnabled || showSmallPlayButton

  return (
    <ThumbnailWrapper className='csod-asset-thumbnail h-full'>
      <HeaderOverlay asset={asset} parentSeries={parentSeries}/>
      {addPlayButton && <PlayOverlay/>}
      <Image asset={asset} category={category} organization={organization} isClickable={isClickable}>
        <Details darkOverlayOnHover={darkOverlayOnHover} className={`p1 csod-category-video-details overlay-hidden ${darkOverlayOnHover && 'hover-action'}`}>
          {!isMobile && <>
            {!parentSeries && <Description asset={asset} category={category} containerRef={containerRef}/>}
            {!asset.hide_play_button &&
              <Play>
                {!asset.is_playable && asset.is_purchasable && <BuyNowButton asset={asset} small/>}
                {parentSeries && <MoreInfoModalButton parentSeries={parentSeries} asset={asset} className={asset.is_playable ? 'mr1' : ''}/>}
                {asset.type === assetTypes.SERIES && <MoreInfo asset={asset} small className="mr-1 csod-series-more-info-button"/>}
                {showSmallPlayButton &&
                  <PlayButton
                    setActive={() => setIsClickable(true)}
                    asset={asset}
                    parentVoucher={parentVoucher}
                    parentSeries={parentSeries}
                    small/>}
              </Play>}
          </>}
        </Details>
        {!!progress &&
          <ProgressBar>
            <Progress
              accent={organization.accent_color}
              progress={progress}/>
          </ProgressBar>}
      </Image>
    </ThumbnailWrapper>
  )
}

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization,
  playbackPositions: state.session.playbackPositions,
})

const mapDispatchToProps = dispatch => ({
  setUtilValue: bindActionCreators(setUtilValue, dispatch),
  clearFilters: bindActionCreators(clearFilters, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(Thumbnail)
