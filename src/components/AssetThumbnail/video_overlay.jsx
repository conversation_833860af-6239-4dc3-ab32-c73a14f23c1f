import React from "react"
import { connect } from 'react-redux'
import { useTranslation } from 'react-i18next'
import parseHTML from "helpers/parse_html"
import styled from '@emotion/styled'
import { assetTypes } from '/src/helpers/constants'

const Truncate = styled.span`
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
`

const Bottom = styled.div`
  position: absolute;
  bottom: 8px;
  left: 8px;
`

const VideoOverlay = ({ asset, category, orgShowLogline, showDirector, showClickForMore, showNone }) => {
  const { t } = useTranslation()
  const isSeries = asset.type === assetTypes.SERIES
  const episodeNumber = isSeries && asset.episode_number && (parseInt(asset.episode_number) > 0)
  if (showNone) {
    return null
  }
  let showLogline = orgShowLogline && asset.logline
  let showSummary = orgShowLogline && asset.short_summary
  const hoverDisplayValue = category && category.hover_display_value ? category.hover_display_value : ""
  if (hoverDisplayValue) {
    showLogline = hoverDisplayValue.includes('logline')
    showSummary = hoverDisplayValue.includes('short_summary')
  }
  return (
    <>
      {showLogline ?
        <Truncate className={`csod-category-video-logline ${episodeNumber ? 'ml3' : ''}`}>{parseHTML(asset.logline)}</Truncate>
        : showSummary ?
          <Truncate className={`csod-category-video-short-summary ${episodeNumber ? 'ml3' : ''}`}>{parseHTML(asset.short_summary)}</Truncate>
          : showClickForMore ?
           <span className={`csod-category-video-click-for-more`}>{t('asset.clickForMore')}</span>
          : null}
      {isSeries && asset.description && <Truncate className={`csod-category-series-description`}>{parseHTML(asset.description)}</Truncate>}
      {showDirector && <Bottom className='col-8'>
        {asset.directors &&
          <div className='csod-category-video-directors'>
            {t("asset.director", {count: asset.directors.split(',').length })}: {asset.directors.replace(/,/g, ", ")}
          </div>
        }
      </Bottom>}
    </>
  )
}

const mapStateToProps = state => ({
  orgShowLogline: ['logline', 'logline_and_director'].includes(state.organization.hover_details),
  showDirector: state.organization.hover_details === 'logline_and_director',
  showClickForMore: state.organization.hover_details === 'click_for_more',
  showNone: state.organization.hover_details === 'none'
})

export default connect(mapStateToProps)(VideoOverlay)
