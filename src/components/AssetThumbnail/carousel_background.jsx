import styled from "@emotion/styled"
import useIsMobile from "hooks/use_is_mobile"
import React from "react"
import { connect } from "react-redux"

const CarouselContainer = styled.div`
  width: 100vw;
  left: calc(-50vw + 50%);
`

const Image = styled.div`
  background: url("${props => props.url}");
  background-repeat: no-repeat;
  background-size: 100% auto;
`

const CarouselBackground = ({ category, asset, constrainWidth }) => {
  const { isMobile } = useIsMobile()
  return (
    <CarouselContainer className='
      csod-carousel-backgrounds-container z-0 flex items-center justify-center
      absolute left-0 right-0 h-full bg-black
    '>
      <div
        className='csod-carousel-image-outer-container h-full relative'
        style={{ width: constrainWidth ? `${constrainWidth}px` : '100%' }}>
        <div
          className={`
            csod-carousel-image-inner-container relative
            ${isMobile ? '' : 'mx-16'}
          `}
          style={{ aspectRatio: category?.carousel_aspect_ratio }}
          >
          <Image
            className='csod-carousel-background-image
              absolute left-0 right-0 h-full'
            url={asset.thumbnail_url}
          />
          {!isMobile ? <>
            <div className='csod-carousel-background-overlay-left
              absolute left-0 right-2/3 h-full
              bg-gradient-to-r from-black to-transparent'/>
            <div className='csod-carousel-background-overlay-right
              absolute left-1/3 right-0 h-full
              bg-gradient-to-r from-transparent from-80% to-black'/>
            <div className='z-10 csod-carousel-background-overlay-bottom
              absolute left-0 right-0 h-full
              bg-gradient-to-t from-black to-20%'/>
          </> : <>
            <div className='z-10 csod-carousel-background-overlay-bottom
              absolute left-0 right-0 h-full
              bg-gradient-to-t from-black to-50%'/>
          </>}
        </div>
      </div>
    </CarouselContainer>
  )
}

const mapStateToProps = state => ({
  constrainWidth: state.organization.constrain_page_width,
})

export default connect(mapStateToProps)(CarouselBackground)
