import React from "react"
import { assetTypes } from '/src/helpers/constants'
import VideoOverlay from './video_overlay'
import PlaylistOverlay from './playlist_overlay'
import TimedScreeningOverlay from './timed_screening_overlay'
import EventOverlay from "./event_overlay";

const Description = ({ asset, category, containerRef }) => {
  if (asset.starts_at) {
    <TimedScreeningOverlay asset={asset}/>
  }
  if (category?.hover_display_value === 'none') {
    return null
  }
  return (
    asset.type === assetTypes.PLAYLIST ?
      <PlaylistOverlay asset={asset} category={category} containerRef={containerRef}/> :
      asset.type === assetTypes.EVENT ?
        <EventOverlay asset={asset} category={category} /> :
        <VideoOverlay asset={asset} category={category}/>
  )
}

export default Description
