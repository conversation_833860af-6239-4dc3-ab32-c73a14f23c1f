import React from "react"
import styled from '@emotion/styled'
import { Icon } from "@bitcine/cinesend-theme"
import useIsMobile from "hooks/use_is_mobile"

const Wrapper = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 10;
`

export default function PlayOverlay() {
  const { isMobile } = useIsMobile()
  return (
    <Wrapper className={`bg-black/50 flex items-center justify-center cursor-pointer group ${!isMobile ? 'opacity-0 hover:opacity-100' : ''}`}>
      <div className='border-gray-200 border-2 rounded-full flex items-center justify-center aspect-square h-20 w-20 group-hover:scale-110 transition-all'>
        <Icon icon='play_arrow' className='text-gray-200 text-5xl transition-all'/>
      </div>
    </Wrapper>
  )
}