import React from "react"
import styled from '@emotion/styled'
import parseHTML from "helpers/parse_html"
import { connect } from "react-redux"

const Truncate = styled.span`
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
`

const EventOverlay = ({ asset, category, showLogline, showDirector }) => {
  return (
    <>
      {showLogline && showDirector && asset.description &&
        <Truncate className={`csod-category-event-description`}>{parseHTML(asset.description)}</Truncate>
      }
    </>
  )
}

const mapStateToProps = state => ({
    showLogline: ['logline', 'logline_and_director'].includes(state.organization.hover_details),
    showDirector: state.organization.hover_details === 'logline_and_director',
})

export default connect(mapStateToProps)(EventOverlay)

