import React, { useState, useEffect } from "react"
import styled from '@emotion/styled'
import { assetTypes } from '/src/helpers/constants'
import parseHTML from "helpers/parse_html";
import {connect} from "react-redux";

const Truncate = styled.span`
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
`

const PlaylistOverlay = ({ asset, category, containerRef, showLogline, showDirector, }) => {
  const [sliceCount, setSliceCount] = useState(5)
  const parsePlaylistTitles = {
    displayGroup: asset.type === assetTypes.PLAYLIST ? asset.titles.slice(0, sliceCount) : [],
    remaindersGroup: asset.type === assetTypes.PLAYLIST ? asset.titles.slice(sliceCount) : []
  }
  useEffect(() => {
    if (containerRef && containerRef.current && asset.type === assetTypes.PLAYLIST) {
      const usableSpace = (containerRef.current.offsetHeight - 50)
      const segments = (Math.floor(usableSpace / 20) - 1)
      if (asset.titles.length - segments === 1) {
        setSliceCount(asset.titles.length)
      }
      else {
        setSliceCount(segments)
      }
    }
  }, [])
  return (
    <>
      {showLogline && showDirector && asset.description &&
        <Truncate className={`csod-category-playlist-short-summary max-w-md`}>{parseHTML(asset.description)}</Truncate>
      }
      {!showDirector &&
        <>
          {parsePlaylistTitles.displayGroup.map((title, i) =>
            <Truncate key={i} className='csod-category-playlist-video-title max-w-md'>{title.title}</Truncate>
          )}
          {parsePlaylistTitles.remaindersGroup.length > 0 && (
            <Truncate className='csod-category-playlist-n-more-notice'>...and {parsePlaylistTitles.remaindersGroup.length} more</Truncate>
          )}
        </>
      }
    </>
  )
};

const mapStateToProps = state => ({
  showLogline: ['logline', 'logline_and_director'].includes(state.organization.hover_details),
  showDirector: state.organization.hover_details === 'logline_and_director',
})

export default connect(mapStateToProps)(PlaylistOverlay)
