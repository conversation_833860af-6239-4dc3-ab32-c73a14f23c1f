import React, { useRef, useState } from "react"
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { useNavigate } from "react-router-dom"
import styled from '@emotion/styled'
import { clearFilters } from '/src/api/search'
import { clickPlay } from '/src/helpers/click_play'
import { isThumbnailClickable } from '/src/helpers/is_thumbnail_clickable'
import Thumbnail from './thumbnail'
import Metadata from './metadata'
import useIsMobile from "hooks/use_is_mobile"
import CarouselBackground from "./carousel_background"

const Container = styled.div`
  width: 100%;
  transition: transform .3s ease-in-out; 
  ${({ shouldGrow }) => shouldGrow && `
    &:hover, &:focus {
      transform: scale(1.02);
      backface-visibility: hidden;
      -webkit-font-smoothing: subpixel-antialiased;
    }
  `}
  ${props => props.aspectRatio ? `aspect-ratio: ${props.aspectRatio};` : ''}
 
`

const AssetThumbnail = (props) => {
  const { isMobile, isTablet } = useIsMobile()
  const navigate = useNavigate()
  const { asset, parentVoucher, parentSeries } = props
  const containerRef = useRef(null)
  const [isClickable, setIsClickable] = useState(isThumbnailClickable(asset, parentVoucher, parentSeries))
  const isSpotlight = props?.category?.display_style === "spotlight" && !props.fromGrid
  const isCarousel = props?.category?.display_style === "carousel" && !props.fromGrid
  const isSwimlane = !isSpotlight && !isCarousel
  const leftThumbnail = isSpotlight && props?.category?.carousel_style === "constrained_thumbnail_left"
  const rightThumbnail = isSpotlight && props?.category?.carousel_style === "constrained_thumbnail_right"
  const isFullWidthThumbnail = isSpotlight && props?.category?.carousel_style === "full_width_thumbnail"
  const hideTitle = !isSwimlane && !props?.category?.carousel_include_title
  const includePoster = isSpotlight && props?.category?.carousel_include_poster
  const useReadMore = !isSwimlane && props?.category?.carousel_include_more_details_button
  const addPlayButton = isSpotlight && props?.category?.carousel_bypass_details_page
  const extraDetails = !isSwimlane && props?.category?.carousel_include_logline ? props.asset.logline || props.asset.description : null
  function onClick(readMore = false) {
    if (!isClickable) {
      return
    }
    clearFilters()
    clickPlay({
      navigate,
      asset,
      parentSeries,
      parentVoucher,
      clickType: readMore || !addPlayButton ? 'thumbnail' : null
    })
  }
  const isHorizontalSpotlight = isSpotlight && !isMobile
  const isVerticalSpotlight = isSpotlight && isMobile
  const aspectRatio = isCarousel ? props?.category?.carousel_aspect_ratio : null

  return (
    <Container
      aspectRatio={aspectRatio}
      className={`hover:cursor-pointer csod-asset-thumbnail-container
        ${isMobile && isCarousel && !isFullWidthThumbnail ? 'min-h-[350px]' : ''}
        ${asset.is_bonus_content ? 'csod-bonus-content' : ''}
        overflow-clip h-full
                ${isSpotlight
          ? 'csod-asset-spotlight-container'
          : isCarousel
            ? 'csod-asset-carousel-container'
            : 'csod-asset-swimlane-container'}
        ${isHorizontalSpotlight
          ? (rightThumbnail ? 'flex flex-row-reverse' : 'flex')
          : isVerticalSpotlight
            ? 'flex flex-col h-full'
            : ''}
        ${isClickable ? 'csod-asset-published cursor-pointer' : 'csod-asset-unpublished'}
        csod-asset-type-${asset.type} csod-asset-id-${asset._id}
        ${Array.isArray(asset.tags) && asset.tags.map(tag => `csod-asset-tag-${tag.replace(/ /g, '')}`).join(' ')}
      `}
      ref={containerRef}
      isClickable={isClickable}
      shouldGrow={isSwimlane && !isMobile}
      onClick={() => onClick()}>
      <div className={`csod-asset-thumbnail-inner-container
        ${isCarousel ? 'absolute left-0 right-0 -z-10' : ''}
        ${isFullWidthThumbnail ? 'w-full' : isHorizontalSpotlight
          ? `w-2/3 h-full`
          : ''}`}>
        {isCarousel
          ? <CarouselBackground category={props?.category} asset={asset} />
          : <Thumbnail
            {...props}
            isCarousel={isCarousel}
            isClickable={isClickable}
            setIsClickable={setIsClickable}
            containerRef={containerRef}
            addPlayButton={addPlayButton} />}
      </div>
      {!isFullWidthThumbnail &&
        <div
          className={`csod-metadata-wrapper 
          ${isCarousel ? 'h-full' : ''} 
          ${isCarousel
              ? `${aspectRatio ? '' : 'min-h-96'}`
              : isHorizontalSpotlight
                ? `w-1/3`
                : isVerticalSpotlight
                  ? 'h-full'
                  : ''}
        ${isMobile && isCarousel ? 'pt-32' : ''}`}>
          <Metadata
            {...props}
            showTitle={!hideTitle}
            includePoster={includePoster}
            isMobile={isMobile}
            isTablet={isTablet}
            isSpotlight={isSpotlight}
            isCarousel={isCarousel}
            isSwimlane={isSwimlane}
            useReadMore={useReadMore}
            extraDetails={extraDetails}
            onReadMoreClick={e => {
              e.stopPropagation()
              e.preventDefault()
              onClick(true)
            }}
          />
        </div>
      }
    </Container>
  )
}

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization,
  playbackPositions: state.session.playbackPositions,
})

const mapDispatchToProps = dispatch => ({
  clearFilters: bindActionCreators(clearFilters, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(AssetThumbnail)