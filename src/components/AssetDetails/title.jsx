import React from 'react'
import styled from 'styled-components'
import { assetTypes } from 'helpers/constants'

const TitleLogo = styled.img`
  position: relative;
  width: 100%;
  max-width: 500px; 
  height: 100%;
`

const Title = ({ asset, isModal }) =>
  asset.title_logo_image_url
    ? <TitleLogo src={asset.title_logo_image_url} />
    : isModal
      ? <>
        <h4 className='csod-video-title'>{asset.title}</h4>
        {asset.type === assetTypes.ALBUM && <p className='csod-album-artist'>{asset.artist}</p>}
      </>
      : <>
        <h2 className='csod-video-title'>{asset.title}</h2>
        {asset.type === assetTypes.ALBUM && <p className='csod-album-artist'>{asset.artist}</p>}
      </>

export default Title