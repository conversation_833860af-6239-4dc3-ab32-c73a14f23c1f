import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { CSSTransition } from 'react-transition-group';
import styled, { css } from 'styled-components'
import { Container, Overlay, Image } from '../Background';
import SeriesGrid from '../SeriesGrid';
import BonusContent from '../BonusContent';
import TimedScreenings from '../TimedScreenings';
import BackButton from '../BackButton';
import MainDetails from './main_details';
import TrackList from './track_list'
import useIsMobile from "hooks/use_is_mobile";
import ArtBanner from "./art_banner";
import Status from "components/Status";

const sharedTransitionStyles = css`
  transition: opacity 1s ease-in-out;
  &.background-image-enter, &.background-image-appear {
    opacity: 0;
  }
  &.background-image-enter-active, &.background-image-exit-active {
    opacity: 1;
  }
  &.background-image-exit {
    opacity: 0;
  }
`;

const FadeBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  ${sharedTransitionStyles}
`;

const Fade = styled.div`
  width: 95%;
  transition: opacity 1.5s ease-in-out, transform 0.5s ease-in;
`;

const AssetDetails = ({ asset, parentVoucher, isRedirected, organization }) => {

  const [imagesLoadedCount, setImagesLoadedCount] = useState(false)
  const [readyToRender, setReadyToRender] = useState(false)

  const { isMobile } = useIsMobile()
  const defaultStyle = organization.base_style === 'full_screen_faded'
  const showBackButton = !parentVoucher && !isRedirected
  const backgroundImageURL = asset.background_image_url || asset.thumbnail_url
  const isAlbumAndNotDefaultStyle = asset.type === 'album' && !defaultStyle
  const showDetailsBackButton = showBackButton && (defaultStyle || asset.type === 'album') && !isMobile

  // All possible images:
  const showArtBanner = !isAlbumAndNotDefaultStyle && !defaultStyle && backgroundImageURL
  const showAlbum = asset.type === 'album' && asset.thumbnail_url && organization.show_poster
  const showPoster = asset.type !== 'album' && asset.poster_image_url && organization.show_poster

  // Count how many are true:
  const imagesCount = [showArtBanner, showAlbum, showPoster].filter(opt => opt).length

  // Keep count of loaded images, when all are loaded we can render :)
  const onImageLoad = () => {
    const newImagesLoaded = imagesLoadedCount + 1
    setImagesLoadedCount(newImagesLoaded)
    if (newImagesLoaded >= imagesCount) {
      setReadyToRender(true)
    }
  }

  useEffect(() => {
    // After 1 second, let's set all items as loaded. This will render the placeholder images if necessary.
    setTimeout(() => {
      setReadyToRender(true)
    }, 1000)
  }, [])

  return (
    <div className={``}>
      <div
        style={{ backgroundColor: organization.background_color }}
        className={`pointer-events-none absolute inset-0 z-10 transition-opacity duration-300 ease-in ${readyToRender ? 'opacity-0' : 'opacity-100'}`}/>
      {showArtBanner && (
        <ArtBanner organization={organization} showBackButton={showBackButton} url={asset.background_image_url} onLoad={onImageLoad}/>
      )}
      <div className={`relative csod-video-details-page csod-asset-type-${asset.type} csod-asset-id-${asset._id} `
        + `${Array.isArray(asset.tags) && asset.tags.map(tag => `csod-asset-tag-${tag.replace(/ /g, '')}`).join(' ')}`}>
        {defaultStyle && <CSSTransition
          in={true}
          timeout={1000}
          appear={true}
          classNames="background-image"
        >
          <FadeBackground>
            <Container>
              <Overlay />
              <Image url={backgroundImageURL} active={true}/>
            </Container>
          </FadeBackground>
        </CSSTransition>}
        <>
          {showDetailsBackButton && <BackButton title="Back to browse" top={'1.75rem'}/>}
          <CSSTransition
            in={true}
            appear={true}
            timeout={500}
            classNames="video-details"
          >
            <Fade>
              <MainDetails
                asset={asset}
                parentVoucher={parentVoucher}
                showAlbum={showAlbum}
                showPoster={showPoster}
                onImageLoad={onImageLoad}
                className={`${isMobile ? 'p2' : 'px4 py3'}`}/>
            </Fade>
          </CSSTransition>
        </>
      </div>
      {asset.type === 'tvseries' && (
        <SeriesGrid series={asset} parentVoucher={parentVoucher} />
      )}
      {asset.timed_screenings && asset.timed_screenings.length > 0 && (
        <TimedScreenings event={asset} parentVoucher={parentVoucher} />
      )}
      {asset.bonus_content && asset.bonus_content.length > 0 && (
        <BonusContent asset={asset} parentVoucher={parentVoucher} />
      )}
      {asset.type === 'album' && (
        <TrackList album={asset} parentVoucher={parentVoucher} />
      )}
    </div>
  );
};

AssetDetails.propTypes = {
  asset: PropTypes.object.isRequired,
  loaded: PropTypes.bool,
  parentVoucher: PropTypes.object,
  isRedirected: PropTypes.bool,
  organization: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  organization: state.organization,
  isRedirected: state.dashboard.redirectTo,
});

export default connect(mapStateToProps)(AssetDetails);
