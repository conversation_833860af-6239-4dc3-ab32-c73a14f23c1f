import React from "react"
import PropTypes from "prop-types"
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'

const Container = styled.div`
  position: relative;
`

const Metadata = ({ asset, settings }) => {
  const { t } = useTranslation()
  return (
    <Container>
      <div className='my2 csod-video-details'>
        {asset.directors &&
          <div>{t("asset.director", {count: asset.directors.split(',').length })}: <b>{asset.directors.replace(/,/g, ", ")}</b></div>}
        {asset.producers &&
          <div>{t("asset.producer", {count: asset.producers.split(',').length })}: <b>{asset.producers.replace(/,/g, ", ")}</b></div>}
        {asset.custom_fields && asset.custom_fields.length > 0 && (
          <>
            {asset.custom_fields.map((field, i) =>
              <div key={i}>
                <span className={`csod-custom-field-key csod-custom-field-key-${String(field.label).replace(/\s+/g, '-').toLowerCase()}`}>
                  {field.label}:{" "}
                </span>
                <b className={`csod-custom-field-value csod-custom-field-value-${String(field.value).replace(/\s+/g, '-').toLowerCase()}`}>
                  {
                    field.type === 'boolean' ?
                      <>{field.value === 1 ? t("common.yes") : t("common.no")}</> :
                      <>{field.value}</>
                  }
                </b>
              </div>
            )}
          </>
        )}
        {asset.languages && asset.languages.length > 0 &&
          <div>
            {asset.languages.split(",").length === 1 ? t("asset.language") : t("asset.languages")}: <b>{asset.languages.replace(/,/g, ", ")}</b>
          </div>
        }
        {asset.subtitles && asset.subtitles.length > 0 &&
          <div>
            {asset.subtitles.split(",").length === 1 ? t("asset.subtitle") : t("asset.subtitles")}: <b>{asset.subtitles.replace(/,/g, ", ")}</b>
          </div>
        }
      </div>
    </Container>
  )
}

Metadata.propTypes = {
  asset: PropTypes.object
}

export default Metadata
