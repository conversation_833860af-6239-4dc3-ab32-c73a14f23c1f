import React, { useEffect } from "react"
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import styled from '@emotion/styled'
import Title from './title'
import Subheader from './subheader'
import Metadata from './metadata'
import ShortSummary from './short_summary'
import Buttons from './buttons'
import PresentedBy from './presented_by'
import { getPlaybackPositions } from '/src/api/session'
import ImageLoader from "components/ImageLoader"

const Container = styled.div`
  height: 100%;
  color: ${props => props.textColor};
`

const Details = styled.div`
  position: relative;
  height: 100%;
`

const ImageContainer = ({ imageUrl, type, className = 'w-full', imageClassName = "", onLoad }) => (
  <div className={`${className} relative lg:max-w-60 mr-4 lg:mr-8 ${type !== 'album' ? 'aspect-[2/3]' : 'aspect-square'}`}>
    <ImageLoader
      className={`${imageClassName} w-full h-full object-cover absolute top-0 left-0`}
      url={imageUrl}
      alt='poster'
      onLoad={onLoad}/>
  </div>
);

const MainDetails = ({
  asset,
  organization,
  parentSeries = null,
  parentVoucher = null,
  getPlaybackPositions,
  className = null,
  textColor = null,
  isModal = false,
  showAlbum = false,
  showPoster = false,
  onImageLoad
}) => {
  useEffect(() => {
    if (asset.type === 'tvseries') {
      let assetIDs = [];
      asset.seasons.forEach(season => {
        season.episodes.forEach(episode => {
          assetIDs.push(episode._id);
        });
      });
      getPlaybackPositions(assetIDs.join(','));
    } else {
      getPlaybackPositions(asset._id);
    }
  }, [asset, getPlaybackPositions]);

  return (
    <Container className={`flex lg:flex-wrap items-start ${className} csod-main-details-container`} textColor={textColor}>
      {showAlbum ? (
        <ImageContainer
          className='w-1/3 csod-album-image-container'
          imageUrl={asset.thumbnail_url}
          type={asset.type}
          onLoad={onImageLoad}
          imageClassName='csod-album-image'
        />
      ) : (
        showPoster && (
          <ImageContainer
            className='w-1/3 csod-poster-image-container'
            imageUrl={asset.poster_image_url}
            type={asset.type}
            onLoad={onImageLoad}
            imageClassName='csod-poster-image'
          />
        )
      )}
      <div className={`h-full ${showAlbum || showPoster ? 'w-2/3' : 'w-full'}`}>
        <Details className='w-full'>
          {organization.asset_details_order.map(section => {
            switch(section) {
              case 'title':
                return <Title key={section} asset={asset} isModal={isModal}/>
              case 'subheader':
                return <Subheader key={section} asset={asset}/>
              case 'metadata':
                return <Metadata key={section} asset={asset}/>
              case 'short_summary':
                return <ShortSummary key={section} asset={asset} isModal={isModal}/>
              case 'buttons':
                return <div key={section}>
                  <Buttons asset={asset} parentSeries={parentSeries} parentVoucher={parentVoucher} organization={organization} isModal={isModal} />
                </div>
              case 'presented_by':
                return <PresentedBy key={section} asset={asset}/>
              default:
                return null
            }
          })}
        </Details>
      </div>
    </Container>
  );
};


MainDetails.propTypes = {
  asset: PropTypes.object,
  organization: PropTypes.object
}

const mapStateToProps = state => ({
  organization: state.organization
})

const mapDispatchToProps = dispatch => ({
  getPlaybackPositions: bindActionCreators(getPlaybackPositions, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(MainDetails)
