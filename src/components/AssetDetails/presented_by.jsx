import React from 'react'
import styled from '@emotion/styled'

const Container = styled.div`
  width: 80%;
  max-width: 280px;
  height: auto;
`

const Logo = styled.img`
  width: 100%;
  height: auto;
`

const PresentedBy = ({ asset }) => asset.presented_by_image_url ? 
  <Container className='mt2'>
    {asset.presented_by_image_link && asset.presented_by_image_link.length > 0 ? (
      <a href={asset.presented_by_image_link} target='_blank' rel='noopener noreferrer'>
        <Logo src={asset.presented_by_image_url}/>
      </a>
    ) : (
      <Logo src={asset.presented_by_image_url}/>
    )}
  </Container> : null

export default PresentedBy