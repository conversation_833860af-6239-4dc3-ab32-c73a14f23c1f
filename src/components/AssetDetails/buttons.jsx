import React from "react"
import styled from '@emotion/styled'
import { connect } from 'react-redux'
import PlayButton from '../Buttons/play_asset'
import TrailerButton from '../Buttons/watch_trailer'
import WatchLaterButton from '../Buttons/watch_later'
import LiveButton from '../Buttons/live_stream'
import CallToActionButton from '../Buttons/call_to_action'
import Ratings from '../Ratings'
import AccessDetails from './access_details'
import BuyNowButton from '../Buttons/buy_now'
import AssetRegister from "components/Buttons/asset_register"

const ButtonContainer = styled.div`
  flex-wrap: wrap;

  .csod-button {
    margin-bottom: 8px;
    margin-right: 8px;
  }
`

const Buttons = ({ asset, parentSeries = null, parentVoucher, organization, authenticated, isModal = false }) => {
  const enableRatings = (authenticated || parentVoucher) && !asset.ratings_on_assets_enabled && !asset.hide_ratings_button && !parentSeries
  const enableWatchLater = authenticated && organization.lists_enabled && !parentVoucher
  return (
    <>
      <ButtonContainer className="csod-buttons-list mt2">
        {asset.is_registerable && <AssetRegister asset={asset} />}
        {(asset.is_purchasable || (asset.is_purchased && !asset.is_playable)) && <BuyNowButton asset={asset} />}
        {asset.is_playable && !asset.hide_play_button && <PlayButton asset={asset} parentSeries={parentSeries} parentVoucher={parentVoucher} />}
        {!!asset.trailer_id && <TrailerButton asset={asset} />}
        {enableWatchLater && !isModal && <WatchLaterButton asset={asset} />}
        {!!asset.live_mux_id && <LiveButton asset={asset} parentVoucher={parentVoucher} authenticated={authenticated} />}
        {asset.call_to_actions ? asset.call_to_actions.map((cta, i) => <CallToActionButton key={i} cta={cta} />) : null}
        {enableRatings && <Ratings asset={asset} />}
      </ButtonContainer>
      <AccessDetails asset={asset} />
    </>
  )
}

const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED'
})
const mapDispatchToProps = dispatch => ({})

export default connect(mapStateToProps, mapDispatchToProps)(Buttons)
