import React from "react"
import { useTranslation } from 'react-i18next'
import { MdClosedCaption } from 'react-icons/md'
import parseHTML from "helpers/parse_html"

function msToTime(duration) {
  let
    seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
  hours = (hours < 10) ? "0" + hours : hours
  minutes = (minutes < 10) ? "0" + minutes : minutes
  seconds = (seconds < 10) ? "0" + seconds : seconds
  return hours + ":" + minutes + ":" + seconds
}

const Subheader = ({ asset }) => {
  const { t } = useTranslation()
  const subheader = asset.subheader || ''
  return (
    <>
      <strong className='mb1 csod-video-subheader align-baseline whitespace-pre'>
        {asset.closed_captions_enabled ? <MdClosedCaption className='h2 align-bottom'/> : '' } {parseHTML(subheader)}
      </strong>
      {asset.type === 'playlist' && asset.show_playlist_runtime &&
        <div className='csod-playlist-duration'>
          {t("playlist.duration")}: <strong>{msToTime(asset.playlist_runtime)}</strong>
        </div>
      }
    </>
  )
}

export default Subheader
