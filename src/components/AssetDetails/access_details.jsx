import React from "react"
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import PropTypes from "prop-types"
import dayjs from 'dayjs'
import { formatDate } from '/src/helpers/format_date'
import { assetTypes } from "helpers/constants"

const AccessDetails = ({ asset, language }) => {
  const { t } = useTranslation()
  return <>
    {!asset.hide_play_button && !asset.timed_event_starts_at && <small className='block muted csod-expiry-notice mt1'>
      {asset.expires_at ?
        dayjs(asset.expires_at).isBefore(dayjs()) ?
          `${t("buttons.expired")}` :
          `${t("buttons.youHaveUntil")} ${formatDate(asset.expires_at, language, t)} ${t("buttons.toWatchThisContent")}` :
          asset.hours_until_expiry ? `${t("buttons.youHave")} ${asset.hours_until_expiry} ${t("buttons.hoursToWatchThisContentFromWhenYouFirstClickPlay")}` :
        null
      }
    </small>}
    {asset.publish_at && !asset.is_playable &&
      <small className='block muted csod-publish-at-text mt1'>
        {t("buttons.thisContentWillBeAvailableOn")} {formatDate(asset.publish_at, language, t)}.
      </small>}
    {asset.timed_event_starts_at &&
      <small className='block muted csod-timed-event-starts-at-text mt1'>
        {t("buttons.thisIsATimedEventAndWillBeginOn")} {formatDate(asset.timed_event_starts_at, language, t)}.
        </small>}
    {/* {!asset.is_available && asset.type === assetTypes.VIDEO && 
      <small className='block muted csod-unavailable-content mt1'>
        {t("buttons.thisContentIsNotAvailable")}.
        </small>} */}
    {asset.is_geoblocked &&
      <small className='block muted csod-geoblocked-content mt1'>
        {t("buttons.thisContentIsGeoblocked")}.
        </small>}
  </>
}

AccessDetails.propTypes = {
  asset: PropTypes.object
}

const mapStateToProps = state => ({
  language: state.dashboard.language
})

export default connect(mapStateToProps)(AccessDetails)
