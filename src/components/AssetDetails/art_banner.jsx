import styled from "@emotion/styled"
import useIsMobile from "hooks/use_is_mobile"
import BackButton from '../BackButton'
import ImageLoader from "components/ImageLoader";

const BackButtonContainer = styled.div`
  position: absolute;
  top: 0;
  left: 6;
`;

const BannerContainer = styled.div`
  width: 100%;
  ${props => !props.maintainAspectRatio ? 'max-height: 40vh;' : ''}
  overflow: hidden;
  position: relative;
  padding-top: 16px;
  img {
    width: 100%;
    height: auto;
    display: block; 
    object-fit: cover;
  }
`;


const ArtBanner = ({ organization, showBackButton, url, onLoad }) => {
  const { isMobile } = useIsMobile()
  const maintainAspectRatio = organization.maintain_art_aspect_ratio
  return (
    <BannerContainer
      className={`${isMobile ? 'px2' : 'px4'} csod-banner-image-container flex`}
      maintainAspectRatio={maintainAspectRatio}>
      {showBackButton && (
        <BackButtonContainer>
          <BackButton title="Back to browse" />
        </BackButtonContainer>
      )}
      <ImageLoader url={url} maintainAspectRatio={maintainAspectRatio} onLoad={onLoad} alt={"Background"}/>
    </BannerContainer>
  )
}

export default ArtBanner