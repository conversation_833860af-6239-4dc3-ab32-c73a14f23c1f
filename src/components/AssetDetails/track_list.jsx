import React from 'react'
import { connect } from "react-redux"
import { Table } from '@bitcine/cinesend-theme'
import useIsMobile from 'hooks/use_is_mobile'

const TrackList = ({ album, theme }) => {
  const { isMobile } = useIsMobile()
  if (!album.track_list || album.track_list.length === 0) {
    return null 
  }
  const showLength = album.track_list.filter(opt => !!opt.duration).length > 0
  return (
  <div className={`csod-tracklist-container ${isMobile ? 'px2' : 'px-16'}`}>
    <Table
      widths={showLength ? [40, 85, 85, 85] : [40, 85, 85]}
      header={{
        columns: [
          { text: '#', key: '' },
          { text: 'Title', key: '' },
          { text: 'Artist', key: '' },
          ...showLength ? [{ text: 'Length', key: '' }] : []
        ],
      }}
      body={{
        data: album.track_list, 
        row: {
          compact: true,
          render: [
            data => data.number,
            data => data.name,
            data => data.artist,
            ...showLength ? [data => data.duration] : []
          ]
        }
      }}
    />
    <div className='csod-tracklist-appended-container' />
  </div>
  )
}


const mapStateToProps = state => ({
  organization: state.organization,
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps)(TrackList)
