import React from "react"
import PropTypes from "prop-types"
import { useTranslation } from 'react-i18next'
import { connect } from 'react-redux'
import Ratings from '../Ratings'
import { assetTypes } from '/src/helpers/constants'
import parseHTML from "helpers/parse_html"

const ShortSummary = ({ asset, authenticated, isModal }) => {
  const { t } = useTranslation()
  let summary = asset.short_summary || asset.description
  if (asset.type === 'playlist' && !asset.show_description) {
    summary = null
  }

  return (
    <>
      {summary && <div className={`mt1 bold csod-video-summary ${!isModal ? 'max-w-md' : ''}`}>
        {/* {ReactHtmlParser(summary,  {
          transform: function (domNode) {
            if (domNode.type === 'script') {
              var script = document.createElement('script');
              if (domNode.children?.length) {
                script.innerHTML = domNode?.children[0]?.data
              }
              if (domNode.attribs.src) {
                script.src = domNode.attribs.src;
              }
              if (domNode.attribs.defer) {
                script.defer = domNode.attribs.defer;
              }
              document.head.appendChild(script);
            }
          }
        })} */}
        {parseHTML(summary)}
      </div>}
      {asset.titles && asset.type === assetTypes.PLAYLIST &&
        <div className='mt1 csod-playlist-summary max-w-md'>
          <h4 className='mb2 csod-playlist-includes-label'>{t("playlist.thisPlaylistIncludes")}:</h4>
          {asset.titles.map(title =>
            <div className='mb2 csod-playlist-video-title-container' key={title._id}>
              <div className='csod-playlist-video-title bold'>{title.title}</div>
              <div className='csod-playlist-video-logline'>{parseHTML(title.logline)}</div>
              {authenticated && asset.ratings_on_assets_enabled && (
                <div style={{ marginTop: '-12px', marginLeft: '-2px' }} className='mb3'>
                  <Ratings asset={title} fromPlaylist={true} />
                </div>
              )}
            </div>
          )}
        </div>
      }
    </>
  )
}

ShortSummary.propTypes = {
  asset: PropTypes.object,
}
const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED',
})
const mapDispatchToProps = dispatch => ({})

export default connect(mapStateToProps, mapDispatchToProps)(ShortSummary)
