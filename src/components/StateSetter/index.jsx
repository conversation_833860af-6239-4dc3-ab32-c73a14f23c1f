import React, { useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { setFilterTagIDs, setFilterGenreID, repopulateDefaultTags } from 'api/dashboard'
import { useLocation } from 'react-router-dom'

const StateSetter = ({
  organization,
  setFilterGenreID,
  setFilterTagIDs,
  repopulateDefaultTags
}) => {
  const location = useLocation()

  // HANDLE URL PARAMETERS
  let urlParams = new URLSearchParams(location.search)
  const tagIDs = urlParams.get("tagIDs")
  const genreID = urlParams.get("genreID")

  // SET IN STATE IF EXISTS
  useEffect(() => {
    if (tagIDs) {
      setFilterTagIDs(tagIDs)
      // After setting partial tags from URL, repopulate missing defaults
      if (organization && organization.tag_filters) {
        repopulateDefaultTags(organization)
      }
    }
  }, [tagIDs, setFilterTagIDs, organization, repopulateDefaultTags])

  useEffect(() => {
    if (genreID) {
      setFilterGenreID(genreID)
    }
  }, [genreID, setFilterGenreID])

  return (
    <div />
  )
}

const mapStateToProps = state => ({
  user: state.auth.user,
  organization: state.organization,
})

const mapDispatchToProps = dispatch => ({
  setFilterTagIDs: bindActionCreators(setFilterTagIDs, dispatch),
  setFilterGenreID: bindActionCreators(setFilterGenreID, dispatch),
  repopulateDefaultTags: bindActionCreators(repopulateDefaultTags, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(StateSetter)
