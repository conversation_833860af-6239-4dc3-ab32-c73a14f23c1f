import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import styled from '@emotion/styled'
import VideoCarousel from '/src/components/VideoCarousel'
import { getButtonWidth, getSlidePadding } from '/src/components/VideoCarousel/utils'
import Category from './category'
import LanguageFilter from '/src/components/LanguageFilter'

const Container = styled.div`
  position: relative;
  z-index: 2;
  width: 100%;
`
const CategoriesRow = ({ categories, theme, headerY, carouselY }) => {
  return categories.length > 0 ? (
    <Container className={`csod-category-container ${carouselY}`}>
      <div className='csod-category-row-header-container flex justify-between'>
        <h4
          className={`csod-category-list-title csod-category-title ${headerY}`}
          style={{ paddingLeft: (getButtonWidth()+getSlidePadding())+'px' }}>
          Genres
        </h4>
        <LanguageFilter/>
      </div>
      <VideoCarousel theme={theme} isCategoryView={true}>
        {categories.map(category => <Category category={category} key={category._id}/>)}
      </VideoCarousel>
    </Container>
  ) : null
}

CategoriesRow.propTypes = {
  idx: PropTypes.number,
  organization: PropTypes.object,
  theme: PropTypes.string
}

const carouselSpacingMap = {
  'compact': '',
  'standard': 'my2',
  'spacious': 'my3'
}

const headerSpacingMap = {
  'compact': '',
  'standard': 'mt2',
  'spacious': 'mt3'
}

const mapStateToProps = state => ({
  categories: state.dashboard.categories.list,
  organization: state.organization,
  theme: state.organization.use_light_theme ? 'light' : 'dark',
  headerY: headerSpacingMap[state.organization.category_spacing],
  carouselY: carouselSpacingMap[state.organization.category_spacing]
})

export default connect(mapStateToProps)(CategoriesRow)
