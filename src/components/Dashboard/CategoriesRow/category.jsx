import React from "react"
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { useNavigate } from "react-router-dom"
import styled from '@emotion/styled'
import { clearFilters } from '/src/api/search'

const Container = styled.div`
  display: inline-block;
  width: 100%;
`

const Thumbnail = styled.div`
  position: relative;
  width: 100%;
`

const Image = styled.div`
  position: relative;
  width: 100%;
  height: 0px;
  padding-top: ${props => props.usePosterImage ? '150%' : '56.25%'};
  transition: transform .3s ease-in-out;
  border-radius: 2px;
  cursor: pointer;
  background: url(${props => props.url});
  background-size: cover;
  .hover-action {
    display: none;
  }
  &:hover, &:focus {
    transform: scale(1.02);
    .hover-action {
      display: inline-block;
    }
  }
  &:focus {
    outline: solid rgba(0,0,0,0.2);
  }
`

const Content = styled.div`
  position: absolute;
  top: 0px;
  height: 100%;
  width: 100%;
  transition: transform .3s ease-in-out;
  background-color: rgba(0,0,0,0.6);
  padding: 10px;

  @media (min-width: 1000px) {
    padding: 15px;
  }

  @media (min-width: 1300px) {
    padding: 20px;
  }
`

const InnerContainer = styled.div`
  height: 100%;
  width: 100%;
  font-weight: bold;
  line-height: 20px;
  z-index: 100;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 18px;
  border: none;

  @media (min-width: 1000px) {
    border: 2px white solid;
    font-size: 20px;
  }

  @media (min-width: 1300px) {
    border: 3px white solid;
    font-size: 24px;
  }
`

const Category = ({ category, clearFilters }) => {
  const navigate = useNavigate()
  return (
    <Container 
      className='csod-category-row-container'
      onClick={() => {
        clearFilters()
        navigate(`/categories/${category.friendly_url_alias || category._id}`)
      }}>
      <Thumbnail className='csod-category-row-thumbnail-container'>
        <Image className='csod-category-row-image' url={category.thumbnail_url}>
          <Content className='csod-category-row-thumbnail-content'>
            <InnerContainer className='csod-category-row-thumbnail-inner-container'>
              <div className='csod-category-row-title'>{category.title}</div>
            </InnerContainer>
          </Content>
        </Image>
      </Thumbnail>
    </Container>
  )
}

const mapDispatchToProps = dispatch => ({
  clearFilters: bindActionCreators(clearFilters, dispatch)
})

export default connect(null, mapDispatchToProps)(Category)
