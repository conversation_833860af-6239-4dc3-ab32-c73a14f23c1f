import React from "react"
import VideoDetails from './details'
import { CSSTransition } from 'react-transition-group'
import styled from '@emotion/styled'

const Fade = styled.div`
  height: 100%;
  width: 95%;
  -webkit-transition: opacity 1.5s ease-in-out, transform 0.5s ease-in;
  -moz-transition: opacity 1.5s ease-in-out, transform 0.5s ease-in;
  -o-transition: opacity 1.5s ease-in-out, transform 0.5s ease-in;
  transition: opacity 1.5s ease-in-out, transform 0.5s ease-in;
  &.video-details-enter, &.video-details-appear {
    opacity: 0;
    transform: translateX(20px);
  }
  &.video-details-enter-active, &.video-details-appear-active {
    opacity: 1;
  }
  &.video-details-exit, &.video-details-leave {
    opacity: 1;
    animation: slide-in 0.5s forwards;
    -webkit-animation: slide-in 0.5s forwards;
  }
  &.video-details-exit-active, &.video-details-leave-active {
    opacity: 0;
  }
`

const Assets = ({ assets, index }) =>
  <>
    {assets.map((video, i) =>
      i === index ?
        <CSSTransition key={i} in appear unmountOnExit timeout={200} classNames='video-details'>
          <Fade>
            <VideoDetails asset={assets[index]} i={i}/>
          </Fade>
        </CSSTransition> : null
    )}
  </>

export default Assets
