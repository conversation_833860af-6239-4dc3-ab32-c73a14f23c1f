import React from "react"
import { connect } from "react-redux"
import PropTypes from "prop-types"
import { useTranslation } from 'react-i18next'
import parseHTML from "helpers/parse_html"
import styled from '@emotion/styled'
import PlayButton from '/src/components/Buttons/play_asset'
import TrailerButton from '/src/components/Buttons/watch_trailer'
import MoreInfoButton from '/src/components/Buttons/more_info'
import CallToActionButton from '../../Buttons/call_to_action'
import { MdClosedCaption } from 'react-icons/md'
import BuyNowButton from '/src/components/Buttons/buy_now'
import { bindActionCreators } from 'redux'
import { toggleModal } from '/src/redux/payments/api'
import useIsMobile from "hooks/use_is_mobile"

const Container = styled.div`
  height: 100%;
`

const TitleLogo = styled.img`
  position: relative;
  max-height: 25%;
  width: auto;
  height: auto;
`

const Details = styled.div`
  position: relative;
  height: 100%;
`

const ButtonContainer = styled.div`
  flex-wrap: wrap;
  .csod-button {
    margin-bottom: 8px;
    margin-right: 8px;
  }
`

const Summary = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
`

const getDescription = (category, asset) => {
  // if the toggle is off, show nothing as per CS-1355
  if (!category.use_logline_in_dashboard) {
    return ''
  }
  // follow through in order logline -> short_summary -> description
  if (category.use_logline_in_dashboard && asset.logline) {
    return asset.logline
  }
  if (category.use_logline_in_dashboard && asset.short_summary) {
    return asset.short_sumary
  }
  return asset.description
}

const FeaturedDetails = ({ asset, category, i = null, foregroundColor }) => {
  const { isMobile } = useIsMobile()
  const { t } = useTranslation()
  const summaryStyle = {
    WebkitLineClamp: (asset.title_logo_image_url || isMobile) ? '5' : '8'
  }
  const description = getDescription(category, asset)
  return (
    <Container
      foregroundColor={foregroundColor}
      className={`p4 csod-featured-video ${i ? 'csod-featured-video-'+i : ''} csod-asset-id-${asset._id} `
      + `${Array.isArray(asset.tags) && asset.tags.map(tag => `csod-asset-tag-${tag.replace(/ /g, '')}`).join(' ')}`}>
      {asset.title_logo_image_url
        ? <TitleLogo src={asset.title_logo_image_url}/>
        : <h2 className='csod-video-title max-width-2 line-clamp-3'>{asset.title}</h2>}
      {!!asset.titles && asset.titles.length > 0 &&
        <div className={`csod-playlist-contains-label`}>{t("playlist.thisPlaylistContains", {count: asset.titles.length } )}.</div>
      }
      <Details className='max-width-2'>
        <strong className='mb1 csod-video-subheader whitespace-pre'>
          {asset.closed_captions_enabled ? <MdClosedCaption className='h2 align-bottom'/> : '' } {parseHTML(asset.subheader)}
        </strong>
        <div className='my2 csod-video-details'>
          {asset.directors &&
            <div><b>{t("asset.director", {count: asset.directors.split(',').length})}:</b> {asset.directors.replace(/,/g, ", ")}</div>}
          {asset.producers &&
            <div><b>{t("asset.producer", {count: asset.producers.split(',').length})}:</b> {asset.producers.replace(/,/g, ", ")}</div>}
        </div>
        {description &&
          <Summary className='mt1 bold csod-video-summary' style={summaryStyle}>{parseHTML(description)}</Summary>}
        <ButtonContainer className='csod-buttons-list mt2'>
          {asset.is_playable && <PlayButton asset={asset}/>}
          {!asset.is_playable && asset.is_purchasable && <BuyNowButton asset={asset}/>}
          {!!asset.trailer_id && <TrailerButton asset={asset}/>}
          <MoreInfoButton asset={asset}/>
          {asset.call_to_actions ? asset.call_to_actions.map((cta, i) => <CallToActionButton key={i} cta={cta}/>) : null}
        </ButtonContainer>
      </Details>
    </Container>
  )
}

FeaturedDetails.propTypes = {
  asset: PropTypes.object,
}

const mapDispatchToProps = dispatch => ({
  toggleModal: bindActionCreators(toggleModal, dispatch),
})

const mapStateToProps = state => ({
  category: state.dashboard.featured.category,
  foregroundColor: state.organization.foreground_color
})

export default connect(mapStateToProps, mapDispatchToProps)(FeaturedDetails)
