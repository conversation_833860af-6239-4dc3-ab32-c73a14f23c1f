import React from "react"
import styled from '@emotion/styled'
import { MdKeyboardArrowLeft, MdKeyboardArrowRight } from 'react-icons/md'
import { connect } from 'react-redux'

const NavIcon = styled.div`
  position: absolute;
  top: 200px;
  left: ${props => props.position === 'left' ? '0px' : 'inherit'};
  right: ${props => props.position === 'right' ? '0px' : 'inherit'};
  opacity: 80%;
  :hover: {
    opacity: 100%;
  }
`

const Arrows = ({ index, setIndex, total, organization }) =>
  total > 1 ? <>
    <NavIcon position='left' useLightTheme={organization.use_light_theme}>
      <MdKeyboardArrowLeft 
        className='pointer csod-feature-arrow-both csod-feature-arrow-left'
        size={'4em'}
        onClick={() => setIndex((index - 1 + total) % total)}/>
    </NavIcon>
    <NavIcon position='right' useLightTheme={organization.use_light_theme}>
      <MdKeyboardArrowRight 
        className='pointer csod-feature-arrow-both csod-feature-arrow-right'
        size={'4em'}
        onClick={() => setIndex((index + 1) % total)}/>
    </NavIcon>
  </> : null

const mapStateToProps = state => ({
  organization: state.organization
})

export default connect(mapStateToProps)(Arrows)
