import React, { useState, useEffect, useCallback } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import styled from '@emotion/styled'
import Backgrounds from './backgrounds'
import Assets from './assets'
import Arrows from './arrows'
import Dots from './dots'

const Container = styled.div`
  position: relative;
  height: 500px;
  color: ${props => props.foregroundColor};
  // overflow-clip-margin: 5px;
  // overflow: clip;
`

const Empty = styled.div`
  height: 50px;
`

const TIMEOUT = 10000

const FeaturedCategory = ({ assets, accent, modal_active, foregroundColor }) => {
  const [index, _setIndex] = useState(0)
  const [timer, setTimer] = useState()

  const setIndex = useCallback((index) => {
    clearTimeout(timer)
    _setIndex(index)
  }, [timer])
  useEffect(() => {
    if (!timer && !modal_active) {
      setTimer(setInterval(() => {
        _setIndex(index => (index + 1) % assets.length)
      }, TIMEOUT))
    }
    if (modal_active) {
      clearTimeout(timer)
      setTimer(null)
    }
  }, [assets, timer, modal_active, setIndex])
  return assets.length > 0 ? (
    <Container className="csod-feature-container" foregroundColor={foregroundColor}>
      <Backgrounds {...{ index, assets }}/>
      <Assets {...{ index, assets }}/>
      <Arrows {...{ index, setIndex, total: assets.length }}/>
      <Dots {...{ index, setIndex, total: assets.length, accent }}/>
    </Container>
  ) : <Empty/>
}

FeaturedCategory.propTypes = {
  assets: PropTypes.array,
}

const mapStateToProps = state => ({
  assets: state.dashboard.featured.category ? state.dashboard.featured.category.videos : [],
  accent: state.organization.accent_color,
  modal_active: state.payments.modal_active,
  foregroundColor: state.organization.foreground_color
})

export default connect(mapStateToProps)(FeaturedCategory)
