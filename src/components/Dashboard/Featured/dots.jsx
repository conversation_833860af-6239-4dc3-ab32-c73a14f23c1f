import React from "react"
import styled from '@emotion/styled'
import { GoDotFill } from 'react-icons/go'

const Bottom = styled.div`
  position: absolute;
  bottom: 0;
  width: 100%;
`

const Dot = styled.span`
  color: ${props => props.active ? props.accent : 'gray'};
  cursor: ${props => props.active ? '' : 'pointer'};
`

const Dots = ({ index, setIndex, total, accent }) =>
  total > 1 ? <Bottom className='center'>
    <div className={'flex flex-row justify-center'}>
      {[...Array(total).keys()].map(i =>
        <Dot key={i} active={index === i} accent={accent} className={`mx1 csod-feature-dot ${index === i ? 'csod-feature-dot-active' : ''}`}>
          <GoDotFill onClick={() => setIndex(i)}/>
        </Dot>
      )}
    </div>
  </Bottom> : null

export default Dots
