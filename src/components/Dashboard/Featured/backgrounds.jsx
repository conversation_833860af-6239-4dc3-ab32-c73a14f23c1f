import React from "react"
import { connect } from "react-redux"
import { Container, Overlay, Image } from '/src/components/Background'

const Backgrounds = ({ assets, index, overlay = true }) => {
  return (
    <Container className='csod-video-backgrounds-container'>
      {overlay && <Overlay dashboard={true}/>}
      {assets.map((asset, i) =>
        <Image dashboard={true} key={i} active={index === i} url={asset.thumbnail_url}/>
      )}
    </Container>
  )
}

const mapStateToMaps = state => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToMaps)(Backgrounds)
