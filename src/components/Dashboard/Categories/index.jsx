import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import CategoriesRow from '../CategoriesRow'
import Category from '../../CategoryRow'
import List from 'rc-virtual-list'

const calculateHeight = (categories) => {
  let height = 0
  categories.forEach(category => {
    if (category.display_style === 'posters') {

    }
  })
  return height
}

const Categories = ({ categoriesRowEnabled, categories, categoriesStatus }) => {
  return (
    <>
      {categoriesRowEnabled && <CategoriesRow/>}
      {/* <div className='w-full'>
        <List
          className='w-full'
          data={categories}
          height={'100%'}
          // itemHeight={30}
          itemKey={'_id'}
        
          {(category, i) => <Category category={category} idx={i} useGridView={category.length === 1}/>}
        </List>
      </div> */}
      {categories.map((category, i) =>
        <Category key={category._id} category={category} idx={i} useGridView={categories.length === 1 && categoriesStatus === 'READY'}/>)}
    </>
  )
}

Categories.propTypes = {
  categories: PropTypes.array,
  categoriesRowEnabled: PropTypes.bool
}

const mapStateToProps = state => ({
  categories: state.dashboard.categories.list,
  categoriesStatus: state.dashboard.categories.status,
  categoriesRowEnabled: state.organization.categories_row_enabled
})

export default connect(mapStateToProps)(Categories)
