import React from "react"
import { useTranslation } from 'react-i18next'

const Empty = () => {
  const { t } = useTranslation()
  return (
    <div
      className={`col-12 csod-no-content-available-container flex items-center justify-center`}
      style={{ minHeight: '300px' }}>
      <div className='flex flex-column justify-center csod-no-content-available-box max-width-2'>
        <span className='material-icons center csod-no-content-available-icon' style={{ fontSize: '82px' }}>movie</span>
        <h4 className='mt2 csod-no-content-available-text'>{t("dashboard.noContentAvailable")}</h4>
      </div>
    </div>
  )
}

export default Empty
