import React, { useState } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from "redux"
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import styled from "styled-components"
import { useTranslation } from "react-i18next"
import { Button, Status, Checkbox } from "@bitcine/cinesend-theme"
import { logIn } from "/src/api/auth"
import ResetPassword from "../ResetPassword"
import useIsMobile from "hooks/use_is_mobile"

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  min-height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const Login = ({
  accent,
  logIn,
  invalidLoginMessage,
  settings,
  terms,
  loading,
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { search } = useLocation()
  let urlParams = new URLSearchParams(search)

  const [email, setEmail] = useState(urlParams.get("email"))
  const [password, setPassword] = useState("")
  const [showReset, setShowReset] = useState(false)
  const [checkedTerms, setCheckedTerms] = useState(false)
  const lang = urlParams.get("lang") || urlParams.get("language")
  const returnTo = urlParams.get("returnTo")
  const { isMobile } = useIsMobile()
  const handleLogin = () => {
    if (loginDisabled()) {
      return
    }
    logIn(email, password, lang, () => {
      if (typeof returnTo === "string") {
        setTimeout(() => {
          navigate(returnTo)
        }, 250)
      }
      else {
        navigate("/")
      }
    })
  }
  const loginDisabled = () => {
    return !email || !password || (terms.accept_required_upon_login && !checkedTerms)
  }
  return (
    <>
      {showReset ? (
        <ResetPassword back={() => setShowReset(false)} lang={lang} />
      ) : (
        <Card isMobile={isMobile} className={`${isMobile ? 'p3' : 'p4'} csod-login-card box-shadow mx-auto`}>
          <Status pending={loading} height={165}>
            <h4 style={{ textAlign: "center" }} className="mb2 csod-login-header">
              {t("home.memberLogin")}
            </h4>
            <input
              className="cs-input col-12 mt1 csod-email-input"
              name="email"
              type="email"
              placeholder={t("home.email")}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  handleLogin()
                }
              }}
            />
            <input
              className="cs-input col-12 mt1 csod-password-input"
              type="password"
              placeholder={t("home.password")}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  handleLogin()
                }
              }}
            />
            {terms.accept_required_upon_login && <div className='flex items-center mt1'>
              <Checkbox
                checked={checkedTerms}
                onChange={() => setCheckedTerms(!checkedTerms)}/>
              <small>Accept <span className='pointer bold' onClick={() => window.open(terms.url, '_blank')}>{terms.name}</span></small>
            </div>}
            <div className="flex justify-between items-center mt2">
              <Button
                className="cs-button link small csod-forgot-password-button"
                style={{ color: settings.use_light_theme ? "#000" : "#fff" }}
                onClick={() => {
                  if (
                    settings.external_authentication.enabled &&
                    settings.external_authentication.forgot_password_link
                  ) {
                    const url =
                      settings.external_authentication.forgot_password_link
                    window.location.href = url.match(/^https?:/)
                      ? url
                      : "//" + url
                  } else {
                    setShowReset(true)
                  }
                }}
              >
                {t("home.forgotPassword")}
              </Button>
              <Button
                style={{ background: accent }}
                className="cs-button accent csod-login-button nowrap"
                small
                disabled={loginDisabled()}
                onClick={handleLogin}
              >
                {t("home.login")}
              </Button>
            </div>

            {settings.allow_account_registrations && (
              <div className="flex justify-between items-center mt1">
                <Button
                  className="cs-button link small csod-register-account-button"
                  style={{ color: settings.use_light_theme ? "#000" : "#fff" }}
                  onClick={() => {
                    navigate("/register")
                  }}
                >
                  {t("home.memberRegister")}
                </Button>
              </div>
            )}

            {invalidLoginMessage && (
              <small className="red bold mt2">{invalidLoginMessage}</small>
            )}
          </Status>
        </Card>
      )}
    </>
  )
}

Login.propTypes = {
  invalidLoginMessage: PropTypes.string,
  settings: PropTypes.object,
  loading: PropTypes.bool,
  terms: PropTypes.object
}

const mapStateToProps = (state) => ({
  loading: state.auth.status === "LOGIN_PENDING",
  accent: state.organization.accent_color,
  invalidLoginMessage: state.auth.error,
  settings: state.organization,
  terms: state.organization.terms_and_conditions,
})

const mapDispatchToProps = (dispatch) => ({
  logIn: bindActionCreators(logIn, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(Login)
