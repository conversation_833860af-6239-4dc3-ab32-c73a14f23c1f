import React, { useState } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from 'react-redux'
import styled from 'styled-components'
import { Status } from '@bitcine/cinesend-theme'
import Questions from './components/questions/'
import LiveChatWrapper from './components/live_chat/wrapper'
import Broadcast from './components/broadcast/'
import Name from '../AudienceInteraction/components/name'
import { saveModeratorLinkName } from '/src/api/moderator'

const Container = styled.div`
  background: black;

  .str-chat__li--single {
    margin-bottom: 40px !important;
  }
  
  .str-chat__li--bottom {
    margin-bottom: 40px !important;
  }
`

const InteractionDisabledMessage = styled.div`
  text-align: center;
  font-size: 18px;
  height: 32vw;
  min-height: 300px;
`

const AudienceInteractionAdmin = ({ assetID, link, saveModeratorLinkName, pending }) => {
  const [name, setName] = useState(link.name)
  const showBroadcast = link.can_broadcast_device
  const showLiveChat = link.can_read_chat || link.can_view_attendees
  const showQuestions = link.can_read_questions

  const count = [showBroadcast, showLiveChat, showQuestions].filter(Boolean).length

  const broadcastSize = count > 1 ? 'col-12 md-col-6' : 'col-12'
  const chatAndQuestionsSize = count === 3 ? 'col-12 md-col-3' : count === 2 ? 'col-12 md-col-6' : 'col-12'

  return (
    <Container className='csod-moderator-container p2'>
      {link && <div className='px2 mb1 center csod-moderator-asset-title'>
        <h2>{link.asset_title}: {link.stream_title}</h2>
      </div>}
      <Status pending={pending} className="clearfix mt2 mxn2 csod-moderator-interactions-wrapper">
      {!name ?
        <div className='max-width-1 mx-auto'>
          <Name onSubmit={value => {
            setName(value)
            saveModeratorLinkName(link._id, value)
          }}/>
        </div> :
        <div>
          {showBroadcast && 
            <div className={`sm-col ${broadcastSize} px1 csod-moderator-broadcasting-wrapper mb2`}>
              <Broadcast link={link} assetID={assetID} name={name}/>
            </div>}
          {showLiveChat && 
            <div className={`sm-col ${chatAndQuestionsSize} px1 csod-moderator-live-chat-wrapper mb2`}>
              <LiveChatWrapper assetID={assetID} link={link} name={name}/>
            </div>}
          {showQuestions &&
            <div className={`sm-col ${chatAndQuestionsSize} px1 csod-moderator-questions-wrapper mb2`}>
              <Questions assetID={assetID}/>
            </div>}
          {!showBroadcast && !showLiveChat && !showQuestions &&
            <InteractionDisabledMessage className={'flex flex-column center justify-center'}>
              No audience interaction options have been enabled for this live stream.
            </InteractionDisabledMessage>}
        </div>}
      </Status>
    </Container>
  )
}

const mapStateToProps = state => ({
  pending: state.dashboard.videoStatus === 'PENDING'
})

const mapDispatchToProps = dispatch => ({
  saveModeratorLinkName: bindActionCreators(saveModeratorLinkName, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(AudienceInteractionAdmin)
