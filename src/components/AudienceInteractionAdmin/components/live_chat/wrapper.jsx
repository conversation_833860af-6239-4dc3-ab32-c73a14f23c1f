import React, { useState, useEffect } from 'react'
import { StreamChat } from 'stream-chat'
import { Status } from '@bitcine/cinesend-theme'
import config from 'config'

import LiveChat from './live_chat'

const client = new StreamChat(process.env.VITE_GETSTREAM_KEY)

const Wrapper = ({ link, assetID, name }) => {
  const [isReady, setIsReady] = useState(false)
  const connectToChat = () => {
    if (!isReady) {
      // client.disconnect().then(() => {
        client.connectUser({ id: link._id, name, identifier: "Moderator", assetID }, link.live_chat_token).then(() => {
          setIsReady(true)
        })
      // })
    }
  }
  useEffect(() => {
    setTimeout(() => {
      connectToChat()
    }, 250)
  }, [connectToChat])
  return (
    <div className='p2 border border-gray-2 rounded'>
      <h3 className='mb1'>Chat with Attendees</h3>
      <Status pending={!isReady}>
        <LiveChat link={link} client={client} channelID={assetID}/>
      </Status>
    </div>
  )
}


export default Wrapper
