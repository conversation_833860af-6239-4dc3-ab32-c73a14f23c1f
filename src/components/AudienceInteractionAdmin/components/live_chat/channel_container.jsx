import React from 'react'
import { MessageList } from 'stream-chat-react'
import Attendees from './attendees'
import ChatMessageAdmin from "./chat_message_admin"
import ChatMessageInputAdmin from "./chat_message_input_admin"
import ChannelFooter from "./channel_footer"

export const ChannelContainer = ({ link, tab, client, channelID }) => {
  const channel = client.channel('livestream', channelID)
  const messageActions = link.can_delete_messages ? ['delete', 'reply', 'react'] : ['reply', 'react'];

  client.user.role = "admin"

  const selectedComponent = () => {
    switch (tab) {
      case 2:
        return link.can_view_attendees && <Attendees link={link} client={client} channelID={channelID} />
      default:
        return (
          <>
            {link.can_read_chat && <MessageList messageActions={messageActions} Message={() => <ChatMessageAdmin />} /> }
            {link.can_write_to_chat && <ChatMessageInputAdmin /> }
            {link.can_clear_chat && <ChannelFooter channel={channel} /> }
          </>
        );
    }
  };

  return <>{selectedComponent()}</>;
};
