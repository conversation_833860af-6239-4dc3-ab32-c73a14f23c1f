import React from 'react'
import styled from 'styled-components'
import BrandedButton from "components/Buttons/button"
import { toast } from "react-toastify"

const LiveChatFooter = styled.div`
  height: 0;
`

function ChannelFooter({ channel }) {
  return (
    <LiveChatFooter className="flex items-center justify-start p1 mb2 col-12" style={{ paddingLeft: '0.7rem'}}>
      <BrandedButton
        className={'small'}
        children={
          <span className='csod-clear-chat-button-text-container'>
            Clear Chat
          </span>
        }
        onClick={() => {
          if (window.confirm("Are you sure you would like to clear chat?")) {
            channel.truncate()
              .then(toast.info("Successfully cleared chat messages"))
              .catch(err => toast.error(`Error clearing chat messages: ${err}`))
          }
        }}/>
    </LiveChatFooter>
  )
}

export default ChannelFooter;
