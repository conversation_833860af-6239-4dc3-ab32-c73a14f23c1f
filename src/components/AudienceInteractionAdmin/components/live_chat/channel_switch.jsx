import React, { useState } from 'react'
import { Attendees, LiveChat } from '/src/components/AudienceInteractionAdmin/assets'
import { ChannelContainer } from './channel_container'
import styled from "styled-components"

const LiveEventChannelSwitchContainer = styled.div`
  flex-direction: row;
  height: 65px;
  box-shadow: 0 1px 0 #2c3134, 0 10px 12px rgba(0, 0, 0, 0.04);
`

const ChannelSwitch = ({ link, client, channelID }) => {
  const [tab, setTab] = useState(link.can_read_chat ? 1 : link.can_view_attendees ? 2 : 0);

  return (
    <>
      {link.can_read_chat && link.can_view_attendees && <LiveEventChannelSwitchContainer className='flex items-center col-12 justify-around relative p1 pointer z2'>
        {link.can_read_chat && <div onClick={() => setTab(1)}>{tab === 1 ? <LiveChat color='#00A3FF' opacity='0.66' /> : <LiveChat />}</div>}
        {link.can_view_attendees && <div onClick={() => setTab(2)}>{tab === 2 ? <Attendees color='#00A3FF' /> : <Attendees color='#8CB0CA' opacity='0.66' />}</div>}
      </LiveEventChannelSwitchContainer>}
      <ChannelContainer link={link} tab={tab} client={client} channelID={channelID} />
    </>
  );
};

export default ChannelSwitch;
