import React, {useEffect} from 'react'
import { connect } from "react-redux"
import { Chat, Channel, Thread, Window } from 'stream-chat-react'
import 'stream-chat-react/dist/css/index.css'
import styled from '@emotion/styled'
import ChannelSwitch from "./channel_switch"

const Container = styled.div`
  // .str-chat-channel {
  //   max-height: auto;
  // }

  // .str-chat {
  //   height: 400px;
  // }

  height: 100%;

  .str-chat-channel {
    max-height: 100%;
  }

  .str-chat {
    height: calc(100vh - 200px);
  }

  .str-chat__fileupload-wrapper {
    display: none;
  }

  .str-chat.livestream.dark {
    background: #1a1a1a;
  }

  .dark.str-chat .str-chat__message-simple-text-inner {
    background: hsla(0,0%,100%,.05);
    color: #fff;
  }
`

const LiveChat = ({ link, client, channelID, theme }) => {
  const channel = client.channel('livestream', channelID)

  useEffect(() => {
  }, [channel])

  return (
    <Container>
      <Chat client={client} theme={'livestream ' + theme}>
        <Channel channel={channel}>
          <Window>
            <ChannelSwitch link={link} client={client} channelID={channelID} />
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </Container>
  )
}

const mapStateToProps = (state, props) => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps)(LiveChat)
