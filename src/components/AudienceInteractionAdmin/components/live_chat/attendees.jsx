import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { toast } from 'react-toastify'
import styled from 'styled-components'
import dayjs from 'dayjs'
import { ButtonDropdown } from '@bitcine/cinesend-theme'
import { IoIosArrowDown } from 'react-icons/io'
import { toggleBan } from 'api/live';

const LiveEventAttendees = styled.div`
  height: 600px;
  font-weight: 500;
  font-size: 14px;
  color: white;
  .cs-button {
    height: 12px;
  }
`

const Subtext = styled.div`
  font-size: smaller;
  color: #858688;
  padding-left: 17px;
`

function sortByLastActive(a, b) {
  return (a.last_active < b.last_active) ? 1
    : (a.last_active > b.last_active) ? -1
      : 0
}

const Attendees = ({ link, client, channelID, toggleUserBan }) => {
  const [users, setUsers] = useState([]);

  function queryUsers(client, channelID) {
    client.queryUsers({ assetID: channelID })
      .then(res => setUsers(res.users.sort((a, b) => sortByLastActive(a, b))))
      .catch(err => toast.error(`Error when trying to retrieve users: ${err}`))
  }

  useEffect(() => {
    queryUsers(client, channelID)

    const interval = setInterval(() => {
      queryUsers(client, channelID)
    }, 30000);
    return () => clearInterval(interval)
  }, [client, channelID])

  return (
    <LiveEventAttendees className='overflow-auto p1'>
      {
        users.map((user) =>
          <div key={user.id}>
            <span className='flex items-center'>
              {user.online
                ? <span className='online' style={{color: 'green'}}>●</span>
                : <span className='offline' style={{color: 'red'}}>●</span>
              }
              <span className='mx1'>
                {user.name}
                &nbsp;|&nbsp;
                {user.identifier ?? 'No identifier'}
              </span>
              {link.can_ban_and_unban_users && <ButtonDropdown
                theme='dark'
                key={user.id}
                button={{
                  className: 'cs-button link',
                  text: <IoIosArrowDown color='white'/>
                }}
                dropdown={{
                  content: [
                    {
                      text: 'Unban',
                      icon: 'gpp_good',
                      onClick: () => {
                        if (window.confirm(`Are you sure you would like to unban ${user.name}?`)) {
                          toggleUserBan(user.id, null, 'true', queryUsers, client, channelID)
                        }
                      },
                      show: user.banned && link.can_ban_and_unban_users
                    },
                    {
                      text: 'Ban',
                      icon: 'gpp_bad',
                      onClick: () => {
                        if (window.confirm(`Are you sure you would like to ban ${user.name}?`)) {
                          toggleUserBan(user.id, client.user.id, 'false', queryUsers, client, channelID)
                        }
                      },
                      show: !user.banned && link.can_ban_and_unban_users
                    }
                  ].filter(opt => opt.show),
                  clickCloses: true
              }}/>}
            </span>
            <Subtext className='flex m0'>
              Last online: {dayjs.utc(user.last_active).local().format('MMM D, YYYY HH:mm')}
              {
                user.banned && <strong>&nbsp;(Banned)</strong>
              }
            </Subtext>
          </div>
        )
      }
    </LiveEventAttendees>
  );
};

const mapDispatchToProps = dispatch => ({
  toggleUserBan: (id, modID, isBanned, queryUsers, client, channelID) => dispatch(toggleBan(id, modID, isBanned, queryUsers, client, channelID)),
})

export default connect(null, mapDispatchToProps)(Attendees);
