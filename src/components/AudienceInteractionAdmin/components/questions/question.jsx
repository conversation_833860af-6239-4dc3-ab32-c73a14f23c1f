import React from 'react'
import { bindActionCreators } from 'redux'
import { connect } from 'react-redux'
import IconWrapper from 'components/Icon'
import { BiUpArrow, BiDownArrow } from 'react-icons/bi'
import dayjs from 'dayjs'
import { updateQuestion, moveQuestion } from 'api/live'
import { SECTIONS } from './constants'

const Question = ({ assetID, question, isFirst, isLast, updateQuestion, moveQuestion, pendingQuestionID }) => {
  const pending = pendingQuestionID === question._id
  return (
    <div className={`py1 border-bottom border-gray-1 flex items-start justify-between ${pending ? 'muted' : ''}`} key={question._id}>
      <div className='pr2 flex items-start'>
        <div className='flex flex-column pt1'>
          <IconWrapper
            id={`move_up_${question._id}`}
            icon={<BiUpArrow size={'1.4em'}/>}
            text='Move up in list'
            tooltipDirection='right'
            disabled={isFirst || pending}
            onClick={() => moveQuestion(assetID, question._id, 'up')}
          />
          <IconWrapper
            id={`move_down_${question._id}`}
            icon={<BiDownArrow size={'1.4em'}/>}
            text='Move down in list'
            tooltipDirection='right'
            disabled={isLast || pending}
            onClick={() => moveQuestion(assetID, question._id, 'down')}
          />
        </div>
        <div className='ml2'>
          <small>Asked By: {question.asked_by} @ {dayjs.utc(question.created_at).local().format('MMM D, YYYY HH:mm')}</small>
          <div>{question.value}</div>
        </div>
      </div>
      <div className='pt2 mr1 nowrap flex items-start'>
        {SECTIONS.filter(section => section.status !== question.status).map(section => {
          const Icon = section.icon
          return (
            <IconWrapper
              id={`mark_${section.type}_${question._id}`}
              key={`mark_${section.type}_${question._id}`}
              icon={<Icon size={'1.6em'}/>}
              text={section.moveToText}
              disabled={pending}
              className='ml2'
              onClick={() => updateQuestion(assetID, question._id, { status: section.status })}/>
          )
        })}
      </div>
    </div>
  )
}

const mapStateToProps = state => ({
  pendingQuestionID: state.live_stream.pendingQuestionID
})

const mapDispatchToProps = dispatch => ({
  updateQuestion: bindActionCreators(updateQuestion, dispatch),
  moveQuestion: bindActionCreators(moveQuestion, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Question)