import React from 'react'
import { Collapse } from 'react-collapse'
import styled from 'styled-components'
import { MdKeyboardArrowDown } from 'react-icons/md'
import Question from './question'

const QuestionsContainer = styled.div`
  height: calc(100vh - 354px);
  overflow-y: scroll;
`

const Collapser = styled.div`
  .ReactCollapse--collapse {
    transition: height 500ms;
  }
`

const Header = ({ isOpen, icon, text, onClick }) => {
  const Icon = icon
  return (
    <h4 className='rounded mb1 pointer bg-gray-1 p1 flex items-center justify-between' onClick={onClick}>
      <div className='flex items-center'>
        <Icon size={'1.4em'}/>
        <span className='ml1'>{text}</span>
      </div>
      <MdKeyboardArrowDown size={'1.4em'} className={`${isOpen ? 'rotate' : ''}`}/>
    </h4>
  )
}

const Section = ({ assetID, icon, onHeaderClick, headerText, isOpen, questions, showsNewQuestions = false }) =>
  <>
    <Header
      icon={icon}
      onClick={onHeaderClick}
      isOpen={isOpen}
      text={`${headerText}${questions.length > 0 ? ` (${questions.length})` : ''}`}/>
    <Collapser>
      <Collapse isOpened={isOpen}>
        <QuestionsContainer className='px1'>
          {questions.map((question, i) =>
            <Question
              key={question._id}
              question={question}
              assetID={assetID}
              isFirst={i === 0}
              isLast={i === questions.length - 1}/>
          )}
          {showsNewQuestions && <div className='italic px3 py1 small'>Questions appear here in real-time...</div>}
        </QuestionsContainer>
      </Collapse>
    </Collapser>
  </>

export default Section