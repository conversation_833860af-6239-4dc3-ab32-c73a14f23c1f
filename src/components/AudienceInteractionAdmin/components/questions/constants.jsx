import { Go<PERSON>heck } from 'react-icons/go'
import { FiStar, FiTrash } from 'react-icons/fi'
import { MdPendingActions } from 'react-icons/md'

export const STATUSES = {
  PENDING: 'Pending',
  STARRED: 'Starred',
  REMOVED: 'Removed',
  ANSWERED: 'Answered'
}

export const SECTIONS = [
  {
    type: 'starred',
    headerText: 'Top Questions',
    moveToText: 'Mark as top question',
    status: STATUSES.STARRED,
    icon: FiStar
  },
  {
    type: 'pending',
    headerText: 'Pending Questions',
    moveToText: 'Move question back to pending',
    status: STATUSES.PENDING,
    showsNewQuestions: true,
    icon: MdPendingActions
  },
  {
    type: 'answered',
    headerText: 'Answered Questions',
    moveToText: 'Mark question as answered',
    status: STATUSES.ANSWERED,
    icon: GoCheck
  },
  {
    type: 'removed',
    headerText: 'Removed Questions',
    moveToText: 'Remove question',
    status: STATUSES.REMOVED,
    icon: FiTrash
  }
]
