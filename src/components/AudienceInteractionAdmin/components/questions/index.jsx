import React, { useEffect, useState } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from 'react-redux'
import styled from 'styled-components'
import { getQuestions } from 'api/live'
import Section from './section'
import { SECTIONS, STATUSES } from './constants'

const Container = styled.div`
  height: calc(100vh - 127px);
  .rotate {
    transform: rotate(180deg);
  }
  transition-property: all;
  transition-property: transform;
`

const Questions = ({ assetID, getQuestions, questions }) => {
  const hasStarred = questions.filter(question => question.status === STATUSES.STARRED).length > 0
  const [openedPanel, setOpenedPanel] = useState(hasStarred ? STATUSES.STARRED : STATUSES.PENDING)
  useEffect(() => {
    let interval = setInterval(() => {
      getQuestions(assetID)
    }, 5000)
    return () => clearInterval(interval)
  }, [])
  return (
    <Container className='p2 border border-gray-2 rounded'>
      {SECTIONS.map(section => 
        <Section
          {...section}
          key={section.type}
          assetID={assetID}
          onHeaderClick={() => setOpenedPanel(section.status)}
          isOpen={openedPanel === section.status}
          questions={questions.filter(question => question.status === section.status)}/>)}
    </Container>
  )
}

const mapStateToProps = state => ({
  questions: state.live_stream.questions
})

const mapDispatchToProps = dispatch => ({
  getQuestions: bindActionCreators(getQuestions, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(Questions)