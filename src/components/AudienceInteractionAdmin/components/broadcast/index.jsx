import React, { useEffect, useState } from 'react'
import { connect } from "react-redux"
import styled from 'styled-components'
// import Live from '@bitcine/cinesend-live'
import config from 'config'
import { getActiveSessions } from "api/live";

const Container = styled.div`
  height: calc(100vh - 127px);
`

const PRODUCTION = 'production'
const VIDEO_PROFILE = 'live'

const Broadcast = ({ link, assetID, name, getViewCount }) => {
  const [viewCount, setViewCount] = useState("0");

  useEffect(() => {
    getViewCount(assetID, setViewCount)

    const interval = setInterval(() => {
      getViewCount(assetID, setViewCount)
    }, 30000);
    return () => clearInterval(interval)
  }, [assetID, setViewCount]);

  return (
    <Container className='col-12 p2 border border-gray-2 rounded'>
      <h3 className='mb1'>
        Live Broadcast
        &nbsp;
        <span style={{ opacity: '0.66', fontSize: '18px' }} className='flex right items-center'>
        {viewCount}&nbsp;viewers
      </span>
      </h3>
      {/*<Live*/}
      {/*  includeChat={false}*/}
      {/*  appID={link.agora_app_key}*/}
      {/*  channelID={assetID}*/}
      {/*  username={name}*/}
      {/*  broadcastURL={link.live_stream_url}*/}
      {/*  highDefinition={config.ENV === PRODUCTION}*/}
      {/*  videoProfile={VIDEO_PROFILE}/>*/}
    </Container>
  )
}

const mapStateToProps = (state, props) => ({
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

const mapDispatchToProps = dispatch => ({
  getViewCount: (id, setViewCount) => dispatch(getActiveSessions(id, setViewCount))
})

export default connect(mapStateToProps, mapDispatchToProps)(Broadcast)
