import React from 'react'
import { connect } from "react-redux"
import styled from '@emotion/styled'
import { useTranslation } from 'react-i18next'
import VideoCarousel from '../VideoCarousel'
import AssetThumbnail from '../AssetThumbnail'
import VideoGrid from '../VideoGrid'
import { getButtonWidth, getSlidePadding } from '../VideoCarousel/utils'
import VideoList from './video_list'
import useIsMobile from 'hooks/use_is_mobile'

const Container = styled.div`
  position: relative;
  z-index: 2;
  width: 100%;
`

const SeriesGrid = ({ organization, series, category, theme, parentVoucher = null }) => {
  const { t } = useTranslation()
  const { isMobile } = useIsMobile()
  return series.seasons.length > 0 ? (
    <div className={`pt4 csod-tv-series-container col-12`}>
       
      {series.seasons.map((season) =>
        <div key={season._id}>
          {season.episodes.length > 0 ? (
            <Container className="my2">
              <div
                className="mb1"
                style={{ paddingLeft: (getButtonWidth() + getSlidePadding()) + 'px' }}>
                {season.title
                  ? <h4 className="csod-series-list-season-title">{season.title}</h4>
                  : <h4 className="csod-series-list-season-label">{t("series.season", { season_number: season.season_number })}</h4>
                }
              </div>
              {organization.series_display_style === 'list' ?
                <VideoList
                  style={{
                    paddingLeft: isMobile ? '16px' : (getButtonWidth() + getSlidePadding()) + 'px',
                    paddingRight: isMobile ? '16px' : (getButtonWidth() + getSlidePadding()) + 'px'
                  }}
                  parentSeries={series}
                  parentVoucher={parentVoucher}
                  videos={season.episodes}/>
                : series?.episode_layout_style === 'grid' ? 
                  <div style={{ paddingLeft: (getButtonWidth() + getSlidePadding()) + 'px', paddingRight: (getButtonWidth()+getSlidePadding())+'px'}}>
                    <VideoGrid
                      parentSeries={series}
                      parentVoucher={parentVoucher}
                      videos={season.episodes}
                      category={{ display_style: series.episode_display_style, row_size: "medium" }}/>  
                  </div>
                : <VideoCarousel
                    displayStyle={series.episode_display_style}
                    displaySize={'large'}
                    theme={theme}>
                    {season.episodes.map(video =>
                      <AssetThumbnail
                        key={video._id}
                        asset={video}
                        category={category}
                        parentVoucher={parentVoucher}
                        parentSeries={series}/>
                    )}
                  </VideoCarousel> 
              } 
            </Container>
          ) : null}
        </div>
      )}
    </div>
  ) : null
}

const mapStateToProps = state => ({
  organization: state.organization,
  theme: state.organization.use_light_theme ? 'light' : 'dark'
})

export default connect(mapStateToProps)(SeriesGrid)
