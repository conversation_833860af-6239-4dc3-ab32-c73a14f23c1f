import PlayButton from '/src/components/Buttons/play_asset'
import parseHTML from 'helpers/parse_html'

function VideoList({ parentSeries, parentVoucher, videos, style }) {
  return (
    <div className='csod-series-list-view space-y-6' style={style}>
      {videos.map((video, index) => 
        <div key={index} className='flex flex-col space-y-2 csod-episode-container'>
          <div className='csod-video-title text-lg font-semibold'>{video.title}</div>
          {video.subheader && <div className='csod-video-subheader text-sm font-light whitespace-pre'>{parseHTML(video.subheader)}</div>}
          {(video.logline || video.short_summary) && <div className='csod-video-description text-sm'>{parseHTML(video.logline || video.short_summary)}</div>}
          {video.is_playable &&
            <PlayButton
              asset={video}
              parentVoucher={parentVoucher}
              parentSeries={parentSeries}
              small/>}
        </div>)}
    </div>
  )
}

export default VideoList