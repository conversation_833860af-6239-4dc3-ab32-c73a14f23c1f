import React from 'react'
import { connect } from "react-redux"
import styled from '@emotion/styled'
import Player from './player'
import IncludeSearchResults from 'components/IncludeSearchResults'
import ContentContainer from '../ContentContainer'
import AudienceInteraction from '../AudienceInteraction'
import useIsMobile from 'hooks/use_is_mobile'

const Wrapper = styled.div`
  ${props => props.isMobile ? `
    margin-top: 16px;
    max-height: 400px;
  ` : `
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    padding-left: 16px;
  `}
`

const AssetPlayerWrapper = ({ voucherID, isChatEnabled, timedScreening, ...props }) => {
  const { isMobile } = useIsMobile()
  return (
    <ContentContainer
      className='csod-video-page-container'
      includeHeader={isChatEnabled}
      includeFooter={isChatEnabled}>
      <IncludeSearchResults>
        <div className={`px4 relative csod-video-page-inner-container`}>
          <div className={`clearfix relative ${isMobile ? 'flex flex-column' : 'flex items-start'}`}>
            <div className={`inline-flex col-12 md-col-6 lg-col-8`}>
              <Player voucherID={voucherID} isChatEnabled={isChatEnabled} {...props}/>
            </div>
            {isChatEnabled &&
              <Wrapper className={`col-12 md-col-6 lg-col-4`} isMobile={isMobile}>
                <AudienceInteraction
                  voucherID={voucherID}
                  isMobile={isMobile}
                  asset={timedScreening}/>
              </Wrapper>}
          </div>
        </div>
      </IncludeSearchResults>
    </ContentContainer>
  )
}

AssetPlayerWrapper.defaultProps = {
  voucherID: null,
  isChatEnabled: false
}

const mapStateToProps = state => ({
  timedScreening: state.session.timedScreening,
  isChatEnabled: state.session.timedScreening && state.session.timedScreening.is_live_chat_enabled,
})

export default connect(mapStateToProps)(AssetPlayerWrapper)
