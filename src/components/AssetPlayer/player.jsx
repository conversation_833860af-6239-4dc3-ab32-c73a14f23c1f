import React from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import styled from "styled-components"
import CineSendPlayer from "@bitcine/cinesend-player"
import { createLog, resetSession } from '/src/api/session'
import ErrorWrapper from "./error_wrapper"
import config from '/src/config'

const InnerContainer = styled.div`
  ${props => props.fillViewport ? `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    ` : `
    width: 100%;
    aspect-ratio: 16 / 9;
    `}
`

const lessThanHalfHour = (date) => {
  return Date.parse(date) < (Date.now() + 1000 * 60 * 30)
}

class VideoPlayer extends React.Component {
  constructor(props) {
    super(props)
    let activeVideo = props.endpoints.videos[props.activeIndex]
    if (props.endpoints.preroll && props.endpoints.preroll.length > 0) {
      activeVideo = props.endpoints.preroll[0]
    }
    let endpoints = props.endpoints
    endpoints.preroll.forEach(video => {
      video.source.poster = props.thumbnailImageURL
    })
    endpoints.videos.forEach(video => {
      video.sourceposter = props.thumbnailImageURL
    })
    this.state = {
      error: null,
      activeVideo,
      endpoints
    }
    this.playerID = 'cinesend-ondemand-player-id'
  }
  onPlayerEvent(log) {
    this.props.createLog(this.props.config.session_logs_url, {
      ...log,
      videoID: this.state.activeVideo.source.video_id,
      sessionID: this.state.activeVideo.source.session_id,
      timestamp: Date.now()
    })
    if (log.logType === 'player_loaded') {
      try {
        window.UserWay.iconVisibilityOff();
      } catch (e) {}
    }
    if (log.logType === 'media_end') {
      try {
        window.UserWay.iconVisibilityOn();
      } catch (e) {}
    }
    if (log.logType === 'media_error') {
      this.setState({
        error: log.error
      })
    }
  }
  videoCompleteHandler(onVideoComplete, asset, voucherID, navigate) {
    const liveStreamDate = asset.live_stream_date

    if (liveStreamDate && lessThanHalfHour(liveStreamDate)) {
      let url = `/live/${asset._id}${voucherID ? `/${voucherID}` : ''}`
      return navigate(url)
    }

    return onVideoComplete()
  }

  render() {
    return (
      <InnerContainer fillViewport={this.props.fillViewport && !this.props.isChatEnabled}>
        <ErrorWrapper
          device={this.props.device}
          recommendation={this.props.device.recommendation}
          error={this.state.error}>
          <CineSendPlayer
            playerCode={this.props.config.player_code}
            muxEnvKey={process.env.VITE_MUX_ANALYTICS_ID}
            chromecastReceiver={this.props.config.chromecast_receiver_code}
            debug={process.env.VITE_ENV !== 'production'}
            playerID={this.playerID}
            activeIndex={this.props.activeIndex}
            endpoints={this.state.endpoints}
            fillViewport={this.props.fillViewport && !this.props.isChatEnabled}
            config={{}}
            autoPlay={this.props.autoPlay}
            startMuted={this.props.startMuted}
            disableResumePlayback={this.props.disableResumePlayback}
            disableFullScreen={this.props.config.disable_full_screen}
            downgradeQualityOnLowDeviceRobustness={this.props.config.downgrade_quality_on_low_device_robustness}
            disableAirplay={this.props.config.disable_airplay}
            disableChromecast={this.props.config.disable_chromecast}
            loopAfterCompletion={this.props.loopAfterCompletion}
            onPlayerEvent={log => this.onPlayerEvent(log)}
            onPlayerCreation={player => {
              setTimeout(() => {
                if (!player || !player.subtitles) {
                  return
                }
                player.subtitles.list().forEach(subtitle => {
                  if (subtitle.id === 'CC1') {
                    player.subtitles.remove(subtitle.id)
                  }
                })
              }, 1000)
            }}
            onVideoChange={video => {
              this.setState({ activeVideo: video })
            }}
            onVideosComplete={() => {
              // this.onPlayerEvent({ logType: 'media_end' }) // Fire off a last media_end log before navigating away (NEW may not need this with timeout below)
              setTimeout(this.videoCompleteHandler(this.props.onVideosComplete, this.props.asset, this.props.voucherID, this.props.navigate), 500) // Delay here so that media_end log can be fired from inside player
            }}/>
        </ErrorWrapper>
      </InnerContainer>
    )
  }
}

VideoPlayer.defaultProps = {
  activeIndex: 0,
  autoPlay: false,
  playbackPosition: 0,
  disableResumePlayback: false,
  loopAfterCompletion: false,
  voucherID: null,
  onVideosComplete: () => {},
  isChatEnabled: false,
  startMuted: false,
  disableAirplay: false,
  disableChromecast: false
}

const mapStateToProps = (state, props) => ({
  device: state.session.device,
  config: state.session.config,
  asset: state.session.asset,
  assetID: state.session.assetID,
  timedScreening: state.session.timedScreening,
  isChatEnabled: state.session.timedScreening && state.session.timedScreening.is_live_chat_enabled,
  disableResumePlayback: state.organization.disable_resume_playback || props.disableResumePlayback,
})

const mapDispatchToProps = dispatch => ({
  createLog: bindActionCreators(createLog, dispatch),
  resetSession: bindActionCreators(resetSession, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(VideoPlayer)
