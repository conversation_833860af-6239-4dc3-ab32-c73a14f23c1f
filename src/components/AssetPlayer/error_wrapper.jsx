import React from "react"
import PropTypes from "prop-types"
import { MdMovie } from 'react-icons/md'

const SCREEN_MIRROR_ERROR = 2013
const APPLE = ['ios', 'osx']
const SCREEN_MIRROR_MESSAGE = <p>
  Please disable all video recording and screen capture software.
  <br/>
  If you have screen mirroring turned on, turn it off and instead use the AirPlay button in the bottom right corner of the player.
  <br/>
  If this message persists and you have an HDMI cable connected, try disconnecting it.
</p>

const PlaybackError = ({ children, device, error, recommendation = {} }) => {
  if (!error) return children
  const os = device?.os?.name.replace(/\s+/g, '').toLowerCase()
  return (
    <div style={{ height: '100%' }} className='flex items-center justify-center csod-playback-error-container'>
      <div className='center csod-playback-error-card'>
        <MdMovie size='4em'/>
        <h3 className='csod-playback-error-header'>Playback Error</h3>
        <p className='my1'>
          <b className="csod-playback-error-title">A playback error occurred.</b>
          <br/>
          <span className={`csod-playback-recommendation csod-playback-recommendation-code-${recommendation.code}`}>
            {
              (APPLE.includes(os) && error?.code === SCREEN_MIRROR_ERROR)
                ? SCREEN_MIRROR_MESSAGE
                : (recommendation?.message)
                ? recommendation.message
                : null
            }
          </span>
          <br/>
          {error.code && <small className='csod-playback-error-code'>(Error code: {error.code} - {error.name})</small>}
        </p>
      </div>
    </div>
  )
}

PlaybackError.propTypes = {
  children: PropTypes.node.isRequired,
  error: PropTypes.object
}

export default PlaybackError
