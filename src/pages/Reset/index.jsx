import React, { useState } from "react"
import PropTypes from "prop-types"
import { bindActionCreators } from 'redux'
import { useNavigate, useParams } from "react-router-dom"
import { useTranslation } from 'react-i18next'
import { connect } from "react-redux"
import styled from '@emotion/styled'
import { Status, Button } from '@bitcine/cinesend-theme'
import ContentContainer from '/src/components/ContentContainer'
import ResetPassword from '/src/components/ResetPassword'
import { logIn, setPassword } from '/src/api/auth'

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const Reset = ({ loading, accent, reset, setPassword }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { token } = useParams()
  const [password, setPasswordState] = useState('')
  return (
    <ContentContainer className='csod-reset-page-container' includeHeroLogo={true}>
      <Status pending={loading}>
        <Card className={`p4 box-shadow mx-auto`}>
          {!reset.hasFailed ?
            <>
              <h4>{t("home.newPassword")}</h4>
              <input
                type="password"
                className='cs-input col-12 mt1'
                placeholder={t("home.enterYourNewPassword")}
                value={password}
                onChange={(e) => setPasswordState(e.target.value)}
                onKeyPress={e => {
                  if (e.key === 'Enter' && password) {
                    setPassword(token, password, () => navigate('/'))
                  }
                }}/>
              <div className='flex justify-end items-center mt2'>
                <Button
                  className={'cs-button csod-reset-password-button'}
                  style={{ background: accent }} disabled={!password || reset.isSubmitting} small onClick={() => {
                  setPassword(token, password, () => navigate('/'))
                }}>{t("home.reset")}</Button>
              </div>
            </> :
            <>
              <small className='red mb2'>{reset.error}</small>
              <ResetPassword/>
            </>
          }
        </Card>
      </Status>
    </ContentContainer>
  )
}

Reset.defaultProps = {
  loading: false,
}

Reset.propTypes = {
  loading: PropTypes.bool,
  setPassword: PropTypes.func,
  reset: PropTypes.object
}

const mapStateToProps = state => ({
  loading: state.session.status === 'PENDING' || state.auth.status === 'PENDING',
  reset: state.auth.reset,
  accent: state.organization.accent_color
})

const mapDispatchToProps = dispatch => ({
  logIn: bindActionCreators(logIn, dispatch),
  setPassword: bindActionCreators(setPassword, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Reset)
