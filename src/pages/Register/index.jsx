import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import styled from 'styled-components'
import ContentContainer from '/src/components/ContentContainer'
import { Button, Status, Checkbox } from '@bitcine/cinesend-theme'
import { useTranslation } from 'react-i18next'
import { register } from '/src/api/auth'
import { useNavigate } from 'react-router-dom'
import { bindActionCreators } from 'redux'

const Container = styled.div`
  padding-top: 120px;
  max-width: 400px;
`

const Card = styled.div`
  background: ${props => props.useLightTheme
  ? 'rgba(0, 0, 0, 0.1)'
  : 'rgba(255, 255, 255, 0.1)'};
  max-width: 500px;
`

const RegisterPage = ({
  useLightTheme,
  loading,
  register,
  registerError,
  registerErrors,
  loginBackgroundImageURL,
  emailOptIn,
  optInLabel
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [passwordConfirm, setPasswordConfirm] = useState('')
  const [buttonDisabled, setButtonDisabled] = useState(true)
  const [registering, setRegistering] = useState(false)
  const [optIn, setOptIn] = useState(true)

  useEffect(() => {
    basicValidation()
  }, [name, email, password, passwordConfirm])

  const basicValidation = () => {
    setButtonDisabled(
      !name || !email || !passwordConfirm || password !== passwordConfirm)
  }
  const handleRegister = () => {
    if (buttonDisabled) return
    setRegistering(true)
    register(name, email, password, optIn, () => {
      if (!registerError) {
        setRegistering(false)
        navigate('/')
      }
    })
  }

  return (
    <ContentContainer
      className="csod-user-register"
      backgroundURL={loginBackgroundImageURL}
      includeHeroLogo={true}
      includeTranslator={true}
      includeHeader={false}
      includeFooter={false}>
      <Container className="mx-auto flex items-top">
        <Status pending={loading} height={165}>
          <Card className="p4 rounded col-12" useLightTheme={useLightTheme}>

            <h4 style={{ textAlign: 'center' }}>{t('home.memberRegister')}</h4>

            {registerError && <>
              <p className="red bold mt2">{t('home.registerErrors')}</p>
              <ul>
                {registerErrors &&
                 registerErrors.map(function(name, index){
                    return <li key={ index }>{name}</li>;
                  })
                }
              </ul>
            </>}

            <input
              className="cs-input col-12 mt1 csod-name-input"
              name="name"
              type="name"
              placeholder={t('home.name')}
              value={name}
              onBlur={basicValidation}
              onChange={(e) => setName(e.target.value)}/>
            <input
              className="cs-input col-12 mt1 csod-email-input"
              name="email"
              type="email"
              placeholder={t('home.email')}
              value={email}
              onBlur={basicValidation}
              onChange={(e) => setEmail(e.target.value)}/>
            <input
              className="cs-input col-12 mt1 csod-password-input"
              type="password"
              placeholder={t('home.password')}
              value={password}
              onBlur={basicValidation}
              onChange={(e) => setPassword(e.target.value)}/>
            <input
              className="cs-input col-12 mt1 csod-confirm-password-input"
              type="password"
              placeholder={t('home.confirmPassword')}
              value={passwordConfirm}
              onBlur={basicValidation}
              onChange={(e) => setPasswordConfirm(e.target.value)}/>
            {emailOptIn && <div className='mt2 mb1'>
                <Checkbox 
                  checked={optIn}
                  size='small'
                  label={<div className='text-white bold'>{optInLabel}</div>}
                  onChange={() => setOptIn(!optIn)}
                />
              </div>
            }
            <div className={'flex items-center justify-between mt1'}>
              <Button
                disabled={registering}
                className='cs-button link small'
                style={{ color: useLightTheme ? '#000' : '#fff' }}
                onClick={() => navigate('/')}>&larr; {t("home.back")}
              </Button>
              <Button
                className="cs-button accent csod-login-button"
                small
                disabled={buttonDisabled}
                onClick={handleRegister}>
                {t('home.registerButton')}
              </Button>
            </div>
          </Card>
        </Status>
      </Container>
    </ContentContainer>
  )
}

RegisterPage.propTypes = {
  loading: PropTypes.bool,
  registerError: PropTypes.bool,
  buttonDisabled: PropTypes.bool,
}

const mapStateToProps = state => ({
  loading: state.auth.status === 'PENDING',
  useLightTheme: state.organization.use_light_theme,
  registerError: state.auth.registerError,
  registerErrors: state.auth.registerErrors,
  loginBackgroundImageURL: state.organization.login_background_image_url,
  emailOptIn: state.organization.email_opt_in_enabled,
  optInLabel: state.organization.email_opt_in_label

})

const mapDispatchToProps = dispatch => ({
  register: bindActionCreators(register, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(RegisterPage)
