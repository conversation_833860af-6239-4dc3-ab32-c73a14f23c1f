import React, { useEffect } from "react"
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useLocation, useParams } from "react-router-dom"
import styled from 'styled-components'
import { Status } from '@bitcine/cinesend-theme'
import { getVideo } from '/src/api/dashboard'
import { getVoucherDetails } from '/src/api/session'
import BackButton from '/src/components/BackButton'
import LiveStreamPlayer from '/src/components/LiveStreamPlayer'
import ContentContainer from '/src/components/ContentContainer'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import AudienceInteraction from '/src/components/AudienceInteraction'
import useIsMobile from "hooks/use_is_mobile"

const Wrapper = styled.div`
  ${props => props.isMobile ? `
    margin-top: 16px;
    max-height: 400px;
  ` : `
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    padding-left: 16px;
  `}
`

const LiveStream = ({
  loggedIn,
  getVideo,
  dashboardVideoStatus,
  voucherVideo,
  voucherStatus,
  dashboardVideo,
  getVoucherDetails
}) => {
  const location = useLocation()
  const { isMobile } = useIsMobile()
  const { videoID, voucherID } = useParams()

  let urlParams = new URLSearchParams(location.search)
  const lang = urlParams.get("lang") || urlParams.get("language")
  const disableBack = urlParams.get("disableBack")

  useEffect(() => {
    if (voucherID !== undefined) {
      getVoucherDetails(voucherID, null, lang)
    }
    else { 
      getVideo(videoID)
    }
  }, [getVideo, videoID, getVoucherDetails, voucherID, loggedIn, lang])
  const status = voucherID !== undefined ? voucherStatus : dashboardVideoStatus
  const video = voucherID !== undefined ? voucherVideo : dashboardVideo
  const isFullViewport =  !video.is_audience_interaction_enabled
  return (
    <ContentContainer
      className='csod-video-live-page-container'
      includeHeader={!isFullViewport}
      includeFooter={!isFullViewport}>
      <IncludeSearchResults>
        <Status pending={status === 'PENDING'} error={status === 'FAILED'}>
          <div className='px4 relative csod-live-page-inner-container'>
            {!disableBack && <BackButton className='csod-back-button-from-live' title='Back to details' forceDark={isFullViewport}/>}
            {status === 'READY' && (
              <div className={`clearfix relative ${isMobile ? 'flex flex-column' : 'flex items-start'}`}>
                <div className={`inline-flex col-12 md-col-6 lg-col-8`}>
                  <LiveStreamPlayer
                    isMobile={isMobile}
                    voucherID={voucherID !== undefined ? voucherVideo.voucher_id : null}/>
                </div>
                {!isFullViewport &&
                  <Wrapper className={`col-12 md-col-6 lg-col-4`} isMobile={isMobile}>
                    <AudienceInteraction
                      loggedIn={loggedIn}
                      isMobile={isMobile}
                      voucherID={voucherID !== undefined ? voucherVideo.voucher_id : null}
                      asset={video}/>
                  </Wrapper>}
              </div>
            )}
          </div>
        </Status>
      </IncludeSearchResults>
    </ContentContainer>
  )
}

LiveStream.propTypes = {
  status: PropTypes.string,
  params: PropTypes.object,
  getVideo: PropTypes.func
}

const mapStateToProps = state => ({
  dashboardVideoStatus: state.dashboard.videoStatus,
  dashboardVideo: state.dashboard.video,
  voucherVideo: state.session.landingDetails.video,
  voucherStatus: state.session.landingDetails.status,
  loggedIn: !!state.auth.user
})

const mapDispatchToProps = dispatch => ({
  getVideo: bindActionCreators(getVideo, dispatch),
  getVoucherDetails: bindActionCreators(getVoucherDetails, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(LiveStream)
