import React from "react"
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useTranslation } from 'react-i18next'
import styled from '@emotion/styled'
import ContentContainer from '/src/components/ContentContainer'
import Login from '/src/components/Login'
import ExternalLogin from '/src/components/ExternalLogin'
import VoucherEntry from '/src/components/VoucherEntry'
import parseHTML from "helpers/parse_html"
import useIsMobile from "hooks/use_is_mobile"
import PinLogin from "components/PinLogin"

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  min-height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const Home = ({ loginBackgroundImageURL, vouchersEnabled, subscribersEnabled, pinLoginEnabled, externalAuthEnabled, welcomeMessageEnabled, welcomeMessage }) => {
  const { t } = useTranslation()
  const { isMobile } = useIsMobile()

  localStorage.setItem('redirect_path', window.location.pathname)

  const enabledCount = [vouchersEnabled, subscribersEnabled, pinLoginEnabled].filter(opt => opt).length

  return (
    <ContentContainer
      className='csod-home-page-container'
      backgroundClassName='csod-home-page-background-container'
      backgroundURL={loginBackgroundImageURL}
      includeHeroLogo={true}
      includeTranslator={true}>
      {welcomeMessageEnabled &&
        <div className={`max-width-3 mx-auto mb3 center csod-welcome-message`}>
          {parseHTML(welcomeMessage)}
        </div>
      }
      <div className={isMobile ? 'mx3' : `flex items-start ${enabledCount === 3 ? 'max-width-4' : 'max-width-3'} mx-auto`}>
        {pinLoginEnabled && <>
          <PinLogin/>
          {(vouchersEnabled || subscribersEnabled) &&
            <div className='m1 center csod-or-text'><b><i>{t("home.or")}</i></b></div>
          }
        </>}
        {vouchersEnabled &&
          <Card isMobile={isMobile} className={`csod-voucher-entry-card ${isMobile ? 'p3' : 'p4'} box-shadow mx-auto`}>
            <VoucherEntry/>
          </Card>}
        {vouchersEnabled && subscribersEnabled &&
          <div className='m1 center csod-or-text'><b><i>{t("home.or")}</i></b></div>
        }
        {subscribersEnabled &&
          <>
            {externalAuthEnabled ? <ExternalLogin/> : <Login/>}
          </>}
      </div>
    </ContentContainer>
  )
}

Home.defaultProps = {
  vouchersEnabled: false,
  subscribersEnabled: false,
  externalAuthEnabled: false,
  pinLoginEnabled: false
}

Home.propTypes = {
  vouchersEnabled: PropTypes.bool,
  subscribersEnabled: PropTypes.bool,
  externalAuthEnabled: PropTypes.bool,
  welcomeMessageEnabled: PropTypes.bool,
  welcomeMessage: PropTypes.string
}

const mapStateToProps = state => ({
  vouchersEnabled: state.organization.vouchers_enabled && !window.location.href.includes('pair'),
  subscribersEnabled: state.organization.subscribers_enabled,
  pinLoginEnabled: state.organization.pin_login_enabled && !window.location.href.includes('pair'),
  externalAuthEnabled: state.organization.external_authentication.redirect,
  welcomeMessageEnabled: state.organization.custom_welcome_message_enabled,
  welcomeMessage: state.organization.custom_welcome_message,
  loginBackgroundImageURL: state.organization.login_background_image_url
})

export default connect(mapStateToProps)(Home)
