import React, { useEffect } from "react"
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"
import Status from 'components/Status'
import Player from '/src/components/AssetPlayer'
import BackButton from '/src/components/BackButton'
import { createVoucherSession } from '/src/api/session'

const PlayerContainer = ({
  endpoints,
  config,
  createVoucherSession,
  status,
  error
}) => {
  let { voucherID, assetID, seasonID, episodeID, activeIndex } = useParams()
  const { search } = useLocation()
  const navigate = useNavigate()
  useEffect(() => {
    // Only create a new voucher session if the status is NOT pending
    // This prevents the duplicate fetching when loading this page directly
    if (status !== 'PENDING') {
      createVoucherSession(voucherID, assetID, seasonID)
    }
  }, [])

  // Let's find the first video to play.
  activeIndex = activeIndex ? parseInt(activeIndex) : 0
  if (endpoints && endpoints.videos) {
    for (let index = 0; index < endpoints.videos.length; index++) {
      let video = endpoints.videos[index]
      if (video.source.video_id === episodeID) {
        activeIndex = index;
      }
    }
    const resumeTimecode = (new URLSearchParams(search)).get("resumeTimecode")
    if (resumeTimecode) {
      if (endpoints.videos[0] && endpoints.videos[0].source) {
        endpoints.videos[0].source.playback_position_in_ms = parseInt(resumeTimecode)
      }
    }
  }

  let urlParams = new URLSearchParams(search)
  const autoPlay = urlParams.get('autoPlay') === "true"
  const loopAfterCompletion = urlParams.get('loop') === "true"
  const disableResumePlayback = urlParams.get('resume') === "false" || config.disable_resume_playback
  const redirectOnEnd = urlParams.get('redirectOnEnd') !== "false"
  const startMuted = urlParams.get('startMuted') === "true"
  const enableBackButton = urlParams.get('enableBack') === "true"
  return (
    <Status
      pending={status === 'PENDING'}
      error={status === 'FAILED'}
      errorMessage={error}
      empty={status === 'NOT_PLAYABLE'}
      emptyMessage={{
        icon: 'movie',
        title: 'This Content Is Not Available Now',
        text: error
      }}>
      <>
        {enableBackButton &&
          <BackButton top='56px' title='back to details' backPath={`/landing/${voucherID}`}/>}
        {endpoints &&
          <Player
            voucherID={voucherID}
            autoPlay={autoPlay}
            startMuted={startMuted}
            disableResumePlayback={disableResumePlayback}
            loopAfterCompletion={loopAfterCompletion}
            endpoints={endpoints}
            activeIndex={activeIndex}
            fillViewport={true}
            navigate={navigate}
            onVideosComplete={() => {
              if (redirectOnEnd) {
                navigate(`/landing/${voucherID}`)
              }
            }}/>
        }
      </>
    </Status>
  )
}

PlayerContainer.propTypes = {
  playlist: PropTypes.array,
  status: PropTypes.string,
  params: PropTypes.object,
  config: PropTypes.object,
  createVoucherSession: PropTypes.func,
}

const mapStateToProps = state => ({
  endpoints: state.session.endpoints,
  config: state.session.config,
  status: state.session.status,
  error: state.session.error,
})

const mapDispatchToProps = dispatch => ({
  createVoucherSession: bindActionCreators(createVoucherSession, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(PlayerContainer)
