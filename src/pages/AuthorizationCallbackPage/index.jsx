import React, { useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { Status } from '@bitcine/cinesend-theme'
import { externalAuthLogin } from '/src/api/auth'

const AuthorizationCallbackPage = ({ externalAuthLogin }) => {
  const { search } = useLocation()
  const params = (new URLSearchParams(search))
  const navigate = useNavigate()
  const code = params.get("code")
  const lang = params.get("lang") || params.get("language") || 'en_US'
  useEffect(() => {
    externalAuthLogin(code, lang, () => {
      // push to the saved login point from {home} component, or root if there isn't one.
      navigate(localStorage.getItem('redirect_path') || '/')
    })
  }, [])
  return (
    <>
      <Status pending={true}></Status>
    </>
  )
}

const mapStateToProps = state => ({
  //details: state.session.landingDetails
})

const mapDispatchToProps = dispatch => ({
  externalAuthLogin: bindActionCreators(externalAuthLogin, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(AuthorizationCallbackPage)
