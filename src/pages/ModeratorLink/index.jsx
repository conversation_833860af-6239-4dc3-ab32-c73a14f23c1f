import React, { useEffect } from 'react'
import { connect } from "react-redux"
import { bindActionCreators } from 'redux'
import { Status } from '@bitcine/cinesend-theme'
import ContentContainer from '/src/components/ContentContainer'
import AudienceInteractionAdmin from '/src/components/AudienceInteractionAdmin'
import { getModeratorLink } from '/src/api/moderator'
import { useParams } from 'react-router-dom'

const ModeratorLink = ({ status, link, getModeratorLink }) => {
  const { moderatorLinkID } = useParams()
  useEffect(() => {
    getModeratorLink(moderatorLinkID)
  }, [getModeratorLink, moderatorLinkID])
  return (
    <ContentContainer
      className='csod-moderator-link-container'
      includeHeader={false}
      includeFooter={false}>
      <Status pending={status === 'PENDING'}>
        <AudienceInteractionAdmin
          assetID={link.asset_id}
          link={link}/>
      </Status>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  link: state.moderator.link,
  status: state.moderator.status
})

const mapDispatchToProps = dispatch => ({
  getModeratorLink: bindActionCreators(getModeratorLink, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(ModeratorLink)
