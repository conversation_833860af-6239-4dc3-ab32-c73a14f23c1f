import React, { useEffect, useState } from 'react'
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useNavigate, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Status, Button, Checkbox } from '@bitcine/cinesend-theme'
import ContentContainer from '/src/components/ContentContainer'
import { getInvite, updateInvite, createAccount } from '/src/api/invite'
import styled from '@emotion/styled'

const Card = styled.div`
  width: 400px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  .material-icons {
    color: ${props => props.accent} !important;
    :hover {
      color: ${props => props.accent} !important;
    }
  }
`

const Label = styled.label`
  color: ${props => props.useLightTheme ? 'black' : 'white'};
`

const Invite = ({ invite, getInvite, accent, updateInvite, createAccount, terms, useLightTheme }) => {
  const [checkedTerms, setCheckedTerms] = useState(false)
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { token } = useParams() 
  useEffect(() => {
    getInvite(token, () => navigate('/'))
  }, [getInvite, token, navigate])
  return (
    <ContentContainer className='csod-invite-page-container' includeHeroLogo={true}>
      <Status pending={invite.status === 'PENDING'} error={invite.status === 'FAILED'} errorMessage={invite.error}>
        <div className='mx-auto'>
          <div className='flex max-width-3 mx-auto'>
            <Card accent={accent} className='p4 mr3 ml2 box-shadow mx-auto'>
              <h4>{t('subscriberActivation.welcome')}</h4>
              <p>{t('subscriberActivation.activationDescription')}</p>
              <Label useLightTheme={useLightTheme} className='cs-label'>{t('subscriberActivation.name')}</Label>
              <form onSubmit={e => {e.preventDefault()}}>
                <input
                  className='cs-input col-12 mb1'
                  placeholder={t('subscriberActivation.name')} value={invite.name}
                  onChange={e => updateInvite('name', e.target.value)}
                />
                <Label useLightTheme={useLightTheme} className='cs-label'>{t('subscriberActivation.email')}</Label>
                <input
                  className='cs-input col-12 mb1'
                  placeholder={t('subscriberActivation.email')}
                  value={invite.email}
                  disabled={true}
                  onChange={e => updateInvite('email', e.target.value)}
                />
                <Label useLightTheme={useLightTheme} className='cs-label'>{t('subscriberActivation.createPassword')}</Label>
                <input
                  className='cs-input col-12 mb1'
                  type='password'
                  placeholder={t('subscriberActivation.createPassword')}
                  value={invite.password}
                  onChange={e => updateInvite('password', e.target.value)}
                />
                {terms.accept_required && <div className='flex items-center mt1'>
                  <Checkbox
                    checked={checkedTerms}
                    onChange={() => setCheckedTerms(!checkedTerms)}/>
                  <small>Accept <span className='pointer bold' onClick={() => window.open(terms.url, '_blank')}>{terms.name}</span></small>
                </div>}
                <div className='right mt2'>
                  <Button
                    className={'cs-button'}
                    style={{ background: accent }}
                    type='submit'
                    small
                    disabled={!invite.name.length || !invite.email.length || !invite.password.length || invite.isSubmitting || (terms.accept_required && !checkedTerms)}
                    onClick={() => createAccount(token, () => navigate('/'))}>
                    {invite.isSubmitting ? t('subscriberActivation.pleaseWait') : t('subscriberActivation.createAccount')}
                  </Button>
                </div>
              </form>
            </Card>
          </div>
        </div>
      </Status>
    </ContentContainer>
  )
}

Invite.propTypes = {
  invite: PropTypes.object,
  getInvite: PropTypes.func,
  createAccount: PropTypes.func,
  useLightTheme: PropTypes.bool
}

const mapStateToProps = state => ({
  invite: state.invite,
  accent: state.organization.accent_color,
  terms: state.organization.terms_and_conditions,
  useLightTheme: state.organization.use_light_theme
})

const mapDispatchToProps = dispatch => ({
  getInvite: bindActionCreators(getInvite, dispatch),
  updateInvite: bindActionCreators(updateInvite, dispatch),
  createAccount: bindActionCreators(createAccount, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Invite)
