import React, { useEffect } from "react"
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import parseHTML from "helpers/parse_html"
import { Status } from '@bitcine/cinesend-theme'
import styled from '@emotion/styled'
import ContentContainer from '/src/components/ContentContainer'
import VideoGrid from '/src/components/VideoGrid'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import BackButton from '/src/components/BackButton'
import { getCategory } from '/src/api/category/'
import useIsMobile from "hooks/use_is_mobile"
import { getCategoryScrollingKey } from "helpers/get_scrolling_key"
import { useParams } from "react-router-dom"

const Container = styled.div`
  .material-icons, h3, p {
    color: ${props => props.useLightTheme ? 'black' : 'white'};
  }
  position: relative;
`

const Full = styled.div`
  width: 100%;
  height: 100%;
`

const Category = ({
  status,
  category,
  useLightTheme,
  getCategory,
  filterTagIDs,
  language
}) => {
  const { isMobile } = useIsMobile()
  const { categoryID } = useParams()

  useEffect(() => {
    getCategory(categoryID, language, filterTagIDs)
  }, [getCategory, categoryID, language, filterTagIDs])

  const scrollStorageKey = getCategoryScrollingKey(categoryID, filterTagIDs)
  useEffect(() => {
    if (status === 'READY') {
      const scrollHeight = localStorage.getItem(scrollStorageKey) || 0
      window.scrollTo(0, scrollHeight)
      setTimeout(() => {
        if (window.scrollY !== scrollHeight) {
          window.scrollTo(0, scrollHeight)
        }
      }, 10)
    }
  }, [status])
  useEffect(() => {
    return () => {
      const scrollHeight = window.scrollY
      localStorage.setItem(scrollStorageKey, scrollHeight)
    };
   }, []);

  if (!category) {
    return null
  }

  return (
    <ContentContainer
      className='csod-category-page-container'
      includeHeader={true}
      backgroundURL={category.background_image_url || null}>
      <IncludeSearchResults>
        <Container useLightTheme={useLightTheme} className='csod-category-page-inner-container'>
          {!isMobile ? <BackButton className='csod-category-back-icon' title='Back to browse' top={'4px'}/> : null}
          <div className={`${isMobile ? 'px2' : 'px4'} py2`}>
            <h3 className={`csod-category-page-header flex items-center mb-2 ${isMobile ? 'text-base' : ''}`}>
              {isMobile
                ? <BackButton
                    className='csod-category-back-icon mr-1'
                    title='Back to browse'
                    small
                    top={0}
                    left={0}
                    position='relative'/>
                : null}
              <div className='csod-category-title'>{category.title}</div>
              <div className='csod-category-counter ml-1'>
                {status === 'READY' && category.videos.length > 0 && ` (${category.videos.length})`}
              </div>
            </h3>
            {category.description
              ? <div className='csod-category-page-description max-width-2 mt2 mb-4 bold'>{parseHTML(category.description)}</div>
              : null}
            <Status
              pending={status === 'PENDING'}
              error={status === 'FAILED'}>
              <Full>
                {category.videos && <VideoGrid videos={category.videos} category={category}/>}
              </Full>
            </Status>
          </div>
        </Container>
      </IncludeSearchResults>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  status: state.category.status,
  category: state.category.model,
  useLightTheme: state.organization.use_light_theme,
  filterTagIDs: state.dashboard.filterTagIDs,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  getCategory: bindActionCreators(getCategory, dispatch),
})

export default connect(mapStateToProps, mapDispatchToProps)(Category)
