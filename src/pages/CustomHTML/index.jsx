import React, { useEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { Status } from '@bitcine/cinesend-theme'
import styled from '@emotion/styled'
import ContentContainer from '/src/components/ContentContainer'
import { getCta } from '/src/api/cta'
import { useParams } from 'react-router-dom'

const HTMLContainer = styled.div`
  padding-top: 62px;
`

const CustomHTML = ({ language, getCta, cta }) => {
  const { ctaID } = useParams()
  useEffect(() => {
    getCta(ctaID, language)
  }, [getCta, ctaID, language])
  const createHTML = () => {
    return {__html: cta.attributes.html}
  }
  return (
    <ContentContainer
      className={`csod-call-to-action-container csod-cta-container-${cta.name}`}
      includeHeader={true}>
      <Status pending={cta.status === 'PENDING'} error={cta.status === 'FAILED'}>
        <HTMLContainer className={`csod-call-to-action-html csod-cta-html-${cta.name}`}>
          {cta.status === 'READY' && <div className='csod-cta-inner-html' dangerouslySetInnerHTML={createHTML()}></div>}
        </HTMLContainer>
      </Status>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  cta: state.cta,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  getCta: bindActionCreators(getCta, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(CustomHTML)
