import React, { useEffect } from "react"
import PropTypes from 'prop-types'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { Status } from '@bitcine/cinesend-theme'
import { getZoomConfig } from '/src/api/videochat'
import ContentContainer from '/src/components/ContentContainer'
import VideoChat from '/src/components/VideoChat'

const VideoChatPage = ({ videochat, getZoomConfig }) => {
  useEffect(() => {
    getZoomConfig()
  }, [getZoomConfig])
  return (
    <ContentContainer includeHeader={true}>
      <Status
        pending={videochat.status === 'PENDING'}
        error={videochat.status === 'FAILED'}
        errorMessage={videochat.error}>
        <VideoChat/>
      </Status>
    </ContentContainer>
  )
}

VideoChatPage.propTypes = {
  videochat: PropTypes.object
}

const mapStateToProps = state => ({
  videochat: state.videochat
})

const mapDispatchToProps = dispatch => ({
  getZoomConfig: bindActionCreators(getZoomConfig, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(VideoChatPage)
