import React, { useEffect } from "react"
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { Status } from '@bitcine/cinesend-theme'
import AssetDetails from '/src/components/AssetDetails'
import ContentContainer from '/src/components/ContentContainer'
import { getVideo } from '/src/api/dashboard'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import { useParams } from "react-router-dom"

const VideoPage = ({ getVideo, video, status, language, location, errorMessage }) => {
  const { videoID } = useParams() 
  useEffect(() => {
    getVideo(videoID, language)
  }, [getVideo, language, videoID])
  useEffect(() => {
    window.scrollTo(0,0)
  }, [])
  return (
    <ContentContainer className='csod-video-details-page-container' includeHeader={true}>
      <IncludeSearchResults>
        {status === "FAILED" && errorMessage === "access_denied" ?
          <div className='col-12 flex items-center justify-center white' style={{ height: '300px' }}>
            <div className='center'>
              <div className='csod-video-details-access-denied'>
                This content is not included in your subscription.
              </div>
            </div>
          </div> : 
          <Status error={status === 'FAILED'} errorMessage={errorMessage} className={errorMessage ? 'h-screen' : ''}>
            {video && status === 'READY' && <AssetDetails asset={video}/>}
          </Status>}
      </IncludeSearchResults>
    </ContentContainer>
  )
}

VideoPage.propTypes = {
  status: PropTypes.string,
  params: PropTypes.object,
  getVideo: PropTypes.func
}

const mapStateToProps = state => ({
  status: state.dashboard.videoStatus,
  video: state.dashboard.video,
  errorMessage: state.dashboard.videoErrorMessage,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  getVideo: bindActionCreators(getVideo, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(VideoPage)
