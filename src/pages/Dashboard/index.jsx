import React, { useEffect, useState, useLayoutEffect } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useNavigate, useParams, Navigate } from 'react-router-dom'
import Status from 'components/Status'
import styled from '@emotion/styled'
import "slick-carousel/slick/slick.css"
import "slick-carousel/slick/slick-theme.css"
import Featured from '/src/components/Dashboard/Featured/'
import Categories from '/src/components/Dashboard/Categories/'
import ActiveBulletin from '/src/components/ActiveBulletin'
import ContentContainer from '/src/components/ContentContainer'
import Empty from '/src/components/Dashboard/Empty'
import Ad from '/src/components/Ad'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import { getDashboard, getFeaturedCategory, getCategories } from '/src/api/dashboard'
import { getAds } from '/src/api/ads'
import { getPlaybackPositions } from '/src/api/session'
import convertToURLFriendly from 'helpers/convert_to_url_friendly'
import GroupedVideoGrid from 'components/GroupedVideoGrid'
import useIsMobile from 'hooks/use_is_mobile'
import { getDashboardScrollingKey } from 'helpers/get_scrolling_key'
import { useLanguage } from 'contexts/language_context'

const CategoriesContainer = styled.div`
  // padding-top: ${props => !props.hasFeatured ? `70px` : 0};
  position: relative;
  z-index: 0;
`

const VideoDash = ({
  user,
  organization,
  categoriesStatus,
  categories,
  featuredStatus,
  featuredCategory,
  getDashboard,
  getFeaturedCategory,
  getCategories,
  activeBulletin,
  redirectTo,
  getAds,
  getPlaybackPositions,
  isEmpty,
  sections,
  groupedResults,
  organizationStatus,
  location,
  filterGenreID,
  filterTagIDs
}) => {
  const [playbackFetched, setPlaybackFetched] = useState(false)
  const { sectionTitle } = useParams()
  const navigate = useNavigate()
  const { activeLanguage } = useLanguage()
  const activeSectionID = sections.find(item => convertToURLFriendly(item.title) === sectionTitle)?._id

  useEffect(() => {
    if (categoriesStatus === 'READY' && !playbackFetched) {
      getPlaybackPositions()
      setPlaybackFetched(true)
    }
  }, [categoriesStatus])

  useLayoutEffect(() => {
    getFeaturedCategory(activeLanguage, activeSectionID, filterGenreID, filterTagIDs)

    if (organization.use_incremental_category_loading) {
      getCategories(0, activeLanguage, activeSectionID, filterGenreID, filterTagIDs)
    }
    else {
      getDashboard(activeLanguage, activeSectionID, filterGenreID, filterTagIDs)
    }

    getAds()
  }, [getDashboard, getCategories, activeLanguage, getAds, user, activeSectionID, filterGenreID, filterTagIDs])

  const redirectToCategory = !featuredCategory && categoriesStatus === 'READY' && categories.length === 1
  const pending = [categoriesStatus, featuredStatus, organizationStatus].includes('PENDING')
  
  const scrollStorageKey = getDashboardScrollingKey(location, filterTagIDs, filterGenreID)
  useEffect(() => {
    if (!pending) {
      const scrollHeight = localStorage.getItem(scrollStorageKey) || 0
      window.scrollTo(0, scrollHeight)
      setTimeout(() => {
        if (window.scrollY !== scrollHeight) {
          window.scrollTo(0, scrollHeight)
        }
      }, 10)
    }
  }, [pending])
  useEffect(() => {
    return () => {
      const scrollHeight = window.scrollY // document.documentElement.scrollTop || document.body.scrollTop || 0
      localStorage.setItem(scrollStorageKey, scrollHeight)
    };
   }, []);

  return (
    <Status
      className={pending ? 'h-screen' : null}
      pending={pending}
      error={[categoriesStatus, featuredStatus, organizationStatus].includes('FAILED')}>
      {redirectTo && navigate(redirectTo)}
      {filterGenreID ?
        <GroupedVideoGrid
          assets={groupedResults}
          organization={organization}/> :
        <>
          {featuredCategory && <Featured/>}
          <div className='csod-extra-1'></div>
          {redirectToCategory && <Navigate to={'/categories/' + (categories[0]?.friendly_url_alias || categories[0]._id)} />}
          {categories.length > 0 && <CategoriesContainer hasFeatured={!!featuredCategory} className='csod-categories-container'>
            <Ad location='top' />
            <Categories/>
            <Ad location='bottom'/>
          </CategoriesContainer>}
          {isEmpty && <Empty/>}
          {activeBulletin && <ActiveBulletin/>}
          <div className='csod-extra-2'></div>
        </>}
    </Status>
  )
}

const Dashboard = (props) => {
  const { isMobile } = useIsMobile()
  const activeSectionID = props.activeSectionID
  const activeSection = props.sections.find(item  => item._id === activeSectionID)
  const showGenreFilter = activeSection?.show_genre_filter
  const showTagFilter = isMobile && props.tagFilters.length > 0
  const showSecondaryHeader = showGenreFilter || showTagFilter
  return (
    <ContentContainer
      className={'csod-dashboard-page-container'}
      includeHeader={true}
      includeSubheader={showSecondaryHeader}>
      <IncludeSearchResults>
        <VideoDash {...props} />
      </IncludeSearchResults>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  featuredCategory: state.dashboard.featured.category,
  featuredStatus: state.dashboard.featured.status,
  categoriesStatus: state.dashboard.categories.status,
  categories: state.dashboard.categories.list,
  activeBulletin: state.organization.active_bulletin,
  redirectTo: state.dashboard.redirectTo,
  organizationStatus: state.organization.status,
  ads: state.ads,
  user: state.auth.user,
  organization: state.organization,
  isEmpty: state.dashboard.categories.status === 'READY'
    && state.dashboard.categories.list.length === 0
    && !state.dashboard.featured.category,
  sections: state.organization.dashboard_sections,
  activeSectionID: state.dashboard.activeSectionID,
  groupedResults: state.dashboard.groupedResults,
  filterGenreID: state.dashboard.filterGenreID,
  filterTagIDs: state.dashboard.filterTagIDs,
  tagFilters: state.organization.tag_filters
})

const mapDispatchToProps = dispatch => ({
  getDashboard: bindActionCreators(getDashboard, dispatch),
  getFeaturedCategory: bindActionCreators(getFeaturedCategory, dispatch),
  getCategories: bindActionCreators(getCategories, dispatch),
  getAds: bindActionCreators(getAds, dispatch),
  getPlaybackPositions: bindActionCreators(getPlaybackPositions, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Dashboard)
