import React from 'react'
import { connect } from "react-redux"
import ContentContainer from '/src/components/ContentContainer'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import GroupedVideoGrid from 'components/GroupedVideoGrid'
import { useTranslation } from 'react-i18next'

const MyList = ({ mylist, organization }) => {
  const { t } = useTranslation()
  return (
    <ContentContainer className='csod-my-list-page-container' includeHeader={true}>
      <IncludeSearchResults>
        <GroupedVideoGrid
          organization={organization}
          pending={mylist.status === 'PENDING'}
          error={mylist.status === 'FAILED'}
          assets={mylist.items}
          header={t('myList.label')}
          headerClass={'csod-my-list-page-header'}
          emptyClass={'csod-my-list-empty-text'}
          emptyTranslation={'myList.empty'}/>
      </IncludeSearchResults>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  mylist: state.dashboard.mylist,
  organization: state.organization
})

export default connect(mapStateToProps)(MyList)
