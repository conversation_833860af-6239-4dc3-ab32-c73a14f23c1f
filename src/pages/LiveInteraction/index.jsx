import React from "react"
import PropTypes from "prop-types"
import ContentContainer from '/src/components/ContentContainer'
import IncludeSearchResults from '/src/components/IncludeSearchResults'
import AudienceInteractionAdmin from '/src/components/AudienceInteractionAdmin'
import { useParams } from "react-router-dom"

const LiveInteraction = () => {
  const { assetID } = useParams()
  return (
    <ContentContainer className='csod-video-live-page-container' includeHeader={true}>
      <IncludeSearchResults>
        <div className='p4'>
          <AudienceInteractionAdmin assetID={assetID}/>
        </div>
      </IncludeSearchResults>
    </ContentContainer>
  )
}

LiveInteraction.propTypes = {
  status: PropTypes.string,
  params: PropTypes.object
}

export default LiveInteraction
