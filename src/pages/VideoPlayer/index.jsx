import React, { useEffect } from "react"
import { bindActionCreators } from 'redux'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom"
import Player from 'components/AssetPlayer'
import BackButton from 'components/BackButton'
import { createSubscriberSession, createPublicSession } from 'api/session'
import Status from "components/Status"

const PlayerContainer = ({
  authenticated,
  publicMode,
  config,
  endpoints,
  createPublicSession,
  createSubscriberSession,
  status,
  error
}) => {
  const { search } = useLocation()
  let urlParams = new URLSearchParams(search)
  const navigate = useNavigate()
  const assetID = urlParams.get('assetID')
  const videoID = urlParams.get('videoID')
  const disableResumePlayback = urlParams.get("resume") === "false" || config.disable_resume_playback
  const disableBack = urlParams.get("disableBack")
  const enableBack = urlParams.get("enableBack") === "true"
  const thumbnailImageURL = urlParams.get("thumbnailImageURL")
  const startMuted = urlParams.get("startMuted") === "true"
  if (!assetID) {
    navigate('/')
  }
  useEffect(() => {
    // Only create a new session if the status is NOT pending
    // This prevents the duplicate fetching when loading this page directly
    if (status === 'PENDING') {
      return
    }
    if (publicMode && !authenticated) {
      createPublicSession(assetID)
    }
    else {
      createSubscriberSession(assetID)
    }
  }, [createSubscriberSession, assetID, publicMode, authenticated])
  let activeIndex = 0
  if (endpoints && endpoints.videos) {
    for(let index = 0; index < endpoints.videos.length; index++) {
      let video = endpoints.videos[index]
      if (videoID && (video.source.video_id === videoID || video.source.friendly_url_alias === videoID)) {
        activeIndex = index;
      }
    }
  }
  const fullScreen = status === 'PENDING' || status === 'FAILED' || status === 'NOT_PLAYABLE'
  return (
    <>
      {(!disableBack || enableBack) && <BackButton top='56px' title='Back to details' className='csod-back-button-from-player' forceDark={true}/>}
      <Status
        className={`${fullScreen ? 'h-screen' : ''} text-gray-200`}
        pending={status === 'PENDING'}
        error={status === 'FAILED'}
        errorMessage={error}
        empty={status === 'NOT_PLAYABLE'}
        emptyMessage={{
          icon: 'movie',
          title: 'This Content Is Not Available Now',
          text: error
        }}>
        {endpoints &&
          <Player
            autoPlay={true}
            startMuted={startMuted}
            endpoints={endpoints}
            activeIndex={activeIndex}
            disableResumePlayback={disableResumePlayback}
            thumbnailImageURL={thumbnailImageURL}
            fillViewport={true}
            navigate={navigate}
            onVideosComplete={() => navigate(-1)}/>
        }
      </Status>
    </>
  )
}

PlayerContainer.propTypes = {
  playlist: PropTypes.array,
  status: PropTypes.string,
  params: PropTypes.object,
  config: PropTypes.object,
  createSubscriberSession: PropTypes.func,
}

const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED',
  publicMode: state.organization.enable_public_browsing ?? false,
  endpoints: state.session.endpoints,
  status: state.session.status,
  error: state.session.error,
  utils: state.utils,
  config: state.session.config,
})

const mapDispatchToProps = dispatch => ({
  createSubscriberSession: bindActionCreators(createSubscriberSession, dispatch),
  createPublicSession: bindActionCreators(createPublicSession, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(PlayerContainer)
