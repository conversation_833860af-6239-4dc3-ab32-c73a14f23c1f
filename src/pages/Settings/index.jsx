import React from 'react'
import { connect } from 'react-redux'
import { Routes, Route, Navigate } from "react-router-dom"
import styled from 'styled-components'
import ContentContainer from '/src/components/ContentContainer'
import Sidebar from '/src/components/Settings/sidebar'
import TV from '/src/components/Settings/tv'
import Devices from '/src/components/Settings/devices'
import Subscriptions from '/src/components/Settings/subscriptions'
import Account from '/src/components/Settings/account'
import Purchases from '/src/components/Settings/purchases'
import IncludeSearchResults from '/src/components/IncludeSearchResults'

const Container = styled.div`
  padding-top: 120px;
  max-width: 720px;
`

const Card = styled.div`
  background: ${props => props.useLightTheme ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.1)'};
  max-width: 500px;
`

const SettingsPage = ({ useLightTheme }) => (
  <ContentContainer
    className='csod-user-settings-container'
    includeHeader={true}
    includeFooter={false}>
    <IncludeSearchResults>
      <Container className='mx-auto flex items-top'>
        <Sidebar />
        <Card className='p2 ml2 rounded col-12' useLightTheme={useLightTheme}>
        <Routes>
            <Route path="/" element={<Navigate to="account" />} />
            <Route path="pair" element={<TV />} />
            <Route path="devices" element={<Devices />} />
            <Route path="subscriptions" element={<Subscriptions />} />
            <Route path="account" element={<Account />} />
            <Route path="purchase-history" element={<Purchases />} />
          </Routes>
        </Card>
      </Container>
    </IncludeSearchResults>
  </ContentContainer>
)

const mapStateToProps = state => ({
  useLightTheme: state.organization.use_light_theme
})

export default connect(mapStateToProps)(SettingsPage)
