import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from "redux"
import { useEffect } from "react"
import { getHeaders } from "../../api/auth"
import { Table } from '@bitcine/cinesend-theme'

const Headers = ({ getHeaders, headers }) => {
  useEffect(() => {
    getHeaders()
  }, [])
  const list = headers
    ? Object.entries(headers).map(([key, value]) => ({ key, value }))
    : []
  return (
    <div className='text-white flex h-full w-full justify-center items-center'>
      {list.length > 0 ? <div className='w-full'>
        <Table
          widths={[300, "auto"]}
          body={{
            data: list,
            row: {
              compact: true,
              render: [
                data => data.key,
                data => data.value
              ]
            }
          }}/>
      </div> : null}
    </div>
  )
}

const mapStateToProps = state => ({
  headers: state.auth.headers
})

const mapDispatchToProps = dispatch => ({
  getHeaders: bindActionCreators(getHeaders, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(Headers)
