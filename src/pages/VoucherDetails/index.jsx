import React, { useEffect, useState } from 'react'
import { bindActionCreators } from 'redux'
import { connect } from "react-redux"
import { useLocation, useParams } from "react-router-dom"
import { Status } from '@bitcine/cinesend-theme'
import CineSendPlayer from '@bitcine/cinesend-player'
import { getVoucherDetails, markAdWatched } from '/src/api/session'
import AssetDetails from '/src/components/AssetDetails'
import ContentContainer from '/src/components/ContentContainer'

const WATCHED_ADS_COOKIE = 'csod-watched-ad-ids'

const VoucherLanding = ({
  getVoucherDetails,
  organization,
  details,
  playerCode,
  markAdWatched,
  language
}) => {
  const { voucherID } = useParams()
  let relatedID = (new URLSearchParams(useLocation().search)).get("ID")
  useEffect(() => {
    getVoucherDetails(voucherID, relatedID, language)
  }, [getVoucherDetails, voucherID, relatedID, language])

  // Handle whether the voucher's ad has been watched yet or not:
  const [watchAdRequired, setWatchAdRequired] = useState(true)
  const watchedAdsString = localStorage.getItem(WATCHED_ADS_COOKIE)
  const watchedAdIDs =  watchedAdsString ? watchedAdsString.split(',') : []
  const setAsWatched = () => {
    setWatchAdRequired(false)
    markAdWatched(voucherID)
    localStorage.setItem(WATCHED_ADS_COOKIE, [...watchedAdIDs, details.video.voucher_id].join())
  }
  const showingAd = details.video.ad_insertion_endpoints && watchAdRequired && !watchedAdIDs.includes(details.video.voucher_id)
  return (
    <ContentContainer
      className='csod-voucher-details-page-container'
      includeHeader={!showingAd}
      includeFooter={!showingAd}>
      <Status
        pending={details.status === 'PENDING'}
        error={details.status === 'FAILED'}
        errorMessage={details.error}
        empty={details.status === 'NOT_PLAYABLE'}
        emptyMessage={{
          icon: 'movie',
          title: 'This content is not currently available.',
          text: details.error
        }}
      >
        {
          showingAd ?
            <CineSendPlayer
              playerCode={playerCode}
              debug={process.env.VITE_ENV !== 'production'}
              playerID={'cinesend-ondemand-ad-player-id'}
              activeIndex={0}
              downgradeQualityOnLowDeviceRobustness={organization?.downgrade_quality_on_low_device_robustness ?? false}
              endpoints={details.video.ad_insertion_endpoints}
              fillViewport={true}
              autoPlay={true}
              onPlayerEvent={log => {
                if (log.logType === 'player_loaded') {
                  try {
                    window.UserWay.iconVisibilityOff();
                  } catch (e) {}
                }
                if (log.logType === 'media_end') {
                  try {
                    window.UserWay.iconVisibilityOn();
                  } catch (e) {}
                }
                if (log.logType === 'media_error') {
                  setAsWatched()
                }
              }}
              onVideosComplete={() => setAsWatched()}/> :
            details.status === 'READY' 
              ? <AssetDetails parentVoucher={details.video} asset={details.video}/>
              : null
        }
      </Status>
    </ContentContainer>
  )
}

const mapStateToProps = state => ({
  details: state.session.landingDetails,
  playerCode: state.organization.player_code,
  organization: state.organization,
  language: state.dashboard.language
})

const mapDispatchToProps = dispatch => ({
  getVoucherDetails: bindActionCreators(getVoucherDetails, dispatch),
  markAdWatched: bindActionCreators(markAdWatched, dispatch)
})

export default connect(mapStateToProps, mapDispatchToProps)(VoucherLanding)
