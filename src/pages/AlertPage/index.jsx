import React from 'react'
import PropTypes from "prop-types"
import { connect } from "react-redux"
import styled from '@emotion/styled'
import ContentContainer from '/src/components/ContentContainer'
import parseHTML from 'helpers/parse_html'
import useIsMobile from 'hooks/use_is_mobile'

const Card = styled.div`
  max-width: 400px;
  width: 85%;
  height: ${props => !props.isMobile ? '300px' : 'auto'};
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
`

const AlertPage = ({ maintenanceHeading, maintenanceMessage, recommendation }) => {
  const { isMobile } = useIsMobile()
  return (
    <ContentContainer className='csod-maintenance-mode-page-container' backgroundClassName='csod-maintenance-mode-background-container' includeHeroLogo={true} includeTranslator={true}>
      <Card isMobile={isMobile} className={`csod-maintenance-mode-container ${isMobile ? 'p3' : 'p4'} box-shadow mx-auto`}>
        <h4 style={{ textAlign: 'center' }}>{recommendation ? 'Incompatible Device Detected' : parseHTML(maintenanceHeading)}</h4>
        <div className='my3'>
          <span>{parseHTML(recommendation ?? maintenanceMessage)}</span>
        </div>
      </Card>
    </ContentContainer>
  )
}

AlertPage.propTypes = {
  maintenanceHeading: PropTypes.string,
  maintenanceMessage: PropTypes.string,
  recommendation: PropTypes.string,
}

const mapStateToProps = state => ({
  maintenanceHeading: state.organization.maintenance_heading,
  maintenanceMessage: state.organization.maintenance_message,
  recommendation: state.organization.recommendation ?? null,
})

export default connect(mapStateToProps)(AlertPage)
