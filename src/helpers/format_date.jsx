import dayjs from 'dayjs'
import 'dayjs/locale/fr-ca'

const map = {
  'fr_CA': 'fr-ca',
  'fr': 'fr-ca'
}

const sanitizeDate = (date, language) => {
  date = dayjs.utc(date).local()
  return (map[language]) ? date.locale(map[language]) : date
}

export const formatDate = (date, language, translate) => {
  return sanitizeDate(date, language).format(translate('datetime.format'))
}

export const formatDateFromNow = (date, language) => {
  return sanitizeDate(date, language).fromNow()
}