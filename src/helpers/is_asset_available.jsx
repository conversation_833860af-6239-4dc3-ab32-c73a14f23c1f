import dayjs from 'dayjs'
import { assetTypes} from './constants'

export const isAssetAvailable = (asset, parentVoucher = null, parentSeries = null) => {
  if (parentVoucher && parentVoucher.expires_at && dayjs(parentVoucher.expires_at).isBefore(dayjs())) {
    if (!asset.is_bonus_content) {
      return false
    }
    if (asset.bonus_content_expires_with_vouchers) {
      return false
    }
  }
  else if (asset.expires_at && dayjs(asset.expires_at).isBefore(dayjs())) {
    return false
  }
  else if (parentSeries) {
    return parentSeries.is_playable
  }
  else if (asset.is_timed_screening) {
    return !asset.hide_play_button
  }
  else if (asset.type === assetTypes.EVENT) {
    return asset.timed_event_starts_at && dayjs(asset.timed_event_starts_at).isBefore(dayjs())
  }
  return asset.is_playable
}
