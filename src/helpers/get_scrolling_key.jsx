const getDashboardScrollingKey  = (location, tagIDs, genreID) => {
  let storageKey = `dashboard-scroll-location`
  if (location) {
    storageKey += `-${location.pathname}`
  }
  let keyArray = [tagIDs, genreID]
  keyArray.filter(key => !!key).forEach(key => {
    storageKey += `-${key}`
  })
  return storageKey
}
const getCategoryScrollingKey  = (categoryID, tagIDs) => {
  let storageKey = `category-scroll-location-${categoryID}`
  if (tagIDs) {
    storageKey += `-${tagIDs}`
  }
  return storageKey
}

export {
  getDashboardScrollingKey,
  getCategoryScrollingKey
}