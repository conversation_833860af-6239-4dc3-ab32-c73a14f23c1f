export const darkStyles = {
  option: (provided, state) => ({
    ...provided,
    background: state.isFocused ? '#333' : '#111',
    cursor: state.isFocused ? 'pointer' : 'cursor',
    color: '#fff'
  }),
  control: () => ({
    transition: 'all 100ms',
    boxSizing: 'border-box',
    justifyContent: 'space-between',
    minHeight: '38px',
    outline: '0 !important',
    position: 'relative',
    flexWrap: 'wrap',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '4px',
    borderStyle: 'solid',
    borderWidth: '1px',
    cursor: 'default',
    backgroundColor: '#333',
    borderColor: '#111',
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: '#111'
  }),
  multiValue: (provided) => ({ 
    ...provided, 
    background: '#111',
    color: '#fff'
  }),
  multiValueLabel: (provided) => ({ 
    ...provided, 
    background: '#111',
    color: '#fff'
  }),
  multiValueRemove: (provided, state) => ({ 
    ...provided, 
    background: state.isFocused ? '#333' : '#111',
    color: '#fff',
    cursor: state.isFocused ? 'pointer' : 'cursor'
  })
}