import dayjs from 'dayjs'

export const isThumbnailClickable = (asset, parentVoucher = null, parentSeries = null) => {
  if (parentVoucher && parentVoucher.expires_at && dayjs(parentVoucher.expires_at).isBefore(dayjs())) {
    if (!asset.is_bonus_content) {
      return false
    }
    if (asset.bonus_content_expires_with_vouchers) {
      return false
    }
  }
  else if (asset.expires_at && dayjs(asset.expires_at).isBefore(dayjs())) {
    return false
  }
  else if (asset.starts_at) {
    return !asset.hide_play_button
  }
  else if (parentSeries) {
    return parentSeries.is_playable && asset.is_playable
  }
  else if (asset.is_bonus_content) {
    return asset.is_playable
  }
  return true
}