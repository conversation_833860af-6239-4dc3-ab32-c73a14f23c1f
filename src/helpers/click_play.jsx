import { assetTypes } from './constants'

const isDashboardAsset = asset => {
  return [
    assetTypes.VIDEO,
    assetTypes.PLAYLIST,
    assetTypes.SERIES,
    assetTypes.EVENT,
    assetTypes.ALBUM
  ].includes(asset.type)
}

export const clickPlay = ({
  navigate, asset, parentVoucher, parentSeries, latestPlaybackVideoID, clickType
}) => {

  const assetID = asset.friendly_url_alias || asset._id

  // If we are clicking on the thumbnail of TV series, playlists, event, or videos, we go to asset details:
  if (clickType === 'thumbnail' && isDashboardAsset(asset) && !asset.is_timed_screening) {
    navigate(`/view/${assetID}`)
    return
  }

  // If we're on the voucher landing page, go to the player for that voucher.
  if (parentVoucher) {

    // If this is a TV episode, go to the specific season.
    if (asset.type === assetTypes.EPISODE) {
      navigate(`/${parentVoucher.voucher_id}/seasons/${asset.season_id}/episodes/${assetID}?autoPlay=true&enableBack=true`)
      return
    }

    // If the asset itself is the parent voucher, play the voucher directly.
    if (assetID === parentVoucher._id) {
      navigate(`/${parentVoucher.voucher_id}?autoPlay=true&enableBack=true`)
      return
    }

    // Otherwise just go to the asset.
    navigate(`/${parentVoucher.voucher_id}/assets/${assetID}?autoPlay=true&enableBack=true`)
    return
  }

  if (parentSeries) {
    // If we're looking at an episode of TV season:
    if (asset.type === assetTypes.EPISODE) {
      navigate(`/play?assetID=${parentSeries._id}&videoID=${assetID}`)
      return
    }
  }

  // If we're looking at the play button of a TV series:
  if (asset.type === assetTypes.SERIES) {

    // It's possible the user has already made progress watching this TV series, here we run some logic to find potential matches.
    if (asset.seasons) {
      let navigated = false
      asset.seasons.forEach(season => {
        if (season.episodes) {
          season.episodes.forEach(episode => {
            if (episode._id === latestPlaybackVideoID) {
              navigate(`/play?assetID=${assetID}&videoID=${episode._id}`)
              navigated = true
            }
          })
        }
      })
      if (navigated) {
        return
      }

      // If no match is found, we play the first episode of season 1.
      if (asset.seasons[0].episodes) {
        navigate(`/play?assetID=${assetID}&videoID=${asset.seasons[0].episodes[0]._id}`)
        return
      }
    }
  }

  // Lastly, just go directly to play the video:
  navigate(`/play?assetID=${assetID}`)
}
