import {
  GET_DASHBOARD,
  SET_CATEGORIES,
  GET_VIDEO,
  RATE_ASSET,
  SET_VIDEO_RATING,
  SET_CATEGORY_SLIDE_INDEX,
  ADD_OR_REMOVE_ASSET_LIST,
  GET_ASSET_LIST,
  SET_REDIRECT,
  SET_LANGUAGE_CODE_FILTER,
  SET_PLAYLIST_ASSET_RATING,
  SET_ACTIVE_SECTION,
  SET_FILTER_GENRE,
  SET_FILTER_TAGS,
  SET_FOOTER_HEIGHT,
  GET_DASHBOARD_FEATURED_CATEGORY,
  GET_NEXT_CATEGORIES,
  REPOPULATE_DEFAULT_TAGS
} from '/src/redux/dashboard/types'
import {
  SET_SESSION_VIDEO_RATING,
  SET_SESSION_PLAYLIST_ASSET_RATING
} from '/src/redux/session/types'
import { get, post, del } from '../fetch'
import _ from 'lodash'
import getDashboardKey from 'helpers/get_dashboard_key'
// import { push } from 'connected-react-router'

const getParams = (organizationID, lang, sectionID, genreID, tagIDs, data = {}) => {
  let filters = {
    organizationID,
    ...data
  }
  if (lang) {
    filters.lang = lang
  }
  if (sectionID) {
    filters.sectionID = sectionID
  }
  if (genreID) {
    filters.genreID = genreID
  }
  if (tagIDs) {
    filters.tagIDs = tagIDs
  }
  return new URLSearchParams(filters).toString();
}

export const getDashboard = (lang = null, sectionID = null, genreID = null, tagIDs = null) => (dispatch, getState) => {
  const params = getParams(getState().organization._id, lang, sectionID, genreID, tagIDs)
  dispatch(setActiveSectionID(sectionID))
  dispatch(
    get(
      GET_DASHBOARD.REQUEST,
      `ondemand/dashboard/videos?${params}`,
      res => {
        // Do not redirect if sectionID or tagIDs or genreIDs exist!
        if (genreID || tagIDs || sectionID) {
          return
        }

        const videos = [].concat.apply([], res.data.categories.map(cat => cat.videos))
        const merged = videos.concat(res.data.featuredCategory ? res.data.featuredCategory.videos : [])
        const uniqueVideos = _.uniqBy(merged, '_id')
        if (uniqueVideos.length === 1) {
          // Use dispatch(push()) once it works...
          // dispatch(push('/view/' + uniqueVideos[0]._id))
          dispatch({
            type: SET_REDIRECT,
            payload: { to: '/view/' + uniqueVideos[0]._id }
          })
        }
      }
    )
  )
}

export const getFeaturedCategory = (lang = null, sectionID = null, genreID = null, tagIDs = null) => (dispatch, getState) => {
  const params = getParams(getState().organization._id, lang, sectionID, genreID, tagIDs)
  dispatch(
    get(
      GET_DASHBOARD_FEATURED_CATEGORY.REQUEST,
      `ondemand/dashboard/featured-category?${params}`
    )
  )
}

export const setCategories = (list) => dispatch => {
  dispatch({
    type: SET_CATEGORIES,
    payload: list
  })
}

export const getCategories = (index = 0, lang = null, sectionID = null, genreID = null, tagIDs = null) => (dispatch, getState) => {
  dispatch(setActiveSectionID(sectionID))
  const params = getParams(getState().organization._id, lang, sectionID, genreID, tagIDs, { index })

  // If we're fetching from the first index, clear the current categories
  if (index === 0) {
    dispatch(setCategories([]))
  }
  dispatch(
    get(
      GET_NEXT_CATEGORIES.REQUEST,
      `ondemand/dashboard/categories?${params}`,
      res => {
        // If the response dashboard key is the same as the current dashboard key, we can continue fetching. Otherwise we'll stop.
        let resultKey = getDashboardKey(sectionID, tagIDs, genreID, lang)
        if (getState().dashboard.dashboardKey !== resultKey) {
          return
        }
        // If no more categories are returned, we're done.
        if (!res.data.categories) {
          return
        }
        // We can keep fetching if the current index is less than the total number of categories
        if (res.data.index + 1 < res.data.total) {
          dispatch(getCategories(res.data.index + 1, lang, sectionID, genreID, tagIDs))
        }
      }
    )
  )
}

export const getVideo = (videoID, lang = null) => (dispatch, getState) => {
  dispatch(
    get(
      GET_VIDEO.REQUEST,
      `ondemand/dashboard/videos/${videoID}?lang=${lang}`,
    )
  )
}

export const rateAsset = (id, rating, type, voucherID, fromPlaylist = false) => dispatch => {
  dispatch({
    type: voucherID && fromPlaylist
      ? SET_SESSION_PLAYLIST_ASSET_RATING
      : voucherID
        ? SET_SESSION_VIDEO_RATING
        : fromPlaylist
          ? SET_PLAYLIST_ASSET_RATING
          : SET_VIDEO_RATING,
    payload: {
      id,
      rating
    }
  })
  return dispatch(
    post(RATE_ASSET.REQUEST, `ondemand/dashboard/rating`, { type, rating, id, voucherID })
  )
}

export const setCategorySlideIndex = (catIdx, slideIdx) => dispatch => dispatch({
  type: SET_CATEGORY_SLIDE_INDEX,
  payload: {
    catIdx, slideIdx
  }
})

export const setActiveSectionID = (sectionID) => dispatch => {
  dispatch({
    type: SET_ACTIVE_SECTION,
    payload: {
      sectionID
    }
  })
}

export const setFilterGenreID = (filterGenreID) => dispatch => {
  dispatch({
    type: SET_FILTER_GENRE,
    payload: {
      filterGenreID
    }
  })
}

export const setFilterTagIDs = (filterTagIDs) => dispatch => {
  dispatch({
    type: SET_FILTER_TAGS,
    payload: {
      filterTagIDs
    }
  })
}

export const setFooterHeight = height => dispatch => {
  dispatch({
    type: SET_FOOTER_HEIGHT,
    payload: height
  })
}

export const getAssetList = () => (dispatch, getState) => dispatch(
  get(
    GET_ASSET_LIST.REQUEST,
    `ondemand/list?lang=${getState().auth.user.active_language_code}`,
  )
)

export const addAssetToList = (type, id) => (dispatch, getState) => dispatch(
  post(
    ADD_OR_REMOVE_ASSET_LIST.REQUEST,
    `ondemand/list?lang=${getState().auth.user.active_language_code}`,
    { type, id },
  )
)

export const removeAssetFromList = (type, id) => (dispatch, getState) => dispatch(
  del(
    ADD_OR_REMOVE_ASSET_LIST.REQUEST,
    `ondemand/list?lang=${getState().auth.user.active_language_code}`,
    { type, id },
  )
)

export const setLanguageCodeFilter = code => (dispatch, getState) => dispatch({
  type: SET_LANGUAGE_CODE_FILTER,
  payload: {
    code
  }
})

export const repopulateDefaultTags = (organization) => dispatch => {
  dispatch({
    type: REPOPULATE_DEFAULT_TAGS,
    payload: { organization }
  })
}
