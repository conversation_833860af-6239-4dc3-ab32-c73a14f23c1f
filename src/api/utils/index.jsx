import { SET_UTIL_VALUE, GET_COUNTRIES } from '/src/redux/utils/types'
import { get } from '../fetch'

export const setUtilValue = (key, value) => dispatch => dispatch({
  type: SET_UTIL_VALUE,
  payload: { key, value }
})

export const getCountries = () => (dispatch, getState) => {
  if (getState().utils.countries.status === 'READY') return
  dispatch(
    get(
      GET_COUNTRIES.REQUEST,
      `utilities/countries`
    )
  )
}