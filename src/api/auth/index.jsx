import {
  AUTH_CHECK,
  REGISTER,
  LOG_OUT,
  LOG_IN,
  PIN_LOG_IN,
  RESET_PASSWORD,
  SET_PASSWORD,
  PAIR_CODE,
  UPDATE_USER,
  UPDATE_USER_LANGUAGE,
  DELETE_SUBSCRIBER_DEVICE,
  GET_HEADERS
} from '/src/redux/auth/types'
import {
  SET_LANGUAGE_CODE
} from '/src/redux/dashboard/types'
import { get, post, del } from '../fetch'
import { toast } from "react-toastify"

export const authCheck = () => dispatch =>  dispatch(
  get(
    AUTH_CHECK.REQUEST,
    `ondemand/subscriber-auth/check`,
  )
)

export const getHeaders = () => dispatch =>  dispatch(
  get(
    GET_HEADERS.REQUEST,
    `users/ip`,
  )
)

export const externalAuthLogin = (code, lang = null, callback, errorCallback) => (dispatch, getState) => dispatch(
  post(
    LOG_IN.REQUEST,
    `ondemand/subscriber-auth/login?code=${code}`,
    {
      code,
      lang
    },
    () => callback(),
    () => errorCallback(),
  )
)
export const register = (name, email, password, optIn, callback) => dispatch => dispatch(
  post(
    REGISTER.REQUEST,
    `ondemand/subscriber-auth/register`,
    {
      name,
      email,
      password,
      optIn
    },
    () => {
      if (typeof callback === 'function') {
        callback()
      }
    }
  )
)

export const logIn = (email, password, lang = null, callback) => (dispatch) => dispatch(
  post(
    LOG_IN.REQUEST,
    `ondemand/subscriber-auth/login${lang ? `?lang=${lang}` : ''}`,
    {
      email,
      password
    },
    () => {
      if (typeof callback === 'function') {
        callback()
      }
    }
  )
)

export const pinLogIn = (pin, lang = null, callback) => (dispatch) => dispatch(
  post(
    PIN_LOG_IN.REQUEST,
    `ondemand/subscriber-auth/login${lang ? `?lang=${lang}` : ''}`,
    {
      pin
    },
    () => {
      if (typeof callback === 'function') {
        callback()
      }
    }
  )
)

export const logOut = () => (dispatch, getState) => dispatch(
  post(
    LOG_OUT.REQUEST,
    `ondemand/subscriber-auth/logout`, {}, () => {
      window.location.replace(`/?lang=${getState().auth.user.active_language_code}`)
    }
  )
)

export const resetPassword = (email, lang = null) => dispatch => dispatch(
  post(
    RESET_PASSWORD.REQUEST,
    `ondemand/subscriber-auth/reset${lang ? `?lang=${lang}` : ''}`,
    { email }
  )
)

export const setPassword = (token, password, callback) => dispatch => dispatch(
  post(
    SET_PASSWORD.REQUEST,
    `ondemand/subscriber-auth/reset/` + token,
    {
      password
    }, 
    () => {
      callback()
      toast.info("Your password has been reset. You can now login with your new password.")
    }
  )
)

export const pairCode = code => dispatch => dispatch(
  post(
    PAIR_CODE.REQUEST,
    `ondemand/pairing-code`,
    {
      code
    }
  )
)

export const updateName = name => dispatch => dispatch(
  post(
    UPDATE_USER.REQUEST,
    `ondemand/subscriber/name`,
    {
      name
    }
  )
)

export const setLanguageCode = code => (dispatch, getState) => {
  dispatch({
    type: SET_LANGUAGE_CODE,
    payload: {
      code
    }
  })
  if (getState().auth.user) {
    dispatch(
      post(
        UPDATE_USER_LANGUAGE.REQUEST,
        `ondemand/subscriber/language-code`,
        {
          code
        }
      )
    )
  }
}

export const updatePassword = (oldPassword, newPassword, callback) => dispatch => dispatch(
  post(
    UPDATE_USER.REQUEST,
    `ondemand/subscriber/password`,
    {
      oldPassword,
      newPassword
    },
    res => callback(res),
    res => callback(res)
  )
)

export const deleteSubscriberDevice = (subscriberID, accessDeviceID) => dispatch =>
  dispatch(
    del(
      DELETE_SUBSCRIBER_DEVICE.REQUEST,
      `ondemand/subscribers/${subscriberID}/access-device/${accessDeviceID}`,
      {},
      () => toast.info("The requested device has been deleted.")
    )
  )
