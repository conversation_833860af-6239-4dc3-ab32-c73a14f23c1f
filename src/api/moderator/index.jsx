import {
  GET_MODERATOR_LINK,
  UPDATE_MODERATOR_LINK
} from '/src/redux/moderator/types'
import { get, post } from '../fetch'

export const getModeratorLink = id => dispatch =>
  dispatch(
    get(
      GET_MODERATOR_LINK.REQUEST, `ondemand/moderators/${id}`
    )
  )

export const saveModeratorLinkName = (id, name) => dispatch =>
  dispatch(
    post(
      UPDATE_MODERATOR_LINK.REQUEST,
      `ondemand/moderators/${id}/name`,
      {
        name
      }
    )
  )
