import {
  GET_CATEGORY
} from '/src/redux/category/types'
import { get } from '../fetch'

export const getCategory = (categoryID, lang = null, tagIDs) => (dispatch, getState) => {
  const organizationID = getState().organization._id
  let filters = {
    organizationID,
    lang
  }
  if (tagIDs) {
    filters.tagIDs = tagIDs
  }
  const params = new URLSearchParams(filters).toString();
  dispatch(
    get(
      GET_CATEGORY.REQUEST,
      `ondemand/dashboard/categories/${categoryID}?${params}`
    )
  )
}
