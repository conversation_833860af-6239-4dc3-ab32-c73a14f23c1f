import {
  TOGGLE_PREVIEW_BAR,
  UPDATE_PREVIEW
} from '/src/redux/preview/types'
import { post } from '../fetch'
// import { authCheck } from '../auth'
import { getOrganization } from '../organization'

export const togglePreviewBar = () => dispatch => dispatch({
  type: TOGGLE_PREVIEW_BAR
})

export const toggleUnpublishedChanges = () => dispatch => dispatch(
  post(
    UPDATE_PREVIEW.REQUEST,
    `ondemand/admin/preview/toggle-unpublished`
  )
)

export const updatePreviewMode = (mode, assetID = null) => dispatch => dispatch(
  post(
    UPDATE_PREVIEW.REQUEST,
    `ondemand/admin/preview/mode`,
    { mode, assetID },
    () => {
      // dispatch(authCheck())
      dispatch(getOrganization())
    }
  )
)

export const updatePreviewSubscriberTypes = subscriberTypeIDs => dispatch => dispatch(
  post(
    UPDATE_PREVIEW.REQUEST,
    `ondemand/admin/preview/subscriber-types`,
    { subscriberTypeIDs },
    () => {
      // dispatch(authCheck())
      dispatch(getOrganization())
    }
  )
)
