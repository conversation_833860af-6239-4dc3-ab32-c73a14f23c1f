import {
  CREATE_VOUCHER_SESSION,
  CREATE_SUBSCRIBER_SESSION,
  RESET_SESSION,
  GET_VOUCHER_DETAILS,
  GET_PLAYBACK_POSITIONS,
  MARK_AD_WATCHED
} from '/src/redux/session/types'
import { get, post } from '../fetch'

export const createVoucherSession = (code, assetID, seasonID) => dispatch => {
  // If there's an asset ID, we are looking at related content (trailer, bonus content).
  if (assetID) {
      dispatch(get(CREATE_VOUCHER_SESSION.REQUEST, `ondemand/v2/vouchers/${code}/videos/${assetID}?web=true`))
  }
  // If there is a season, we're looking at an episode in a TV series.
  else if (seasonID) {
      dispatch(get(CREATE_VOUCHER_SESSION.REQUEST, `ondemand/v2/vouchers/${code}/redeem?assetID=${seasonID}&web=true`))
  }
  // Otherwise we're redeeming the base asset related to the voucher.
  else {
      dispatch(get(CREATE_VOUCHER_SESSION.REQUEST, `ondemand/v2/vouchers/${code}/redeem?web=true`))
  }
}

export const createSubscriberSession = (assetID) => (dispatch, getState) => dispatch(
  post(CREATE_SUBSCRIBER_SESSION.REQUEST, `ondemand/v2/videos/${assetID}/vouchers?web=true`)
)

export const createPublicSession = (assetID) => (dispatch, getState) => dispatch(
  post(CREATE_SUBSCRIBER_SESSION.REQUEST, `ondemand/v2/videos/${assetID}/public?web=true`)
)

export const createLog = (sessionLogsURL, log) => dispatch =>
  fetch(sessionLogsURL, {
    method: 'POST',
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json"
    },
    body: JSON.stringify(log)
  })

export const getPlaybackPositions = () => (dispatch, getState) => {
  dispatch(
    get(
      GET_PLAYBACK_POSITIONS.REQUEST,
      `ondemand/v2/playback-positions`
    )
  )
}

export const resetSession = () => dispatch => dispatch({
  type: RESET_SESSION
})

export const getVoucherDetails = (code, relatedID = null, lang = null) => dispatch => {
  const queries = {
    ...relatedID ? { relatedID } : null,
    ...lang ? { lang } : null
  }
  dispatch(
    get(
      GET_VOUCHER_DETAILS.REQUEST,
      `ondemand/v2/vouchers/${code}/details?${(new URLSearchParams(queries)).toString()}`
    )
  )
}

export const markAdWatched = code => dispatch => dispatch(
  post(MARK_AD_WATCHED, `ondemand/v2/vouchers/${code}/mark-ad-watched`))
