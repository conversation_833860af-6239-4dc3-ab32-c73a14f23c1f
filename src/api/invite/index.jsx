import {
  GET_INVITE,
  CREATE_ACCOUNT,
  UPDATE_INVITE_FIELD
} from '/src/redux/invite/types'

import { get, post } from '../fetch'
import { toast } from "react-toastify"

export const getInvite = (token, callback) => dispatch => dispatch(
  get(
    GET_INVITE.REQUEST,
    `ondemand/invites/${token}`,
    null,
    () => callback()
  )
)

export const updateInvite = (key, value) => dispatch => dispatch({
  type: UPDATE_INVITE_FIELD,
  payload: { key, value }
})

export const createAccount = (token, callback) => (dispatch, getState) => dispatch(
  post(
    CREATE_ACCOUNT.REQUEST,
    `ondemand/invites/${token}`,
    {
      name: getState().invite.name,
      email: getState().invite.email,
      password: getState().invite.password
    },
    () => {
      toast.info("Your account has been created!")
      if (typeof callback === 'function') {
        callback()
      }
    }
  )
)
