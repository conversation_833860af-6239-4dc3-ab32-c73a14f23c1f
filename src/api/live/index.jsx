import {
  CHECK_LIVE_STREAM_STATUS,
  ASK_QUESTION,
  UPDATE_QUESTION,
  GET_QUESTIONS_BY_ASKER,
  AUTH_CHAT_USER,
  GET_LIVE_QUESTIONS,
  GET_LIVE_STREAM_ENDPOINTS,
  TOGGLE_BAN_USER_FROM_CHAT,
  GET_ACTIVE_SESSIONS,
  UPDATE_VOUCHER_CHAT_DETAILS
} from '/src/redux/live/types'
import { toast } from "react-toastify"
import { get, post } from '../fetch'

export const getLiveStreamStatus = (id, callback = null, errorCallback = null) => dispatch => dispatch(
  get(
    CHECK_LIVE_STREAM_STATUS.REQUEST,
    `ondemand/livestream-status?id=${id}`,
    res => {
      if (typeof callback === 'function') {
        callback(res)
      }
    },
    error => {
      if (typeof errorCallback === 'function') {
        errorCallback(error.message)
      }
    }
  )
)

export const getLiveStreamEndpoints = (id, voucherID = null, callback = null, errorCallback = null) => dispatch => dispatch(
  get(
    GET_LIVE_STREAM_ENDPOINTS.REQUEST,
    `ondemand/livestream-endpoints?id=${id}${voucherID ? `&voucherID=${voucherID}` : ''}`,
    res => {
      if (typeof callback === 'function') {
        callback(res)
      }
    },
    error => {
      if (typeof errorCallback === 'function') {
        errorCallback(error.message)
      }
    }
  )
)

export const askQuestion = (assetID, question, identifier, name, callback) => dispatch => dispatch(
  post(
    ASK_QUESTION.REQUEST,
    `ondemand/livestream/ask`,
    { assetID, question, identifier, name },
    res => {
      callback(res)
      toast.info("Question sent!")
    }
  )
)

export const getQuestionsByAsker = (assetID, identifier) => dispatch => dispatch(
  get(
    GET_QUESTIONS_BY_ASKER.REQUEST,
    `ondemand/livestream/questions?assetID=${assetID}&identifier=${identifier}`
  )
)

export const authUser = (voucherID = null, cb) => dispatch => dispatch(
  post(AUTH_CHAT_USER.REQUEST, `ondemand/chat/auth`, { voucherID }, res => {
    if (typeof cb === 'function') {
      cb(res.data.stream_token)
    }
  })
)

export const updateVoucherDetails = (code, name) => dispatch => dispatch(
  post(
    UPDATE_VOUCHER_CHAT_DETAILS.REQUEST,
    `ondemand/vouchers/${code}/chat-details`,
    { code, name },
  )
)

export const getQuestions = assetID => dispatch => dispatch(
  get(GET_LIVE_QUESTIONS.REQUEST, `ondemand/${assetID}/questions`)
)

export const updateQuestion = (assetID, questionID, data) => dispatch => dispatch(
  post(
    UPDATE_QUESTION.REQUEST,
    `ondemand/${assetID}/questions/${questionID}`,
    { ...data, questionID }
  )
)

export const moveQuestion = (assetID, questionID, direction) => dispatch => dispatch(
  post(
    UPDATE_QUESTION.REQUEST,
    `ondemand/${assetID}/questions/${questionID}/move`,
    { direction, questionID } 
  )
)

export const toggleBan = (id, modID, isBanned, queryUsers, client, channelID) => dispatch => dispatch(
  post(TOGGLE_BAN_USER_FROM_CHAT, 'ondemand/chat/toggle-ban',
    { userID: id, moderatorID: modID, isBanned: isBanned },
    res => {
      queryUsers(client, channelID)
      res.success ? toast.info(res.message) : toast.error(res.message)
    })
)

export const getActiveSessions = (id, setViewCount) => dispatch => dispatch(
  get(GET_ACTIVE_SESSIONS, `ondemand/live-stream/${id}/active-sessions`, res => setViewCount(res))
)
