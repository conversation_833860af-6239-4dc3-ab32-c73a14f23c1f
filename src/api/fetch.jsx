import config from '../config'

import {
  SET_JWT_TOKEN
} from '../redux/auth/types'

const DEFAULTS = {
  credentials: "same-origin",
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json"
  }
}

const POST_DEFAULTS = {
  method: "POST",
  ...DEFAULTS
}

const DELETE_DEFAULTS = {
  method: "DELETE",
  ...DEFAULTS
}

const PUT_DEFAULTS = {
  method: "PUT",
  ...DEFAULTS
}
//
export const post = (type, route, payload, callback, errorCb) => dispatch =>
  dispatch(api(type, route, getPayload(POST_DEFAULTS, payload, "post"), callback, errorCb, payload))
export const get = (type, route, callback, errorCb) => dispatch =>
  dispatch(api(type, route, getPayload(DEFAULTS, null, "get"), callback, errorCb))
export const put = (type, route, payload, callback, errorCb) => dispatch =>
  dispatch(api(type, route, getPayload(PUT_DEFAULTS, payload, "put"), callback, errorCb, payload))
export const del = (type, route, payload, callback, errorCb) => dispatch =>
  dispatch(api(type, route, getPayload(DELETE_DEFAULTS, payload, "delete"), callback, errorCb, payload))

const getPayload = (defaults, payload, type) => ({
  ...defaults,
  ...getBody(payload, type)
})

const getBody = (payload, type) => (
  (type === "get" || !payload) ? {} : { body: JSON.stringify(payload) }
)

const api = (type, route, payload, callback, errorCb, data) => (dispatch, getState) =>
  dispatch({
    type,
    meta: data,
    payload: new Promise((resolve, reject) =>
      fetch(process.env.VITE_BASE_API + route, {
        ...payload,
        headers: {
          ...payload.headers,
          Authorization: 'Bearer ' + (getState().auth.jwtToken || sessionStorage.getItem('previewToken') || localStorage.getItem('token'))
        }
      }).then(res => {
        const ok = res.ok
        const status = res.status
        res.json().then(json => {
          if (ok) {
            if (callback && typeof callback === "function") {
              callback(json)
            }
            resolve(json)
          }
          else {
            if (errorCb && typeof errorCb === "function") {
              errorCb(json)
            } else {
              reject({
                message: json.message,
                errors: json.errors ?? null,
                status
              })
            }
          }
          if (parseInt(status) < 400 && typeof json.data !== 'undefined' && typeof json.data.token === 'string' && json.data.token !== 'current') {
            // Only set the token to localStorage if there isn't a preview token in sessionStorage
            if (!sessionStorage.getItem('previewToken')) {
              localStorage.setItem('token', json.data.token)
            }
            dispatch({ type: SET_JWT_TOKEN, payload: json.data.token })
          }
        })
      })
      .catch(error => {
        if (errorCb && typeof errorCb === "function") {
          errorCb(error)
        }
        reject(error)
      })
    )
  })
