import {
  UPDATE_SEARCH_TERM,
  SEARCH,
  UPDATE_FILTER,
  SHOW_RESULTS,
  HIDE_RESULTS,
  UPDATE_CUSTOM_FILTER,
  TOGGLE_FILTERS,
  SHOW_SEARCH_INPUT
} from '/src/redux/search/types'
import { get } from '../fetch'

export const updateSearchTerm = term => dispatch => dispatch({
  type: UPDATE_SEARCH_TERM,
  payload: { term }
})

export const toggleFilters = () => dispatch => dispatch({
  type: TOGGLE_FILTERS
})

export const showSearchInput = (value) => dispatch => dispatch({
  type: SHOW_SEARCH_INPUT,
  payload: value
})

export const updateFilter = (key, value) => dispatch => dispatch({
  type: UPDATE_FILTER,
  payload: { key, value }
})

export const getSearchResults = (term, language) => (dispatch, getState) => {
  const availableCustomFields = getState().organization.custom_fields
  const customFields = []
  Object.entries(getState().search.customFields).forEach(fieldValue => {
    const customField = availableCustomFields.filter(customField => customField._id === fieldValue[0])[0]
    if (customField.type === 'dropdown' && fieldValue[1]) {
      customFields.push({
        type: customField.type,
        id: customField._id,
        value: fieldValue[1].map(f => f.value).join('|')
      })
    }
    if (customField.type === 'boolean' && fieldValue[1] === true) {
      customFields.push({
        type: customField.type,
        id: customField._id,
        value: fieldValue[1]
      })
    }
  })
  let params = {
    organizationID: getState().organization._id,
    term,
    countries: getState().search.countries.map(c => c.country_code).join('|'),
    durStart: getState().search.duration[0],
    durEnd: getState().search.duration[1],
    custom: JSON.stringify(customFields),
    language
  }
  if (getState().dashboard.filterTagIDs) {
    params.tagIDs = getState().dashboard.filterTagIDs
  }
  dispatch(
    get(
      SEARCH.REQUEST,
      `ondemand/dashboard/videos/search?${(new URLSearchParams(params)).toString()}`,
    )
  )
}

export const applyFilters = () => (dispatch, getState) => {
  dispatch({ type: SHOW_RESULTS })
  dispatch(getSearchResults(getState().search.term))
}

export const clearFilters = () => (dispatch, getState) => {
  dispatch(updateFilter('results', []))
  dispatch(updateFilter('countries', []))
  dispatch(updateFilter('duration', [0, 240]))
  dispatch(updateFilter('customFields', {}))
  dispatch(updateSearchTerm(''))
  dispatch({ type: HIDE_RESULTS })
}

export const updateCustomFilter = (key, value) => dispatch => dispatch({
  type: UPDATE_CUSTOM_FILTER,
  payload: { key, value }
})
