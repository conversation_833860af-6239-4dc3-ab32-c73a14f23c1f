import { applyMiddleware, compose, createStore } from 'redux'
import thunk from 'redux-thunk'
import createRootReducer from './reducer'
import promise from 'redux-promise-middleware'

export default function configureStore(preloadedState) {
  const middlewares = [thunk, promise]
  const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose

  const store = createStore(
    createRootReducer(), 
    preloadedState,
    composeEnhancers(applyMiddleware(...middlewares))
  )

  store.subscribe(() => {
    try {
      // const { dashboard, organization } = store.getState();
      // localStorage.setItem('dashboard_featured', JSON.stringify(dashboard.featured));
      // localStorage.setItem('dashboard_categories', JSON.stringify(dashboard.categories));
      // localStorage.setItem('organization', JSON.stringify(organization));
    } catch (e) {}
  })

  return store
}
