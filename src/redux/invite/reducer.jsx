import {
  GET_INVITE,
  UPDATE_INVITE_FIELD,
  CREATE_ACCOUNT
} from './types'

const initialState = {
  status: 'PENDING',
  name: '',
  email: '',
  password: '',
  isSubmitting: false
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_INVITE.PENDING: 
      return {
        ...state,
        status: 'PENDING'
      }
    case GET_INVITE.FULFILLED:
      return {
        ...state,
        status: 'READY',
        name: action.payload.invite.name,
        email: action.payload.invite.email
      }
    case GET_INVITE.REJECTED:
      return {
        ...state,
        status: 'FAILED',
        error: action.payload.message
      }
    case UPDATE_INVITE_FIELD:
      return {
        ...state,
        [action.payload.key]: action.payload.value
      }
    case CREATE_ACCOUNT.PENDING:
      return {
        ...state,
        isSubmitting: true
      }
    case CREATE_ACCOUNT.FULFILLED:
      return {
        ...state,
        isSubmitting: false
      }
  default:
    return state
  }
}