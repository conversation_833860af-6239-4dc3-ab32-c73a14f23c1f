import {
  UPDATE_SEARCH_TERM,
  SEARCH,
  UPDATE_FILTER,
  SHOW_RESULTS,
  HIDE_RESULTS,
  UPDATE_CUSTOM_FILTER,
  TOGGLE_FILTERS,
  SHOW_SEARCH_INPUT
} from './types'

import { LOG_IN } from '/src/redux/auth/types'

const initialState = {
  term: '',
  showResultsPage: false,
  status: 'PENDING',
  results: [],
  countries: [],
  duration: [0, 240],
  customFields: {},
  showFilters: false,
  showSearchInput: false
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case UPDATE_SEARCH_TERM:
      return {
        ...state,
        term: action.payload.term,
        showResultsPage: action.payload.term.length > 0,
        status: 'PENDING',
        results: [],
      }
    case SEARCH.PENDING:
      return {
        ...state,
        status: 'PENDING',
        results: [],
      }
    case SEARCH.FULFILLED:
      return {
        ...state,
        status: 'READY',
        results: action.payload.data.results,
      }
    case SEARCH.REJECTED:
      return {
        ...state,
        status: 'FAILED',
        results: [],
      }
    case LOG_IN.FULFILLED:
      return {
        ...state,
        term: '',
        status: 'PENDING',
        results: [],
      }
    case UPDATE_FILTER:
      return {
        ...state,
        [action.payload.key]: action.payload.value,
      }
    case SHOW_RESULTS:
      return {
        ...state,
        showResultsPage: true,
      }
    case HIDE_RESULTS:
      return {
        ...state,
        showResultsPage: false,
      }
    case UPDATE_CUSTOM_FILTER:
      let newCustomFields = state.customFields
      if (action.payload.value) {
        newCustomFields[action.payload.key] = action.payload.value
      } 
      else {
        delete newCustomFields[action.payload.key]
      }
      return {
        ...state,
        customFields: newCustomFields,
      }
    case TOGGLE_FILTERS:
      return {
        ...state,
        showFilters: !state.showFilters,
      }
    case SHOW_SEARCH_INPUT:
      return {
        ...state,
        showSearchInput: action.payload
      }
    default:
      return state
  }
}
