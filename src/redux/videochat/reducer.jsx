import {
  GET_ZOOM_CONFIG
} from './types'

const initialState = {
  status: '',
  config: {}
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_ZOOM_CONFIG.PENDING:
      return {
        ...state,
        status: 'PENDING'
      }
    case GET_ZOOM_CONFIG.REJECTED: 
      return {
        ...state,
        status: 'FAILED',
        error: action.payload.message
      }
    case GET_ZOOM_CONFIG.FULFILLED: 
      return {
        ...initialState,
        ...action.payload,
        status: 'READY'
      }
  default:
    return state
  }
}