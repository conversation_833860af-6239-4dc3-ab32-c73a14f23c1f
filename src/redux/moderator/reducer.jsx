import {
  GET_MODERATOR_LINK
} from './types'

const initialState = {
  status: 'PENDING',
  link: {}
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_MODERATOR_LINK.PENDING:
      return {
        ...state,
        status: 'PENDING'
      }
    case GET_MODERATOR_LINK.FULFILLED:
      return {
        ...state,
        status: 'READY',
        link: action.payload.moderatorLink
      }
  default:
    return state
  }
}