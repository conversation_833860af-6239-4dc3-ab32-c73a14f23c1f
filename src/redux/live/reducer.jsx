import {
  CHECK_LIVE_STREAM_STATUS,
  UPDATE_QUESTION,
  GET_LIVE_QUESTIONS,
  GET_LIVE_STREAM_ENDPOINTS,
  ASK_QUESTION,
  GET_QUESTIONS_BY_ASKER
} from './types'

const initialState = {
  status: 'PENDING',
  is_online: false,
  is_disconnected: false,
  endpoints: null,
  config: null,
  device: null,
  chat: {
    isAuthed: false
  },
  questions: [],
  pendingQuestionID: null,
  asked_questions: null
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case CHECK_LIVE_STREAM_STATUS.PENDING:
    case CHECK_LIVE_STREAM_STATUS.REJECTED:
      return {
        ...state,
        status: 'PENDING',
        is_online: false,
        is_disconnected: false
      }
    case CHECK_LIVE_STREAM_STATUS.FULFILLED:
      return {
        ...state,
        status: 'READY',
        is_online: !!action.payload.is_online,
        is_disconnected: !!action.payload.is_disconnected
      } 
    case UPDATE_QUESTION.PENDING:
      return {
        ...state,
        pendingQuestionID: action.meta.questionID
      }
    case GET_LIVE_QUESTIONS.FULFILLED:
    case UPDATE_QUESTION.FULFILLED:
      return {
        ...state,
        questions: action.payload.data.questions,
        pendingQuestionID: null
      }
    case GET_LIVE_STREAM_ENDPOINTS.FULFILLED:
      return {
        ...state,
        config: action.payload.config,
        endpoints: action.payload.endpoints,
        device: action.payload.device
      }
    case GET_LIVE_STREAM_ENDPOINTS.REJECTED:
      return {
        ...state,
        error: action.payload.message
      }
    case ASK_QUESTION.FULFILLED:
    case GET_QUESTIONS_BY_ASKER.FULFILLED:
      return {
        ...state,
        asked_questions: action.payload.data.questions
      }
  default:
    return state
  }
}