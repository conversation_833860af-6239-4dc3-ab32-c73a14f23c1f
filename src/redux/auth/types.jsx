export const AUTH_CHECK = {
  REQUEST: 'AUTH_CHECK',
  PENDING: 'AUTH_CHECK_PENDING',
  REJECTED: 'AUTH_CHECK_REJECTED',
  FULFILLED: 'AUTH_CHECK_FULFILLED'
}

export const REGISTER = {
  REQUEST: 'REGISTER',
  PENDING: 'REGISTER_PENDING',
  REJECTED: 'REGISTER_REJECTED',
  FULFILLED: 'REGISTER_FULFILLED'
}

export const LOG_IN = {
  REQUEST: 'LOG_IN',
  PENDING: 'LOG_IN_PENDING',
  REJECTED: 'LOG_IN_REJECTED',
  FULFILLED: 'LOG_IN_FULFILLED'
}

export const PIN_LOG_IN = {
  REQUEST: 'PIN_LOG_IN',
  PENDING: 'PIN_LOG_IN_PENDING',
  REJECTED: 'PIN_LOG_IN_REJECTED',
  FULFILLED: 'PIN_LOG_IN_FULFILLED'
}

export const LOG_OUT = {
  REQUEST: 'LOG_OUT',
  PENDING: 'LOG_OUT_PENDING',
  REJECTED: 'LOG_OUT_REJECTED',
  FULFILLED: 'LOG_OUT_FULFILLED'
}

export const SET_JWT_TOKEN = 'SET_JWT_TOKEN'

export const RESET_PASSWORD = {
  REQUEST: 'RESET_PASSWORD',
  PENDING: 'RESET_PASSWORD_PENDING',
  REJECTED: 'RESET_PASSWORD_REJECTED',
  FULFILLED: 'RESET_PASSWORD_FULFILLED'
}

export const SET_PASSWORD = {
  REQUEST: 'SET_PASSWORD',
  PENDING: 'SET_PASSWORD_PENDING',
  REJECTED: 'SET_PASSWORD_REJECTED',
  FULFILLED: 'SET_PASSWORD_FULFILLED'
}

export const PAIR_CODE = {
  REQUEST: 'PAIR_CODE',
  PENDING: 'PAIR_CODE_PENDING',
  REJECTED: 'PAIR_CODE_REJECTED',
  FULFILLED: 'PAIR_CODE_FULFILLED'
}

export const UPDATE_USER = {
  REQUEST: 'UPDATE_USER',
  PENDING: 'UPDATE_USER_PENDING',
  REJECTED: 'UPDATE_USER_REJECTED',
  FULFILLED: 'UPDATE_USER_FULFILLED'
}

export const UPDATE_USER_LANGUAGE = {
  REQUEST: 'UPDATE_USER_LANGUAGE',
  PENDING: 'UPDATE_USER_LANGUAGE_PENDING',
  REJECTED: 'UPDATE_USER_LANGUAGE_REJECTED',
  FULFILLED: 'UPDATE_USER_LANGUAGE_FULFILLED'
}

export const DELETE_SUBSCRIBER_DEVICE = {
  REQUEST: "DELETE_SUBSCRIBER_DEVICE",
  PENDING: "DELETE_SUBSCRIBER_DEVICE_PENDING",
  REJECTED: "DELETE_SUBSCRIBER_DEVICE_REJECTED",
  FULFILLED: "DELETE_SUBSCRIBER_DEVICE_FULFILLED"
}

export const GET_HEADERS = {
  REQUEST: "GET_HEADERS",
  PENDING: "GET_HEADERS_PENDING",
  REJECTED: "GET_HEADERS_REJECTED",
  FULFILLED: "GET_HEADERS_FULFILLED"
}