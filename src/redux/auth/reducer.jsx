import {
  AUTH_CHECK,
  REGISTER,
  LOG_IN,
  PIN_LOG_IN,
  SET_JWT_TOKEN,
  LOG_OUT,
  RESET_PASSWORD,
  SET_PASSWORD,
  PAIR_CODE,
  UPDATE_USER,
  UPDATE_USER_LANGUAGE,
  DELETE_SUBSCRIBER_DEVICE,
  GET_HEADERS
} from './types'
import { 
  CREATE_ACCOUNT
} from '../invite/types'
import {
  UPDATE_PREVIEW
} from '../preview/types'

const initialState = {
  status: 'PENDING',
  user: null,
  accessDevices: [],
  over_device_limit: false,
  jwtToken: null,
  reset: {
    isSubmitting: false,
    isSuccess: false,
    hasFailed: false
  },
  liveChatToken: null,
  registerError: false,
  registerErrors: [],
  headers: null
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case REGISTER.REJECTED:
      return {
        ...state,
        registerError: true,
        registerErrors: Object.values(action.payload.errors),
        status: 'ERROR'
      }
    case AUTH_CHECK.PENDING:
    case REGISTER.PENDING:
    case LOG_OUT.PENDING:
      return {
        ...state,
        status: 'PENDING'
      }
    case AUTH_CHECK.REJECTED: 
      return {
        ...state,
        status: 'UNAUTHENTICATED'
      }
    case LOG_IN.PENDING:
      return {
        ...state,
        status: 'LOGIN_PENDING'
      }
    case LOG_IN.REJECTED:
      return {
        ...state,
        status: 'UNAUTHENTICATED',
        error: action.payload.message
      }
    case PIN_LOG_IN.PENDING:
      return {
        ...state,
        pin_status: 'LOGIN_PENDING'
      }
    case PIN_LOG_IN.REJECTED:
      return {
        ...state,
        pin_status: 'UNAUTHENTICATED',
        pin_error: action.payload.message
      }
    case CREATE_ACCOUNT.FULFILLED:
    case REGISTER.FULFILLED:
    case AUTH_CHECK.FULFILLED: 
    case LOG_IN.FULFILLED:
    case PIN_LOG_IN.FULFILLED:
    case UPDATE_PREVIEW.FULFILLED:
      if (action.payload.data.over_device_limit) {
        if (window.location.pathname !== `/settings/devices`) {
          window.location.href = `/settings/devices`
        }
      }
      return {
        ...state,
        status: action.payload.data.user ? 'AUTHENTICATED' : 'UNAUTHENTICATED',
        pin_status: action.payload.data.user ? 'AUTHENTICATED' : 'UNAUTHENTICATED',
        user: action.payload.data.user,
        accessDevices: action.payload.data.devices,
        over_device_limit: action.payload.data.over_device_limit,
        registerError: false,
        admin: action.payload.data.admin
      }
    case DELETE_SUBSCRIBER_DEVICE.FULFILLED:
      return {
        ...state,
        accessDevices: action.payload.devices,
        over_device_limit: action.payload.over_device_limit
      }
    case SET_JWT_TOKEN:
      return {
        ...state,
        jwtToken: action.payload
      }
    case LOG_OUT.FULFILLED:
      return {
        ...initialState,
        status: 'UNAUTHENTICATED'
      }
    case RESET_PASSWORD.PENDING:
    case SET_PASSWORD.PENDING:
      return {
        ...state,
        reset: {
          ...state.reset,
          isSubmitting: true,
          isSuccess: false
        }
      }
    case RESET_PASSWORD.FULFILLED:
    case SET_PASSWORD.FULFILLED:
      return {
        ...state,
        reset: {
          ...state.reset,
          isSubmitting: false,
          isSuccess: true,
          error: null
        }
      }
    case RESET_PASSWORD.REJECTED:
    case SET_PASSWORD.REJECTED:
      return {
        ...state,
        reset: {
          ...state.reset,
          isSubmitting: false,
          isSuccess: false,
          hasFailed: true,
          error: action.payload.message
        }
      }
    case PAIR_CODE.PENDING:
      return {
        ...state,
        pairingStatus: 'PENDING'
      }
    case PAIR_CODE.REJECTED:
      return {
        ...state,
        pairingStatus: 'FAILED',
        pairingError: action.payload.message
      }
    case PAIR_CODE.FULFILLED:
      return {
        ...state,
        pairingStatus: 'SUCCESS'
      }
    // case UPDATE_USER.PENDING:
    case UPDATE_USER_LANGUAGE.PENDING:
      return {
        ...state,
        status: 'PENDING'
      }
    case UPDATE_USER.REJECTED:
      return {
        ...state,
        status: 'AUTHENTICATED'
      }
    case UPDATE_USER.FULFILLED:
    case UPDATE_USER_LANGUAGE.FULFILLED:
      return {
        ...state,
        status: 'AUTHENTICATED',
        user: action.payload.data.user
      }
    case GET_HEADERS.FULFILLED:
      return {
        ...state,
        headers: action.payload
      }
  default:
    return state
  }
}
