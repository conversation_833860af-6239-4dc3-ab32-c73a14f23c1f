import {
  CREATE_VOUCHER_SESSION,
  CREATE_SUBSCRIBER_SESSION,
  RESET_SESSION,
  GET_VOUCHER_DETAILS,
  GET_PLAYBACK_POSITIONS,
  SET_SESSION_VIDEO_RATING,
  SET_SESSION_PLAYLIST_ASSET_RATING
} from './types'
import { UPDATE_VOUCHER_CHAT_DETAILS } from '../live/types'
import {
  LOG_OUT
} from '../auth/types'

const MSG_UNAVAILABLE = "This content isn't available now. If this is a scheduled event, please check back later when the event is scheduled to start."

const initialState = {
  status: null,
  error: '',
  video: {},
  videos: null,
  endpoints: null,
  voucherID: null,
  device: {},
  config: {},
  asset: {},
  landingDetails: {
    status: 'PENDING',
    video: {
      call_to_actions: []
    }
  },
  playbackPositions: {},
  latestPlaybackVideoID: null,
  playbackPositionsStatus: 'PENDING'
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case CREATE_SUBSCRIBER_SESSION.PENDING:
    case CREATE_VOUCHER_SESSION.PENDING:
      return {
        ...state,
        status: 'PENDING',
        error: '',
        video: null,
        videos: null,
        endpoints: null,
        voucherID: null
      }
    case CREATE_SUBSCRIBER_SESSION.REJECTED:
    case CREATE_VOUCHER_SESSION.REJECTED: 
      return {
        ...state,
        status: action.payload.message === 'unpublished' ? 'NOT_PLAYABLE' : 'FAILED',
        error: action.payload.message === 'unpublished' ? MSG_UNAVAILABLE : action.payload.message
      }
    case CREATE_SUBSCRIBER_SESSION.FULFILLED:
    case CREATE_VOUCHER_SESSION.FULFILLED: 
      return {
        ...state,
        ...action.payload.data,
        status: 'READY'
      }
    case GET_VOUCHER_DETAILS.PENDING:
      return {
        ...state,
        landingDetails: {
          ...state.landingDetails,
          ...initialState.landingDetails
        }
      }
    case GET_VOUCHER_DETAILS.REJECTED:
      return {
        ...state,
        landingDetails: {
          ...state.landingDetails,
          status: action.payload.message === 'unpublished' ? 'NOT_PLAYABLE' : 'FAILED',
          error: action.payload.message === 'unpublished' ? MSG_UNAVAILABLE : action.payload.message
        }
      }
    case GET_VOUCHER_DETAILS.FULFILLED:
    case UPDATE_VOUCHER_CHAT_DETAILS.FULFILLED:
      return {
        ...state,
        landingDetails: {
          status: 'READY',
          video: action.payload.data
        }
      }
    case GET_PLAYBACK_POSITIONS.PENDING:
      return {
        ...state,
        playbackPositionsStatus: 'PENDING'
      }
    case GET_PLAYBACK_POSITIONS.FULFILLED:
      return {
        ...state,
        playbackPositionsStatus: 'READY',
        latestPlaybackVideoID: action.payload.data.latestPlaybackVideoID,
        playbackPositions: {
          ...state.playbackPositions,
          ...action.payload.data.playbackPositions
        }
      }
    case SET_SESSION_VIDEO_RATING:
      return {
        ...state,
        landingDetails: {
          ...state.landingDetails,
          video: {
            ...state.landingDetails.video,
            rating: action.payload.rating
          }
        }
      }
    case SET_SESSION_PLAYLIST_ASSET_RATING:
      return {
        ...state,
        landingDetails: {
          ...state.landingDetails,
          video: {
            ...state.landingDetails.video,
            titles: state.landingDetails.video.titles.map(video => (video._id === action.payload.id) ? { ...video, rating: action.payload.rating } : video)
          }
        }
      }
    case LOG_OUT.FULFILLED:
    case RESET_SESSION:
      return initialState
  default:
    return state
  }
}