import {
  PAYMENT_TOGGLE_MODAL,
  GET_PAYMENT_HISTORY,
  POST_PAYMENTS,
  RESET_STATE,
  GET_AVAILABLE_SUBSCRIPTIONS,
  POST_UPDATE_SUBSCRIPTION,
  CREATE_BILLING_PORTAL
} from './types'

import { get, post, put } from '/src/api/fetch'

export const toggleModal = (item, isSubscription = false) => (dispatch) => {
  dispatch({
    type: PAYMENT_TOGGLE_MODAL,
    payload: { item, isSubscription }
  })
}

export const setPaymentMessage = (status) => (dispatch) => {
  dispatch({ type: status })
}

export const postPayment = (payload) => (dispatch) => {
  return dispatch(post(POST_PAYMENTS.REQUEST,
    `ondemand/process-payment`,
    payload
  ))
}

export const getPaymentHistory = () => (dispatch) => {
  dispatch(get(GET_PAYMENT_HISTORY.REQUEST,
    `ondemand/subscriber-history`,
  ))
}

export const getAvailableSubscriptions = () => (dispatch) => {
  dispatch(get(GET_AVAILABLE_SUBSCRIPTIONS.REQUEST,
    'ondemand/available-subscriptions'
  ))
}

export const resetState = () => (dispatch) => {
  dispatch({ type: RESET_STATE });
}

export const updateSubscription = (id, payload) => (dispatch) => {
  return dispatch(put(POST_UPDATE_SUBSCRIPTION.REQUEST,
    `ondemand/subscription/${id}/update`,
    payload
  ))
}

export const createBillingPortal = () => (dispatch) => {
  return dispatch(get(CREATE_BILLING_PORTAL.REQUEST,
    'ondemand/billing-portal',
    res => {
      if (res.url) {
        window.open(res.url, '_blank')
      }
    }
  ));
};
