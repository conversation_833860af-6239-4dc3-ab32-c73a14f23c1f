import {
  POST_PAYMENTS,
  GET_PAYMENT_HISTORY,
  PAYMENT_TOGGLE_MODAL,
  RESET_STATE,
  GET_AVAILABLE_SUBSCRIPTIONS,
  POST_UPDATE_SUBSCRIPTION,
  CREATE_BILLING_PORTAL
} from './types'

const initialState = {
  status: 'WAITING',
  item: null,
  modal_active: false,
  latest_voucher: null,
  payment_message: '',
  payment_status: 'PENDING',
  isSubscription: false,
  history: [],
  processed: [],
  upcoming: [],
  availableSubscriptions: [],
  allSubscriptions: [],
  availableSubscriptionsStatus: '',
  portalSessionStatus: "WAITING"
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case PAYMENT_TOGGLE_MODAL:
      return {
        ...state,
        item: action.payload.item,
        isSubscription: action.payload.isSubscription,
        modal_active: !state.modal_active
      }
    case GET_PAYMENT_HISTORY.PENDING:
    case POST_PAYMENTS.PENDING:
    case GET_PAYMENT_HISTORY.REQUEST:
    case POST_PAYMENTS.REQUEST:
      return {
        ...state,
        status: 'PENDING'
      }
    case GET_PAYMENT_HISTORY.REJECTED:
    case POST_PAYMENTS.REJECTED:
      return {
        ...state,
        payment_message: action.payload.message,
        status: 'ERROR'
      }
    case GET_PAYMENT_HISTORY.FULFILLED:
      return {
        ...state,
        history: action.payload.history,
        processed: action.payload.processed,
        upcoming: action.payload.upcoming,
        status: 'COMPLETE'
      }
    case POST_PAYMENTS.FULFILLED:
      return {
        ...state,
        payment_status: 'COMPLETE',
        status: 'COMPLETE'
      }
      case GET_AVAILABLE_SUBSCRIPTIONS.PENDING:
        return {
          ...state,
          availableSubscriptionsStatus: 'PENDING'
        }
    case GET_AVAILABLE_SUBSCRIPTIONS.FULFILLED:
      return {
        ...state,
        availableSubscriptions: action.payload.availableSubscriptions,
        allSubscriptions: action.payload.allSubscriptions,
        availableSubscriptionsStatus: 'COMPLETE'
      }
    case POST_UPDATE_SUBSCRIPTION.PENDING:
      return {
        ...state,
        availableSubscriptionsStatus: 'PENDING'
      }
    case POST_UPDATE_SUBSCRIPTION.REJECTED:
      return {
        ...state,
        availableSubscriptionsStatus: 'ERROR'
      }
    case POST_UPDATE_SUBSCRIPTION.FULFILLED: 
      return {
        ...state,
        availableSubscriptionsStatus: 'COMPLETE',
        allSubscriptions: action.payload.all_subscriptions
      }
    case CREATE_BILLING_PORTAL.PENDING:
      return {
        ...state,
        portalSessionStatus: "PENDING"
      }
    case CREATE_BILLING_PORTAL.FULFILLED: 
      return {
        ...state,
        portalSessionStatus: "COMPLETE"
      }
    case RESET_STATE:
      return {
        ...initialState,
        availableSubscriptions: state.availableSubscriptions,
        allSubscriptions: state.allSubscriptions
      }
    default:
      return state
  }
}
