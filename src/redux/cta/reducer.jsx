import {
  GET_CTA
} from './types'

const initialState = {
  status: 'PENDING',
  attributes: null
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_CTA.PENDING:
      return {
        ...state,
        status: 'PENDING',
        attributes: null
      }
    case GET_CTA.REJECTED:
      return {
        ...state,
        status: 'FAILED',
        attributes: null
      }
    case GET_CTA.FULFILLED:
      return {
        ...state,
        status: 'READY',
        attributes: action.payload.data.cta
      }
      default:
    return state
  }
}