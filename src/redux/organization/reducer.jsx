import getTextColor from 'helpers/get_text_color'
import {
  GET_ORGANIZATION
} from './types'
import getThemeTextColor from 'helpers/get_theme_text_color'

let initialState = {
  status: 'PENDING',
  name: '',
  settings: {},
  accent_color: "#ff0000",
  terms_and_conditions: {},
  tv_apps: {},
  external_authentication: {},
  custom_script_urls: [],
  background_color: '#000000',
  dashboard_sections: [],
}

try {
  const fromStorage = JSON.parse(localStorage.getItem('organization'))
  initialState = fromStorage ?? initialState
} catch (e) {}


export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_ORGANIZATION.PENDING:
      return {
        ...state,
        status: 'PENDING'
    }
    case GET_ORGANIZATION.FULFILLED:
      return {
        ...state,
        ...action.payload.organization,
        background_color: action.payload.organization.background_color,
        background_overlay_opacity: action.payload.organization.background_overlay_opacity,
        foreground_color: getTextColor(action.payload.organization.background_color),
        theme_text_color: getThemeTextColor(action.payload.organization.use_light_theme),
        recommendation: null,
        status: 'READY'
      }
    case GET_ORGANIZATION.REJECTED:
      return action.payload.message.organization ? {
        ...state,
        ...action.payload.message.organization,
        recommendation: action.payload.message.recommendation ?? null,
        status: 'READY'
      } : {
        ...state,
        status: 'FAILED'
      }
  default:
    return state
  }
}
