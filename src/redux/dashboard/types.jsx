export const GET_DASHBOARD = {
  REQUEST: 'GET_DASHBOARD',
  PENDING: 'GET_DASHBOARD_PENDING',
  REJECTED: 'GET_DASHBOARD_REJECTED',
  FULFILLED: 'GET_DASHBOARD_FULFILLED'
}

export const SET_CATEGORIES = 'SET_CATEGORIES'

export const GET_DASHBOARD_FEATURED_CATEGORY = {
  REQUEST: 'GET_DASHBOARD_FEATURED_CATEGORY',
  PENDING: 'GET_DASHBOARD_FEATURED_CATEGORY_PENDING',
  REJECTED: 'GET_DASHBOARD_FEATURED_CATEGORY_REJECTED',
  FULFILLED: 'GET_DASHBOARD_FEATURED_CATEGORY_FULFILLED'
}

export const GET_NEXT_CATEGORIES = {
  REQUEST: 'GET_NEXT_CATEGORIES',
  PENDING: 'GET_NEXT_CATEGORIES_PENDING',
  REJECTED: 'GET_NEXT_CATEGORIES_REJECTED',
  FULFILLED: 'GET_NEXT_CATEGORIES_FULFILLED'
}

export const GET_VIDEO = {
  REQUEST: 'GET_VIDEO',
  PENDING: 'GET_VIDEO_PENDING',
  REJECTED: 'GET_VIDEO_REJECTED',
  FULFILLED: 'GET_VIDEO_FULFILLED'
}

export const RATE_ASSET = {
  REQUEST: 'RATE_ASSET',
  PENDING: 'RATE_ASSET_PENDING',
  REJECTED: 'RATE_ASSET_REJECTED',
  FULFILLED: 'RATE_ASSET_FULFILLED'
}

export const SET_ACTIVE_SECTION = "SET_ACTIVE_SECTION"

export const SET_VIDEO_RATING = 'SET_VIDEO_RATING'
export const SET_PLAYLIST_ASSET_RATING = 'SET_PLAYLIST_ASSET_RATING'

export const SET_CATEGORY_SLIDE_INDEX = 'SET_CATEGORY_SLIDE_INDEX'

export const GET_ASSET_LIST = {
  REQUEST: 'GET_ASSET_LIST',
  PENDING: 'GET_ASSET_LIST_PENDING',
  REJECTED: 'GET_ASSET_LIST_REJECTED',
  FULFILLED: 'GET_ASSET_LIST_FULFILLED'
}

export const ADD_OR_REMOVE_ASSET_LIST = {
  REQUEST: 'ADD_OR_REMOVE_ASSET_LIST',
  PENDING: 'ADD_OR_REMOVE_ASSET_LIST_PENDING',
  REJECTED: 'ADD_OR_REMOVE_ASSET_LIST_REJECTED',
  FULFILLED: 'ADD_OR_REMOVE_ASSET_LIST_FULFILLED'
}

export const SET_REDIRECT = 'SET_REDIRECT'

export const SET_LANGUAGE_CODE_FILTER = 'SET_LANGUAGE_CODE_FILTER'
export const SET_FILTER_GENRE = 'SET_FILTER_GENRE'
export const SET_FILTER_TAGS = 'SET_FILTER_TAGS'
export const SET_LANGUAGE_CODE = 'SET_LANGUAGE_CODE'

export const SET_FOOTER_HEIGHT = 'SET_FOOTER_HEIGHT'

export const POST_ASSET_REGISTER = {
  REQUEST: 'POST_ASSET_REGISTER',
  PENDING: 'POST_ASSET_REGISTER_PENDING',
  REJECTED: 'POST_ASSET_REGISTER_REJECTED',
  FULFILLED: 'POST_ASSET_REGISTER_FULFILLED'
}

export const REPOPULATE_DEFAULT_TAGS = 'REPOPULATE_DEFAULT_TAGS'
