import {
  GET_DASHBOARD,
  SET_CATEGORIES,
  GET_DASHBOARD_FEATURED_CATEGORY,
  GET_NEXT_CATEGORIES,
  GET_VIDEO,
  SET_VIDEO_RATING,
  SET_CATEGORY_SLIDE_INDEX,
  ADD_OR_REMOVE_ASSET_LIST,
  GET_ASSET_LIST,
  SET_REDIRECT,
  SET_LANGUAGE_CODE_FILTER,
  SET_PLAYLIST_ASSET_RATING,
  SET_ACTIVE_SECTION,
  SET_FILTER_GENRE,
  SET_FILTER_TAGS,
  SET_FOOTER_HEIGHT,
  SET_LANGUAGE_CODE,
  POST_ASSET_REGISTER,
  REP<PERSON>ULATE_DEFAULT_TAGS
} from './types'
import {
  GET_ORGANIZATION
} from '../organization/types'
import {
  AUTH_CHECK,
  LOG_IN,
  UPDATE_USER_LANGUAGE
} from '../auth/types'
import {
  LOG_OUT
} from '../auth/types'
import getDashboard<PERSON>ey from 'helpers/get_dashboard_key'

let initialState = {
  categories: {
    list: [],
    status: 'PENDING'
  },
  featured: {
    category: null,
    status: 'PENDING'
  },
  cachedDashboards: {
  },
  groupedResults: [],
  video: {},
  videoStatus: '',
  videoErrorMessage: null,
  isRatingVideo: false,
  mylist: {
    status: 'PENDING',
    items: []
  },
  isAddingOrRemovingVideoList: false,
  redirectTo: null,
  activeLanguageCodeFilter: null,
  language: null,
  activeSectionID: '',
  filterGenreID: '',
  filterTagIDs: '',
  footerHeight: null,
  registerStatus: ''
}

// try {
//
//   // If the featured category is in storage, let's use it to render more quickly.
//   const dashboardFeaturedCategory = JSON.parse(localStorage.getItem('dashboard_featured'))
//   if (dashboardFeaturedCategory && dashboardFeaturedCategory.status === "READY") {
//     initialState.featured = dashboardFeaturedCategory
//   }
//
//   // If the categories list is in storage, let's use it to render more quickly.
//   const dashboardCategories = JSON.parse(localStorage.getItem('dashboard_categories'))
//   if (dashboardCategories && dashboardCategories.status === "READY") {
//     initialState.categories = dashboardCategories
//   }
//
// } catch (e) {}

function mergeListsWithoutDuplicates(listOne, listTwo) {
  var ids = new Set(listOne.map(d => d._id));
  var merged = [...listOne, ...listTwo.filter(d => !ids.has(d._id))];
  return merged.sort((a, b) => a.index - b.index)
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case SET_ACTIVE_SECTION:
      return {
        ...state,
        activeSectionID: action.payload.sectionID
      }
    case SET_FILTER_GENRE:
      return {
        ...state,
        filterGenreID: action.payload.filterGenreID
      }
    case SET_FILTER_TAGS:
      return {
        ...state,
        filterTagIDs: action.payload.filterTagIDs
      }
    case SET_LANGUAGE_CODE:
      return {
        ...state,
        language: action.payload.code
      }
    case AUTH_CHECK.FULFILLED:
    case LOG_IN.FULFILLED:
    case UPDATE_USER_LANGUAGE.FULFILLED:
      return {
        ...state,
        language: action.payload.data?.user?.active_language_code,
      }
    case REPOPULATE_DEFAULT_TAGS:
    case GET_ORGANIZATION.FULFILLED:
      const defaultTags = action.payload.organization.tag_filters
        .filter(opt => opt.default_filter_tag_id)

      const currentTagIDs = state.filterTagIDs ? state.filterTagIDs.split(",") : []

      // Find which tag filters don't have any selected tags
      const missingDefaults = defaultTags
        .filter(tagFilter => !tagFilter.tag_ids.some(tagID => currentTagIDs.includes(tagID)))
        .map(tagFilter => tagFilter.default_filter_tag_id)

      const mergedTagIDs = [...currentTagIDs, ...missingDefaults].join(",")

      return {
        ...state,
        filterTagIDs: mergedTagIDs || state.filterTagIDs
      }
    case GET_DASHBOARD.PENDING:
      let getterKey = getDashboardKey(state.activeSectionID, state.filterTagIDs, state.filterGenreID, state.language)
      // If we have something stored in cached dashboards, use it temporarily first!
      if (state.cachedDashboards[getterKey]) {
        return {
          ...state,
          categories: {
            status: 'READY',
            list: state.cachedDashboards[getterKey]
          }
        }
      }
      // Otherwise return initial state...
      return {
        ...state,
        categories: {
          list: [],
          status: ''
        },
        featured: {
          category: null,
          status: ''
        }
      }
    case GET_DASHBOARD.REJECTED:
      return {
        ...state,
        categories: {
          ...state.categories,
          status: 'FAILED',
          message: action.payload.message
        },
        featured: {
          ...state.featured,
          status: 'FAILED',
          message: action.payload.message
        }
      }
    case GET_DASHBOARD.FULFILLED:
      let setterKey = getDashboardKey(state.activeSectionID, state.filterTagIDs, state.filterGenreID, state.language)
      let resultKey = getDashboardKey(action.payload.data.sectionID, action.payload.data.tagIDs, action.payload.data.genreID, action.payload.data.language)
      return {
        ...state,
        categories: {
          // If the result key does not match the one in state, we do not override the values!
          list: setterKey === resultKey ? action.payload.data.categories : state.categories.list,
          status: 'READY'
        },
        // featured: {
        //   category: action.payload.data.featuredCategory,
        //   status: 'READY'
        // },
        allData: action.payload.data,
        groupedResults: action.payload.data.groupedResults,
        cachedDashboards: {
          ...state.cachedDashboards,
          [resultKey]: action.payload.data.categories
        }
      }
    case GET_DASHBOARD_FEATURED_CATEGORY.FULFILLED:
      return {
        ...state,
        featured: {
          category: action.payload.data.featuredCategory,
          status: 'READY'
        }
      }
    case SET_CATEGORIES:
      return {
        ...state,
        categories: {
          list: action.payload,
          status: 'LOADING'
        }
      }
    case GET_NEXT_CATEGORIES.PENDING:
      return {
        ...state,
        dashboardKey: getDashboardKey(state.activeSectionID, state.filterTagIDs, state.filterGenreID, state.language)
      }
    case GET_NEXT_CATEGORIES.FULFILLED:
      let nextCategoryResultKey = getDashboardKey(action.payload.data.sectionID, action.payload.data.tagIDs, action.payload.data.genreID, action.payload.data.language)

      // If the current dashboard key and the one returned from the API are the same, we can append the category to the current list.
      // Otherwise we are just going to keep the list the same!
      const newList = state.dashboardKey === nextCategoryResultKey
        ? mergeListsWithoutDuplicates(state.categories.list, action.payload.data.categories)
        : state.categories.list

      return {
        ...state,
        categories: {
          status: action.payload.data.total <= action.payload.data.index ? 'READY' : 'LOADING',
          list: newList
        }
      }
    case GET_VIDEO.PENDING:
      return {
        ...state,
        videoStatus: 'PENDING',
        video: {}
      }
    case GET_VIDEO.REJECTED:
      return {
        ...state,
        videoStatus: 'FAILED',
        videoErrorMessage: action.payload.message
      }
    case GET_VIDEO.FULFILLED:
      return {
        ...state,
        videoStatus: 'READY',
        video: action.payload.data.video
      }
    case LOG_OUT.FULFILLED:
      return initialState
    case SET_VIDEO_RATING:
      return {
        ...state,
        video: {
          ...state.video,
          rating: action.payload.rating
        }
      }
    case SET_CATEGORY_SLIDE_INDEX:
      return {
        ...state,
        categories: {
          ...state.categories,
          list: state.categories.list.map((category, i) => {
            return {
              ...category,
              ...(i === action.payload.catIdx) ? {
                slideIdx: action.payload.slideIdx
              } : {}
            }
          })
        }
      }
    case ADD_OR_REMOVE_ASSET_LIST.PENDING:
      return {
        ...state,
        mylist: {
          ...state.mylist,
          isAddingOrRemoving: true
        }
      }
    case ADD_OR_REMOVE_ASSET_LIST.FULFILLED:
      return {
        ...state,
        mylist: {
          ...state.mylist,
          isAddingOrRemoving: false,
          items: action.payload.data.assets
        }
      }
    case GET_ASSET_LIST.PENDING:
      return {
        ...state,
        mylist: {
          ...state.mylist,
          status: 'PENDING',
          items: []
        }
      }
    case GET_ASSET_LIST.FULFILLED:
      return {
        ...state,
        mylist: {
          ...state.mylist,
          status: 'READY',
          items: action.payload.data.assets
        }
      }
    case GET_ASSET_LIST.REJECTED:
      return {
        ...state,
        mylist: {
          ...state.mylist,
          status: 'FAILED',
          items: []
        }
      }
    case SET_REDIRECT:
      return {
        ...state,
        redirectTo: action.payload.to
      }
    case SET_FOOTER_HEIGHT:
      return {
        ...state,
        footerHeight: action.payload
      }
    case SET_LANGUAGE_CODE_FILTER:
      // If we're adding a filter, save values and filter.
      if (action.payload.code) {
        return {
          ...state,
          activeLanguageCodeFilter: action.payload.code,
          categories: {
            ...state.categories,
            list: state.allData.categories.map(category => ({
              ...category,
              videos: category.videos.filter(video => {
                const languages = video.languages ? video.languages : ''
                const subtitles = video.subtitles ? video.subtitles : ''
                return languages.includes(action.payload.code) || subtitles.includes(action.payload.code)
              })
            }))
          }
          // featured: {
          //   ...state.featured,
          //   category: {
          //     ...state.featured.category,
          //     videos: state.allData.featuredCategory.videos.filter(video => video.title === 'Up')
          //   }
          // }
        }
      }
      // If we're clearing the filter, revert to saved values...
      else {
        return {
          ...state,
          activeLanguageCodeFilter: action.payload.code,
          categories: {
            list: state.allData.categories,
            status: 'READY'
          },
          featured: {
            category: state.allData.featuredCategory,
            status: 'READY'
          }
        }
      }
    case SET_PLAYLIST_ASSET_RATING:
      return {
        ...state,
        video: {
          ...state.video,
          titles: state.video.titles.map(video => {
            if (video._id === action.payload.id) {
              return {
                ...video,
                rating: action.payload.rating
              }
            }
            return video
          })
        }
      }
    case POST_ASSET_REGISTER.PENDING:
      return {
        ...state,
        register_status: 'PENDING',
      }
    case POST_ASSET_REGISTER.FULFILLED:
      return {
        ...state,
        register_status: 'COMPLETE',
        video: {
          ...state.video,
          is_registered: true
        }
      }
    default:
      return state
  }
}
