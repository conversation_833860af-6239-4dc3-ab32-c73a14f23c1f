import { combineReducers } from 'redux'
import auth from './auth/reducer'
import session from './session/reducer'
import organization from './organization/reducer'
import dashboard from './dashboard/reducer'
import category from './category/reducer'
import invite from './invite/reducer'
import search from './search/reducer'
import utils from './utils/reducer'
import videochat from './videochat/reducer'
import ads from './ads/reducer'
import live_stream from './live/reducer'
import cta from './cta/reducer'
import moderator from './moderator/reducer'
import payments from './payments/reducer'
import preview from './preview/reducer'

const createRootReducer = () => combineReducers({
  auth,
  organization,
  session,
  dashboard,
  category,
  invite,
  search,
  utils,
  videochat,
  ads,
  live_stream,
  cta,
  moderator,
  payments,
  preview
})

export default createRootReducer
