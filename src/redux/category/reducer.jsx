import {
  GET_CATEGORY,
} from './types'

let initialState = {
  status: 'PENDING',
  model: {}
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case GET_CATEGORY.PENDING:
      return {
        ...state,
        status: 'PENDING'
      }
    case GET_CATEGORY.REJECTED:
      return {
        ...state,
        status: 'FAILED'
      }
    case GET_CATEGORY.FULFILLED:
      return {
        ...state,
        status: 'READY',
        model: action.payload.data.category
      }
  default:
    return state
  }
}