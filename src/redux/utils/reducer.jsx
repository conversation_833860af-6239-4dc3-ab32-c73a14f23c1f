import {
  SET_UTIL_VALUE,
  GET_COUNTRIES
} from './types'

const initialState = {
  countries: {
    list: [],
    status: 'PENDING'
  }
}

export default function reducer(state = initialState, action = {}) {
  switch (action.type) {
    case SET_UTIL_VALUE:
      return {
        ...state,
        [action.payload.key]: action.payload.value
      }
    case GET_COUNTRIES.PENDING:
      return {
        ...state,
        countries: {
          ...state.countries,
          status: 'PENDING'
        }
      }
    case GET_COUNTRIES.REJECTED:
      return {
        ...state,
        countries: {
          ...state.countries,
          status: 'FAILED'
        }
      }
    case GET_COUNTRIES.FULFILLED:
      return {
        ...state,
        countries: {
          ...state.countries,
          list: action.payload,
          status: 'READY'
        }
      }
  default:
    return state
  }
}