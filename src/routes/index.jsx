import React from 'react'
import { connect } from 'react-redux'
import { <PERSON>rows<PERSON><PERSON>out<PERSON>, Route } from 'react-router-dom'
import AlertPage from '../pages/AlertPage'
import AllRoutes from './all_routes'
import PreviewBar from '../components/PreviewBar'

const Routes = ({ maintenanceModeEnabled, invalidDevice, includePreviewBar }) => {

  return (<>
    {includePreviewBar && <PreviewBar />}
    {(maintenanceModeEnabled || invalidDevice) ?
      <Route path="*" component={AlertPage} /> :
      <AllRoutes />
    }
  </>
  )
}

const mapStateToProps = state => ({
  maintenanceModeEnabled: state.organization.maintenance_mode_enabled,
  invalidDevice: !!state.organization.recommendation,
  includePreviewBar: state.auth.admin
})

export default connect(mapStateToProps)(Routes)
