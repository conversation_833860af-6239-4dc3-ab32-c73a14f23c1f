import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom'
import ReactGA from 'react-ga'
import ReactGA4 from 'react-ga4'
import Home from '../pages/Home'
import Dashboard from '../pages/Dashboard'
import VideoDetails from '../pages/VideoDetails'
import VoucherPlayer from '../pages/VoucherPlayer'
import VideoPlayer from '../pages/VideoPlayer'
import Invite from '../pages/Invite'
import Reset from '../pages/Reset'
import VideoChat from '../pages/VideoChat'
import MyList from '../pages/MyList'
import VoucherDetails from '../pages/VoucherDetails'
import AuthorizationCallbackPage from '../pages/AuthorizationCallbackPage'
import LiveStream from '../pages/LiveStream'
import LiveInteraction from '../pages/LiveInteraction'
import ModeratorLink from '../pages/ModeratorLink'
import Category from '../pages/Category'
import CustomHTML from '../pages/CustomHTML'
import Settings from '../pages/Settings'
import Register from '../pages/Register'
import { Status } from '@bitcine/cinesend-theme'
import Headers from '../pages/Headers'

const AllRoutes = ({
  authenticated,
  isPreview,
  publicMode,
  assetID,
  previewVoucherID,
  universalAnalyticsCode,
  googleAnalyticsCode,
  listsEnabled,
  vouchersEnabled,
  redirect,
  redirectURL
}) => {
  const [uaInitialized, setUAInitialized] = useState(false)
  const [ga4Initialized, setGA4Initialized] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  useEffect(() => {
    if (redirect) {
      window.location.href = redirectURL ?? 'https://cinesend.com'
    }
  }, [redirect, redirectURL])

  useEffect(() => {
    if (universalAnalyticsCode && !uaInitialized) {
      ReactGA.initialize(universalAnalyticsCode)
      setUAInitialized(true)
    }
  }, [universalAnalyticsCode, uaInitialized])

  useEffect(() => {
    if (uaInitialized) {
      ReactGA.pageview(location.pathname)
    }
  }, [location, uaInitialized])

  useEffect(() => {
    if (googleAnalyticsCode && !ga4Initialized) {
      ReactGA4.initialize(googleAnalyticsCode)
      setGA4Initialized(true)
    }
  }, [googleAnalyticsCode, ga4Initialized])

  useEffect(() => {
    if (ga4Initialized) {
      ReactGA4.send("pageview")
    }
  }, [location, ga4Initialized])

  useEffect(() => {
    let redirect = localStorage.getItem('external_redirect_path')
    if (redirect && authenticated) {
      localStorage.removeItem('external_redirect_path')
      navigate(redirect)
    }
  }, [authenticated, navigate])

  if (redirect) {
    return <Status pending />
  }

  return (authenticated || publicMode) ? (
    <Routes>
      <Route path='/ip' element={<Headers />} />
      {authenticated && <Route path='/register' element={<Navigate to='/' />} />}
      {!authenticated && <Route path='/register' element={<Register />} />}
      {authenticated ? (
        <Route path='/authorization-callback' element={<Navigate to='/' />} />
      ) : (
        <Route path='/authorization-callback' element={<AuthorizationCallbackPage />} />
      )}
      {authenticated ? (
        <Route path='/invite/:token' element={<Navigate to='/' />} />
      ) : (
        <Route path='/invite/:token' element={<Invite />} />
      )}
      <Route path='/pair' element={<Navigate to='/settings/pair' />} />
      <Route path='/settings/*' element={<Settings />} />
      <Route path='/password/:token' element={<Reset />} />
      <Route path='/play' element={<VideoPlayer />} />
      <Route path='/view/:videoID' element={<VideoDetails />} />
      <Route path='/live/:videoID/:voucherID?' element={<LiveStream />} />
      <Route path='/live-interaction/:assetID' element={<LiveInteraction />} />
      <Route path='/moderators/:moderatorLinkID' element={<ModeratorLink />} />
      <Route path='/cta/:ctaID' element={<CustomHTML />} />
      <Route path='/zoom' element={<VideoChat />} />
      {listsEnabled && <Route path='/list' element={<MyList />} />}
      <Route path='/login' element={<Home />} />
      <Route path='/' element={<Dashboard />} />
      <Route path='/sections/:sectionTitle' element={<Dashboard />} />
      <Route path='/categories/:categoryID' element={<Category />} />
      {isPreview && assetID && (
        <Route path='/landing/:voucherID' element={<Navigate to={`/view/${assetID}`} />} />
      )}
      {isPreview && (
        <Route path='/landing/:voucherID' element={<Navigate to='/' />} />
      )}
      {previewVoucherID && (
        <Route path='/view/:videoID' element={<Navigate to={`/landing/${previewVoucherID}`} />} />
      )}
      {vouchersEnabled && <Route path='/landing/:voucherID' element={<VoucherDetails />} />}
      {vouchersEnabled && <Route path='/:voucherID/assets/:assetID' element={<VoucherPlayer />} />}
      {vouchersEnabled && <Route path='/:voucherID/seasons/:seasonID/episodes/:episodeID' element={<VoucherPlayer />} />}
      {vouchersEnabled && <Route path='/:voucherID/:activeIndex?' element={<VoucherPlayer />} />}
      <Route path='*' element={<Navigate to='/' />} />
    </Routes>
  ) : (
    <Routes>
      {previewVoucherID && (
        <Route path='/view/:videoID' element={<Navigate to={`/landing/${previewVoucherID}`} />} />
      )}
      <Route path='/ip' element={<Headers />} />
      <Route path='/authorization-callback' element={<AuthorizationCallbackPage />} />
      <Route path='/pair' element={<Home />} />
      <Route path='/login' element={<Home />} />
      <Route path='/register' element={<Register />} />
      <Route path='/cta/:ctaID' element={<CustomHTML />} />
      <Route path='/view/:videoID' element={<Home />} />
      <Route path='/live/:videoID/:voucherID?' element={<LiveStream />} />
      <Route path='/live-interaction/:assetID' element={<LiveInteraction />} />
      <Route path='/moderators/:moderatorLinkID' element={<ModeratorLink />} />
      <Route path='/play/:videoID' element={<Home />} />
      <Route path='/categories/:categoryID' element={<Home />} />
      <Route path='/password/:token' element={<Reset />} />
      <Route path='/invite/:token' element={<Invite />} />
      {vouchersEnabled && <Route path='/landing/:voucherID' element={<VoucherDetails />} />}
      {vouchersEnabled && <Route path='/:voucherID/assets/:assetID' element={<VoucherPlayer />} />}
      {vouchersEnabled && <Route path='/:voucherID/seasons/:seasonID/episodes/:episodeID' element={<VoucherPlayer />} />}
      {vouchersEnabled && <Route path='/:voucherID/:activeIndex?' element={<VoucherPlayer />} />}
      <Route path='*' element={<Home />} />
    </Routes>
  )
}

const mapStateToProps = state => ({
  authenticated: state.auth.status === 'AUTHENTICATED',
  listsEnabled: state.organization.lists_enabled,
  vouchersEnabled: state.organization.vouchers_enabled,
  publicMode: state.organization.enable_public_browsing ?? false,
  universalAnalyticsCode: state.organization.ondemand_ga_code ?? null,
  googleAnalyticsCode: state.organization.ondemand_ga4_code ?? null,
  isPreview: state.auth.admin,
  assetID: state.session.landingDetails.video._id,
  previewVoucherID: state.auth.admin?.active_voucher_id,
  redirect: state.organization.redirect,
  redirectURL: state.organization.redirect_url
})

export default connect(mapStateToProps)(AllRoutes)
