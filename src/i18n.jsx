import i18n from "i18next"
import { initReactI18next } from "react-i18next"
import resources from './translations'
import dayjs from 'dayjs'

i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: "en_US",
    keySeparator: ".",
    interpolation: {
      alwaysFormat: true,
      format: function (value, format, lng) {
        if (value instanceof Date) {
          if (lng === 'en_US') {
            lng = 'en'
          }
          dayjs.locale(lng.replace('_', '-').toLowerCase())
          return dayjs.utc(value).format(format)
        }
        return value
      },
      escapeValue: false // react already safes from xss
    }
  })
