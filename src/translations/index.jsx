import languages from "./languages";

const defaults = {
  languages,
  datetime: {
    format: "LL [at] LT",
  },
  dashboard: {
    noContentAvailable: "There is no content available to you at this time.",
  },
  home: {
    ticketHolders: "Ticket Holders",
    code: "Enter a voucher code",
    submit: "Submit",
    memberLogin: "Member Login",
    pinLogin: "Enter PIN Code",
    pin: "Enter your PIN",
    pinSubmit: "Submit",
    memberRegister: "Create an account",
    registerButton: "Create",
    registerErrors: "There were errors with your submission.",
    email: "Enter your email address",
    name: "Enter your full name",
    password: "Enter your password",
    confirmPassword: "Confirm your password",
    login: "Log in",
    forgotPassword: "Forgot?",
    or: "or",
    newPassword: "New Password",
    enterYourNewPassword: "Enter your new password",
    reset: "Reset",
    pleaseCheckYourInbox:
      "Please check your inbox for a link to reset your password.",
    resetPassword: "Reset password",
    emailAddress: "Email address",
    back: "Back",
  },
  userDropdown: {
    logIn: "Log In",
    logOut: "Log Out",
    language: "Language",
    watchOnTV: "Watch on my TV",
    manageAccessDevices: "Manage Devices",
    purchases: "Orders",
    account: "Account",
    register: "Create an Account",
    manageCookies: "Manage Cookies"
  },
  myList: {
    label: "Watch Later",
    empty: "You have no videos in your watch later list.",
  },
  purchases: {
    expiresAt: "Expires",
    expiredAt: "Expired",
    renewsAt: "Renews",
    purchasedAt: "Purchased",
    refundedAt: "Refunded",
    redeemedAt: "Redeemed",
    playableAt: "Playable",
    unplayed: "Playback has not been started.",
    success: "Your purchase was successful.",
    status: "Status"
  },
  accessDevices: {
    loggedInDevices: "Manage Devices",
    deviceLimitMessage:
      "You can associate up to {{deviceLimit}} devices with your account.",
    confirmDelete: "Are you sure you want to delete this access device?",
    location: "Location",
    deviceName: "Device Name",
    details: "Connection Details",
    lastAccess: "Last Accessed",
    overLimit: "You are currently over your device limit.",
    newDevicesAdded: "When you sign into a new device it will appear here.",
    confirmClearTV: "Are you sure you want to clear all TV sessions?",
  },
  search: {
    label: "Search",
    apply: "Apply",
    clear: "Clear",
    duration: "Duration",
    countries: "Countries",
    results: "Titles matching your search and filters",
    empty: "No results",
    searchingFor: "Searching for"
  },
  footer: {
    poweredBy: "Powered By",
    allRightsReserved: "All Rights Reserved",
  },
  asset: {
    director_one: "Director",
    director_other: "Directors",
    producer_one: "Producer",
    producer_other: "Producers",
    language: "Language",
    languages: "Languages",
    subtitle: "Subtitle",
    subtitles: "Subtitles",
    clickForMore: "Click for more",
    watchNowFor: "Watch now for {{assetPrice}}",
    preorderNowFor: 'Pre-order Now for {{assetPrice}}',
    fullName: "Full Name on Card",
    cardNumber: "Credit Card Number",
    cardExpiration: "Expiration",
    cardVerification: "Verification CVV/CVC",
    postalCode: "Postal code / Zip Code",
    cancelOrder: "Cancel",
    payWithPrice: "Pay {{price}} {{currency}}",
    yourPurchaseExpires: "Your purchase expires on {{expires_at, LLLL}}",
    assetRegister: "Register",
    assetRegisterPending: "Registering...",
    assetRegistered: 'Registered',
    checkout: 'Secure Checkout',
    purchased: "Purchased"
  },
  assetTypes: {
    videos: "Videos",
    events: "Events",
    playlists: "Playlists",
    series: "Series",
    albums: "Albums",
  },
  playlist: {
    thisPlaylistIncludes: "This playlist includes",
    duration: "Playlist Duration",
    thisPlaylistContains: "This playlist contains {{count}} video(s)",
  },
  series: {
    season: "Season {{season_number}}",
  },
  liveStream: {
    yourName: "Your name...",
    yourCompany: "Company name...",
    join: "Join!",
    chatWithAttendees: "Chat with attendees",
    typeQuestion: "Type a question...",
    ask: "Ask!",
    typeMessage: "Type your message...",
    startingSoon: "Starting Soon",
    sourceDisconnected: 'Source Disconnected',
    checkBackLater: "Check Back Later",
    subtext:
      "The broadcast hasn't started yet - it will display here automatically when it does.",
    isDisconnected: "The live stream has gone offline and we’ll try to reconnect as soon as it returns.",
    askAQuestion: "Ask a question",
    noQuestionsAsked: "You haven't asked any questions yet. Ask one now!",
    offline: "Offline",
    joinChatAndQuestions:
      "Enter your details to join the chat and ask questions",
    joinChat: "Enter your details to join the chat",
    joinQuestions: "Enter your details to ask questions",
  },
  buttons: {
    play: "Play",
    notAvailable: "Not Available",
    expired: "This content is expired.",
    live: "Live",
    youHave: "You have ",
    youHaveUntil: "You have until ",
    toWatchThisContent: " to watch this content.",
    hoursToWatchThisContentFromWhenYouFirstClickPlay:
      " hours to watch this content from when you first click play.",
    thisContentWillBeAvailableOn: "This content will be available on",
    thisIsATimedEventAndWillBeginOn: "This is a timed event and will begin on",
    addingToWatchLater: "Adding",
    removingFromWatchLater: "Removing",
    watchLater: "Watch Later",
    moreInfo: "More Info",
    watchTrailer: "Watch Trailer",
    readMore: "Read More",
    dismiss: "Dismiss",
    thisContentIsNotAvailable: 'This content is not yet available',
    thisContentIsGeoblocked: 'This content is geoblocked from your location',
    viewAllInCategory: 'View All',
    close: 'Close',
    clearFilter: 'Clear Filter',
  },
  common: {
    yes: "Yes",
    no: "No",
  },
  tv: {
    watchOnMyTV: "Watch on my TV",
    description:
      "You can watch content directly on select smart TVs or streaming boxes.",
    instructions: "Pairing Instructions",
    ifYouHave: "If you have an ",
    youCanDownload: "you can download the",
    appToWatch: "app to watch your films on your TV.",
    step: "Step ",
    stepOneA: "Download the ",
    stepOneB: "app on:",
    stepTwo:
      "Select the login option, and then enter the code displayed on your TV below. If a pairing code does not appear, you can enter your credentials using your remote control.",
    enterPairingCode: "Enter pairing code here",
    successfullyPaired:
      "You have successfully paired your account to your TV app!",
  },
  subscription: {
    subscribeNow: "Subscribe",
    paymentInfo: "Payment Info"
  },
  settings: {
    subscriptions: "Subscriptions",
    managedSubscriptions: "Managed Subscriptions",
    activeSubscriptions: "Active Subscriptions",
    availableSubscriptions: "Available Subscriptions",
    managedSubscriptionsDescription:
      "This is a summary of your externally managed subscriptions.",
    activeSubscriptionsDescription:
      "This is a summary of your purchased subscriptions",
    availableSubscriptionsDescription:
      "These subscriptions are available for purchase.",
    externalSubscription: "Purchased and billed externally.",
    account: "Account",
    accountDescription: "Manage account details and change your password",
    name: "Name",
    email: "E-mail / Username",
    password: "Password",
    accountLanguage: "Account Language",
    oldPassword: "Old Password",
    newPassword: "New Password",
    confirmPassword: "Confirm Password",
    passwordMismatch: "Your passwords do not match.",
    passwordError: "The password provided is incorrect.",
    passwordSuccess: "You have successfully updated your password!",
    purchases: "Your Orders",
    purchasesNoneMade: "You have not yet made any purchases.",
    subscriptionsNoneMade: "You have not made any subscriptions.",
    noUpcomingInvoices: "You have no upcoming invoices.",
    purchasesLoading: "Retrieving your history...",
    invoicesLoading: "Retrieving your invoices...",
  },
  subscriberActivation: {
    welcome: "Welcome",
    activationDescription:
      "Fill out the fields below to finish setting up your account.",
    name: "Name",
    email: "E-mail",
    createPassword: "Create a Password",
    createAccount: "Create Account",
    pleaseWait: "Please Wait...",
  },
  cookieConsent: {
    consentModal: {
      title: 'We use cookies',
      description: 'We use cookies to get analytic insights and improve site performance.',
      acceptAllBtn: 'Accept all',
      acceptNecessaryBtn: 'Reject all',
      showPreferencesBtn: 'Manage Individual preferences'
    },
    preferencesModal: {
      title: 'Manage cookie preferences',
      acceptAllBtn: 'Accept all',
      acceptNecessaryBtn: 'Reject all',
      savePreferencesBtn: 'Accept current selection',
      closeIconLabel: 'Close modal',
      sections: {
        'necessary': {
          title: 'Strictly Necessary cookies',
          description: 'These cookies are essential for the proper functioning of the website and cannot be disabled.',
        },
        'analytics': {
          title: 'Analytics',
          description: 'These cookies collect information about how you use our website. All of the data is anonymized and cannot be used to identify you.',
        },
        'performance': {
          title: 'Performance',
          description: 'These cookies cache data to improve site performance.',
        }
      }
    }
  }
};

const resources = {
  en_US: {
    translation: defaults,
  },
  fr_CA: {
    translation: {
      ...defaults,
      languages: {
        ...defaults.languages,
        en_US: "English",
        fr_CA: "Français",
        lt: "Lietuvių k.",
      },
      datetime: {
        format: "LL [à] HH [h] mm",
      },
      home: {
        ticketHolders: "Détenteurs de billets",
        code: "Code",
        submit: "Soumettre",
        memberLogin: "Connexion de membres",
        memberRegister: "Créer un compte",
        email: "Adresse électronique",
        name: "Nom",
        password: "Mot de passe",
        confirmPassword: "Confirmez le mot de passe",
        login: "Se connecter",
        forgotPassword: "Mot de passe oublié ?",
        or: "ou",
        newPassword: "Nouveau mot de passe",
        enterYourNewPassword: "Entrer votre nouveau mot de passe",
        reset: "Réinitialiser",
        pleaseCheckYourInbox:
          "Veuillez accéder à votre boîte de réception, vous trouverez un lien pour réinitialiser votre mot de passe",
        resetPassword: "Réinitialiser votre mot de passe",
        emailAddress: "Adresse e-mail",
        back: "Retour",
      },
      userDropdown: {
        ...defaults.userDropdown,
        logOut: "Se déconnecter",
        language: "Langue",
        watchOnTV: "Regarder sur ma télé",
        manageAccessDevices: "Gérer les appareils",
        account: "Compte",
        manageCookies: "Gérer les cookies"
      },
      myList: {
        label: "Votre Liste",
        empty: "Vous n'avez aucune vidéo dans votre liste.",
      },
      accessDevices: {
        ...defaults.accessDevices,
        loggedInDevices: "Gérer les appareils",
        deviceLimitMessage:
          "Vous pouvez associer jusqu'à {{deviceLimit}} appareils à votre compte.",
      },
      search: {
        label: "Chercher",
        apply: "Appliquer",
        clear: "Effacer",
        duration: "Durée",
        countries: "Pays",
        results: "Titres correspondant à votre recherche et filtres",
        empty: "Aucun résultat",
        searchingFor: "Recherche",
      },
      footer: {
        poweredBy: "Propulsé Par",
        allRightsReserved: "Tous Droits Réservés",
      },
      asset: {
        director_one: "Cinéaste",
        director_other: "Cinéastes",
        producer_one: "Producteur",
        producer_other: "Producteurs",
        language: "Langue",
        languages: "Langues",
        subtitle: "Sous-titre",
        subtitles: "Sous-titres",
        yourPurchaseExpires: "Votre achat expire le {{expires_at, LLLL}}",
      },
      assetTypes: {
        videos: "Vidéos",
        events: "Événements",
        playlists: "Listes de lecture",
        series: "Série",
        albums: "Albums",
      },
      playlist: {
        thisPlaylistIncludes: "Cette liste de lecture inclut",
        duration: "Durée de la liste de lecture",
        thisPlaylistContains:
          "Cette liste de lecture contient {{count}} vidéo(s)",
      },
      series: {
        season: "Saison {{season_number}}",
      },
      liveStream: {
        ...defaults.liveStream,
        yourName: "Votre nom...",
        yourCompany: "Le nom de votre entreprise...",
        chatWithAttendees: "Chat en directe",
        join: "Rejoigner!",
        typeQuestion: "Je pose ma question...",
        ask: "Soumettre!",
        typeMessage: "Entrez votre message...",
        askAQuestion: "Posez vos questions",
        noQuestionsAsked:
          "Vous n'avez pas encore posé de questions. Posez une question maintenant !",
        checkBackLater: "Revenez plus tard",
        subtext:
          "La diffusion n'a pas encore commencé - elle s'affichera ici automatiquement lorsqu'elle le fera.",
        offline: "Hors ligne",
        startingSoon: "Commence bientôt",
        joinChatAndQuestions:
          "Entrez vos informations pour rejoindre le chat et poser des questions",
        joinChat: "Entrez vos informations pour rejoindre le chat",
        joinQuestions: "Entrez vos informations pour poser des questions",
      },
      buttons: {
        ...defaults.buttons,
        play: "Jouer",
        notAvailable: "Pas disponible",
        expired: "Ce contenu est expiré.",
        live: "Live",
        youHave: "You have ",
        youHaveUntil: "You have until ",
        toWatchThisContent: " to watch this content.",
        hoursToWatchThisContentFromWhenYouFirstClickPlay:
          " hours to watch this content from when you first click play.",
        thisContentWillBeAvailableOn: "Ce contenu sera disponible à partir du",
        thisIsATimedEventAndWillBeginOn:
          "Ce contenu est un événement chronométré et commencera le",
        addingToWatchLater: "Adding",
        removingFromWatchLater: "Removing",
        watchLater: "Regarder plus tard",
        moreInfo: "Plus d'informations",
        readMore: "Plus d'informations",
        watchTrailer: "Regarde la bande-annonce",
        dismiss: "Ignorer",
        viewAllInCategory: "Voir tout",
        clearFilter: 'Effacer le filtre'
      },
      common: {
        yes: "Oui",
        no: "Non",
      },
      tv: {
        ...defaults.tv,
        watchOnMyTV: "Regarder sur ma télé",
        description:
          "Vous pouvez regarder du contenu directement sur une sélection de téléviseurs intelligents ou d'appareils de streaming",
        instructions: "Instructions de couplage",
        ifYouHave: "Si vous possédez une ",
        youCanDownload: "vous pouvez télécharger l'application",
        appToWatch: "pour regarder vos films sur votre téléviseur.",
        step: "Étape ",
        stepOneA: "Télécharger l'application ",
        stepOneB: "sur:",
        stepTwo:
          "Sélectionner l'option de connexion, puis entrer le code affiché sur votre téléviseur ci-dessous.",
        enterPairingCode: "Entrer le code de couplage ici",
        successfullyPaired:
          "Vous avez correctement associé votre compte à votre application TV!",
      },
      settings: {
        ...defaults.settings,
        subscriptions: "Abonnements",
        subscriptionsDescription:
          "Affichez un résumé de vos abonnements actifs et mettez-les à jour s'ils sont récurrents.",
        externalSubscription: "Acheté et facturé à l'extérieur.",
        account: "Compte",
        accountDescription:
          "Gérer les détails du compte et changer votre mot de passe",
        name: "Nom",
        email: "Courriel / Nom d'utilisateur",
        password: "Mot de passe",
        oldPassword: "Ancien mot de passe",
        newPassword: "Nouveau mot de passe",
        confirmPassword: "Confirmer le mot de passe",
        passwordMismatch: "Les mots de passe ne correspondent pas.",
        passwordError: "Le mot de passe fourni est erroné.",
        accountLanguage: "Langue du compte",

      },
      subscriberActivation: {
        ...defaults.subscriberActivation,
        welcome: "Bienvenue",
        activationDescription:
          "Remplissez les champs ci-dessous pour compléter la configuration de votre compte.",
        name: "Nom",
        email: "Courriel",
        createPassword: "Créer un mot de passe",
        createAccount: "Créer un compte",
        pleaseWait: "Veuillez patienter...",
      },
      cookieConsent: {
        consentModal: {
          title: "Nous utilisons des cookies",
          description: "Nous utilisons des cookies pour personnaliser le contenu et les publicités, offrir des fonctionnalités de médias sociaux et analyser notre trafic. Nous partageons également des informations sur votre utilisation de notre site avec nos partenaires de médias sociaux, de publicité et d'analyse.",
          acceptAllBtn: "Accepter tout",
          acceptNecessaryBtn: "Rejeter tout",
          showPreferencesBtn: "Gérer les préférences individuelles"
        },
        preferencesModal: {
          title: "Gérer les préférences de cookies",
          acceptAllBtn: "Accepter tout",
          acceptNecessaryBtn: "Rejeter tout",
          savePreferencesBtn: "Sauvegarder la sélection actuelle",
          closeIconLabel: "Fermer la fenêtre",
          sections: {
            necessary: {
              title: "Cookies strictement nécessaires",
              description: "Ces cookies sont essentiels pour le bon fonctionnement du site et ne peuvent pas être désactivés."
            },
            analytics: {
              title: "Analytique",
              description: "Ces cookies collectent des informations sur la manière dont vous utilisez notre site Web. Toutes les données sont anonymisées et ne peuvent pas être utilisées pour vous identifier."
            },
            performance: {
              title: "Performance",
              description: "Ces cookies stockent des données pour améliorer les performances du site."
            }
          }
        }
      }
    },
  },
  lt: {
    translation: {
      ...defaults,
      languages: {
        en_US: "English",
        fr_CA: "Français",
        lt: "Lietuvių k.",
      },
      home: {
        ticketHolders: "Kino Bilietas",
        code: "Kodas",
        submit: "Žiūrėti",
        memberLogin: "Festivalio Pasas",
        email: "El. paštas",
        password: "Slaptažodis",
        login: "Prisijungti",
        forgotPassword: "Pamiršote?",
        or: "arba",
      },
      userDropdown: {
        ...defaults.userDropdown,
        logOut: "Atsijungti",
        language: "Kalba",
        watchOnTV: "Žiūrėti per mano TV",
      },
      myList: {
        label: "Žiūrėti vėliau",
        empty: "Žiūrėti vėliau sąraše filmų nėra",
      },
      search: {
        label: "Ieškoti",
        apply: "Pasirinkti",
        clear: "Išvalyti",
        duration: "Trukmė",
        countries: "Šalis",
        results: "Titles matching your search and filters",
        empty: "Nerasta",
        searchingFor: "Searching for",
      },
      footer: {
        poweredBy: "Powered By",
        allRightsReserved: "Visos teisės saugomos",
      },
      asset: {
        director_one: "Režisierius(ė)",
        director_other: "Režisierius(ė)",
        producer_one: "Prodiuseris(ė)",
        producer_other: "Prodiuseris(ė)",
        language: "Kalba",
        languages: "Kalbos",
        subtitle: "Subtitrai",
        subtitles: "Subtitrai",
        yourPurchaseExpires:
          "Pirkinio galiojimo laikas baigiasi {{expires_at, LLLL}}",
      },
      assetTypes: {
        videos: "Vaizdo įrašus",
        events: "Įvykiai",
        playlists: "Grojaraščiai",
        series: "Serija",
        albums: "Albumai",
      },
      series: {
        season: "Sezonas {{season_number}}",
      },
      liveStream: {
        ...defaults.liveStream,
        yourName: "Jūsų vardas...",
        join: "Prisijungti!",
        typeQuestion: "Užduokite klausimą...",
        ask: "Klausti!",
        typeMessage: "Rašykite žinutę...",
        yourCompany: "Kompanijos pavadinimas...",
        chatWithAttendees: "Bendraukite su kitais seanso dalyviais",
        startingSoon: "Pradžia jau greitai",
        checkBackLater: "Patikrinkite vėliau",
        subtext:
          "Transliacija dar neprasidėjo. Transliacijai prasidėjus, ji automatiškai pradės groti šiame lange.",
        askAQuestion: "Užduokite klausimą",
        noQuestionsAsked: "Dar neuždavėte jokių klausimų.",
        offline: "Neprisijungęs",
        joinChatAndQuestions:
          "Įrašyk savo duomenis ir prisijunk prie pokalbių lango",
        joinChat: "Įrašyk savo duomenis ir prisijunk prie pokalbių lango",
        joinQuestions: "Įrašyk savo duomenis ir prisijunk prie pokalbių lango",
      },
      buttons: {
        ...defaults.buttons,
        play: "Žiūrėti filmą",
        notAvailable: "Not Available",
        expired: "This content is expired.",
        live: "Žiūrėti",
        youHave: "Pradėję žiūrėti filmą, turėsite ",
        youHaveUntil: "You have until ",
        toWatchThisContent: " to watch this content.",
        hoursToWatchThisContentFromWhenYouFirstClickPlay:
          " valandas peržiūrai pabaigti.",
        addingToWatchLater: "Adding",
        removingFromWatchLater: "Removing",
        watchLater: "Žiūrėti vėliau",
        moreInfo: "Plačiau",
        watchTrailer: "Žiūrėti anonsą",
        viewAllInCategory: "Žiūrėti daugiau"
      },
      common: {
        yes: "Taip",
        no: "Ne",
      },
      subscriberActivation: {
        ...defaults.subscriberActivation,
        welcome: "Sveiki atvykę!",
        activationDescription:
          "Užpildykite laukelius apačioje ir būsite nukreipti į savo paskyrą.",
        name: "Vardas",
        email: "El.paštas",
        createPassword: "Sukurkite slaptažodį",
        createAccount: "Prisijunkite",
        pleaseWait: "Prašome palaukti...",
      },
      cookieConsent: {
        consentModal: {
          title: "Mes naudojame slapukus",
          description: "Mes naudojame slapukus, kad gautume analitinių įžvalgų ir pagerintume svetainės veikimą.",
          acceptAllBtn: "Priimti viską",
          acceptNecessaryBtn: "Atmesti būtinus",
          showPreferencesBtn: "Tvarkyti nuostatas"
        },
        preferencesModal: {
          title: "Tvarkyti slapukų nuostatas",
          acceptAllBtn: "Priimti viską",
          acceptNecessaryBtn: "Atmesti būtinus",
          savePreferencesBtn: "Išsaugoti dabartinį pasirinkimą",
          closeIconLabel: "Uždaryti modalą",
          sections: {
            necessary: {
              title: "Būtini slapukai",
              description: "Šie slapukai yra būtini svetainės veikimui ir negali būti išjungti."
            },
            analytics: {
              title: "Analitikos slapukai",
              description: "Šie slapukai renka informaciją apie tai, kaip naudojatės mūsų svetaine. Visa informacija yra anonimiška ir negali būti naudojama jus identifikuoti."
            },
            performance: {
              title: "Našumo slapukai",
              description: "Šie slapukai kaupia duomenis, kad pagerintų svetainės našumą."
            }
          }
        }
      }
    },
  },
  ja: {
    translation: {
      ...defaults,
      search: {
        ...defaults.search,
        label: "検索",
        searchingFor: "検索",
      },
      assetTypes: {
        ...defaults.assetTypes,
        videos: "映画",
        series: "シリーズ",
        albums: "オーディオ",
      },
      buttons: {
        ...defaults.buttons,
        watchTrailer: "トレーラーを見る",
        moreInfo: "続きを読む",
        readMore: "続きを読む",
        viewAllInCategory: "すべて見る"
      },
      cookieConsent: {
        consentModal: {
          title: "クッキーの使用について",
          description: "私たちは分析的な洞察を得てサイトのパフォーマンスを向上させるためにクッキーを使用しています。",
          acceptAllBtn: "すべて承認する",
          acceptNecessaryBtn: "必要最低限のもののみ承認する",
          showPreferencesBtn: "個別設定を管理する"
        },
        preferencesModal: {
          title: "クッキーの設定を管理する",
          acceptAllBtn: "すべて承認する",
          acceptNecessaryBtn: "必要最低限のもののみ承認する",
          savePreferencesBtn: "現在の選択を保存する",
          closeIconLabel: "モーダルを閉じる",
          sections: {
            necessary: {
              title: "必須クッキー",
              description: "これらのクッキーはウェブサイトの正常な動作に必要であり、無効にすることはできません。"
            },
            analytics: {
              title: "分析クッキー",
              description: "これらのクッキーは、お客様が当サイトをどのように利用しているかの情報を収集し、匿名化されたデータのみが使用されます。"
            },
            performance: {
              title: "パフォーマンスクッキー",
              description: "これらのクッキーは、サイトのパフォーマンスを向上させるためのデータを保存します。"
            }
          }
        }
      }
    },
  }
};

export default resources
