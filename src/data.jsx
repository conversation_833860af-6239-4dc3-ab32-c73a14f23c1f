const short_summary = "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. "
const logline = "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes. "

const phaseOne = [
  {
    title: "Iron Man",
    thumbnail_url: "https://hips.hearstapps.com/digitalspyuk.cdnds.net/17/19/1494599203-greatest-superhero-films-iron-man.jpg",
    featured: true
  },
  {
    title: "The Incredible Hulk",
    thumbnail_url: "https://api.time.com/wp-content/uploads/2017/07/brb1740_trbcomp_v411-1171.jpg",
    featured: true
  },
  {
    title: "Iron Man 2",
    thumbnail_url: "https://d13ezvd6yrslxm.cloudfront.net/wp/wp-content/images/Road-to-Endgame-Iron-Man-2-1-700x312.jpg",
    featured: true
  },
  {
    title: "Thor",
    thumbnail_url: "https://vignette.wikia.nocookie.net/marvelcinematicuniverse/images/4/47/Thor-movie-Loki.jpg/revision/latest?cb=20150211155941"
  },
  {
    title: "Captain America: The First Avenger",
    thumbnail_url: "https://media1.popsugar-assets.com/files/thumbor/QWPPl19b9wjOzYngtIZ5cI8vEoQ/fit-in/2048xorig/filters:format_auto-!!-:strip_icc-!!-/2015/04/28/029/n/1922283/7f185c4f_edit_img_cover_file_845210_1429641781_Captain-America-The-First-Avenger__3_/i/Chris-Evans-Captain-America-GIFs.jpg",
    featured: true
  },
  {
    title: "Captain America: The First Avenger - Trailer",
    thumbnail_url: "https://i.ytimg.com/vi/F020aNi0wS0/maxresdefault.jpg",
    featured: true
  },
  {
    title: "The Avengers",
    thumbnail_url: "https://superherojunky.com/wp-content/uploads/2018/12/51282cb3-cd4b-4492-a8b6-4ab16476b366.jpg"
  }
]

const phaseTwo = [
  {
    title: "Iron Man 3",
    thumbnail_url: "https://imagesvc.meredithcorp.io/v3/mm/image?url=https%3A%2F%2Fstatic.onecms.io%2Fwp-content%2Fuploads%2Fsites%2F6%2F2018%2F02%2Firon-man-3-review-2000.jpg",
  },
  {
    title: "Thor: The Dark World",
    thumbnail_url: "https://media2.fdncms.com/rochester/imager/chris-hemsworth-in-thor-the-dark-world/u/zoom/2296287/movie_review2-1.jpg",
  },
  {
    title: "Captain America: The Winter Soldier",
    thumbnail_url: "https://hdwallpaperim.com/wp-content/uploads/2017/08/26/183442-Captain_America_The_Winter_Soldier-Scarlett_Johansson-Chris_Evans-Steve_Rogers.jpg",
    featured: true
  },
  {
    title: "Guardians of the Galaxy",
    thumbnail_url: "https://mtv.mtvnimages.com/uri/mgid:ao:image:mtv.com:57849?quality=0.8&format=jpg&width=1440&height=810&.jpg"
  },
  {
    title: "Avengers: Age of Ultron",
    thumbnail_url: "https://i.insider.com/553db66e69bedd953567ff69?width=1100&format=jpeg&auto=webp",
    featured: true
  },
  {
    title: "Ant-Man",
    thumbnail_url: "https://m.media-amazon.com/images/M/MV5BMjI4OTM0NjY2OF5BMl5BanBnXkFtZTgwNzc0NDcyNjE@._V1_.jpg",
    featured: true
  }
]

const phaseThree = [
  {
    title: "Captain America: Civil War",
    thumbnail_url: "https://dl9fvu4r30qs1.cloudfront.net/9f/b9/c7707f5c4039b8db5466a3b1bfba/captain-america-civil-war-ca3-12.jpg",
    featured: true
  },
  {
    title: "Doctor Strange",
    thumbnail_url: "https://media.comicbook.com/2016/04/doctor-strange-screens-trailer-1-178730-1280x0.jpg",
  },
  {
    title: "Guardians of the Galaxy Vol. 2",
    thumbnail_url: "https://media.vanityfair.com/photos/58ff9d115831951cdbf4cec8/master/pass/guardians-of-the-galaxy.jpg",
    featured: true
  },
  {
    title: "Spider-Man: Homecoming",
    thumbnail_url: "https://cms.qz.com/wp-content/uploads/2017/07/spider-man-homecoming-df-26558_r-e1499112483591.jpg",
    featured: true
  },
  {
    title: "Thor: Ragnarok",
    thumbnail_url: "https://i2.wp.com/www.solzyatthemovies.com/wp-content/uploads/2017/10/ThorRagnarok59f23d7a73af4.jpg",
    featured: true
  },
  {
    title: "Black Panther",
    thumbnail_url: "https://i.ytimg.com/vi/y6XdSejym3U/maxresdefault.jpg",
    featured: true
  },
  {
    title: "Avengers: Infinity War",
    thumbnail_url: "https://i.insider.com/5ab181fc42e1cc57a06eca9e?width=1100&format=jpeg&auto=webp"
  },
  {
    title: "Ant-Man and the Wasp",
    thumbnail_url: "https://www.etonline.com/sites/default/files/styles/video_1920x1080/public/images/2015-10/1280_ant_man_paul_rudd_evangeline_lilly_100815.jpg?itok=SpZi7jGn"
  },
  {
    title: "Captain Marvel",
    thumbnail_url: "https://www.gannett-cdn.com/presto/2019/03/04/USAT/0fc0d19f-c434-4e15-bf6a-3b6de7b8993c-FRC4300_TRLcmp_v016.1061_R.jpg"
  },
  {
    title: "Avengers: Endgame",
    thumbnail_url: "https://m.media-amazon.com/images/M/MV5BZWExODQ5ZjgtNWNjOC00ZDQ3LWFlM2EtZmI1YzdlODBjNDkzXkEyXkFqcGdeQXVyNjg2NjQwMDQ@._V1_.jpg",
    featured: true
  },
  {
    title: "Spider-Man: Far From Home",
    thumbnail_url: "https://static01.nyt.com/images/2019/07/08/arts/08spider-spoilers-1/merlin_157155882_1b06740b-940a-4a63-99e1-31122006d72a-superJumbo.jpg",
    featured: true
  },
]

const data =  {
  organization: {
    "_id": "59527a140532a312008b4568",
    "name":"BitCine Technologies",
    "logo_url": "https://i.ya-webdesign.com/images/marvel-logo-png-4.png",
    "settings": {
        "vouchers_enabled":true,
        "subscribers_enabled":true,
        "api_enabled":true,
        "featured_category_id":"5e85f9e6fdce274e61089542"
    }
  },
  featuredCategory: {
      "_id": "5e8648bbc29af5686d2da682",
      "title": "Marvel Cinematic Universe",
      "organization_id":"59527a140532a312008b4568",
      "updated_at":"2020-04-03 02:00:57",
      "created_at":"2020-04-02 20:19:07",
      "videos": [
        ...phaseOne,
        ...phaseTwo,
        ...phaseThree
      ].filter(film => film.featured).map(film => (
          {
            ...film,
            logline,
            short_summary,
            "subheader": "3 mins | Canada, United States | 2016",
            "directors": "Johnny Day",
            "producers": "Charlie Day, Jackie Day, Mariska Day",
          }
        )
      )
  },
  categories: [
    {
      "_id": "5e8648bbc29af5686d2da682",
      "title": "Phase 1",
      "organization_id":"59527a140532a312008b4568",
      "updated_at":"2020-04-03 02:00:57",
      "created_at":"2020-04-02 20:19:07",
      "videos": phaseOne.map(film => (
        {
          ...film,
          logline,
          short_summary,
          "subheader": "3 mins | Canada, United States | 2016",
          "directors": "Johnny Day",
          "producers": "Charlie Day, Jackie Day, Mariska Day",
        }
      ))
    },
    {
      "_id": "5e8648bbc29af5686d2da682",
      "title": "Phase 2",
      "organization_id":"59527a140532a312008b4568",
      "updated_at":"2020-04-03 02:00:57",
      "created_at":"2020-04-02 20:19:07",
      "videos": phaseTwo.map(film => (
        {
          ...film,
          logline,
          short_summary,
          "subheader": "3 mins | Canada, United States | 2016",
          "directors": "Johnny Day",
          "producers": "Charlie Day, Jackie Day, Mariska Day",
        }
      ))
    },
    {
      "_id": "5e8648bbc29af5686d2da682",
      "title": "Phase 3",
      "organization_id":"59527a140532a312008b4568",
      "updated_at":"2020-04-03 02:00:57",
      "created_at":"2020-04-02 20:19:07",
      "videos": phaseThree.map(film => (
        {
          ...film,
          logline,
          short_summary,
          "subheader": "3 mins | Canada, United States | 2016",
          "directors": "Johnny Day",
          "producers": "Charlie Day, Jackie Day, Mariska Day",
        }
      ))
    }
  ]
}


export default data