import { getWindowWidth } from "components/VideoCarousel/utils"
import { useMediaQuery } from "react-responsive"

export default function usePageWidth(constrainWidth) {
  const windowWidth = getWindowWidth()
  const query = constrainWidth ? `${constrainWidth}px` : '100%'
  const isConstrained = useMediaQuery({ query: `(width: ${query})` })

  if (!isConstrained && constrainWidth && windowWidth > constrainWidth) {
    return constrainWidth + 'px'
  }
  return '100%'
}