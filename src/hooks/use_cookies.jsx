import { useEffect } from 'react'
import 'vanilla-cookieconsent/dist/cookieconsent.css'
import * as CookieConsent from 'vanilla-cookieconsent'
import { useTranslation } from 'react-i18next'

export default function useCookies(organization, language) {
  const originalSetItem = localStorage.setItem.bind(localStorage)
  const { t } = useTranslation()

  useEffect(() => {
    if (organization.status !== 'READY' || !language) return
  
    if (!organization.show_cookie_consent) {
      initAnalytics()
      localStorage.setItem = originalSetItem
      return
    }
  
    const consentTranslations = {
      consentModal: {
        title: t('cookieConsent.consentModal.title'),
        description: t('cookieConsent.consentModal.description'),
        acceptAllBtn: t('cookieConsent.consentModal.acceptAllBtn'),
        acceptNecessaryBtn: t('cookieConsent.consentModal.acceptNecessaryBtn'),
        showPreferencesBtn: t('cookieConsent.consentModal.showPreferencesBtn')
      },
      preferencesModal: {
        title: t('cookieConsent.preferencesModal.title'),
        acceptAllBtn: t('cookieConsent.preferencesModal.acceptAllBtn'),
        acceptNecessaryBtn: t('cookieConsent.preferencesModal.acceptNecessaryBtn'),
        savePreferencesBtn: t('cookieConsent.preferencesModal.savePreferencesBtn'),
        closeIconLabel: t('cookieConsent.preferencesModal.closeIconLabel'),
        sections: [
          {
            title: t('cookieConsent.preferencesModal.sections.necessary.title'),
            description: t('cookieConsent.preferencesModal.sections.necessary.description'),
            linkedCategory: 'necessary'
          },
          {
            title: t('cookieConsent.preferencesModal.sections.analytics.title'),
            description: t('cookieConsent.preferencesModal.sections.analytics.description'),
            linkedCategory: 'analytics'
          },
          {
            title: t('cookieConsent.preferencesModal.sections.performance.title'),
            description: t('cookieConsent.preferencesModal.sections.performance.description'),
            linkedCategory: 'performance'
          }
        ]
      }
    }
  
    CookieConsent.run({
      categories: {
        necessary: {
          enabled: true,
          readOnly: true
        },
        analytics: {
          enabled: false
        },
        performance: {
          enabled: false
        }
      },
      language: {
        default: language,
        translations: {
          en_US: consentTranslations,
          fr_CA: consentTranslations,
          ja: consentTranslations,
          lt: consentTranslations
        }
      },
      onChange: handleConsentPermissions,
      onConsent: handleConsentPermissions
    })
  }, [organization, t, language])
  

  const handleConsentPermissions = () => {
    if (CookieConsent.acceptedCategory('analytics')) {
      initAnalytics()
    }
    if (CookieConsent.acceptedCategory('performance')) {
      localStorage.setItem = originalSetItem
    } else {

      // copy and persist token through clears
      const token = localStorage.getItem('token')
      localStorage.clear()
      originalSetItem('token', token)
      localStorage.setItem = function (key, value) {
        if (key === 'token') {
          originalSetItem(key, value)
        } else {
          console.log(`Attempt to save ${key} was blocked due to cookie preferences.`)
        }
        return
      }
    }
  }
  const initAnalytics = () => {
    const script = document.createElement('script')
    script.async = true
    script.src = "https://www.googletagmanager.com/gtag/js?id=G-M1MBV46BLR"
    document.head.appendChild(script)

    window.dataLayer = window.dataLayer || []
    function gtag() {
      dataLayer.push(arguments)
    }
    gtag('js', new Date())
    gtag('config', 'G-M1MBV46BLR')
  }

}
