import { useMediaQuery } from 'react-responsive'

const MO<PERSON>LE_WIDTH = 768
const TABLET_WIDTH = 1024

export default function useIsMobile() {
  const isMobile = useMediaQuery({ query: `(max-width: ${MOBILE_WIDTH}px)` })
  const isTablet = useMediaQuery({ query: `(max-width: ${TABLET_WIDTH}px)` })

  return {
    mobileWidth: MOBILE_WIDTH,
    tabletWidth: TABLET_WIDTH,
    isMobile,
    isTablet
  }
}