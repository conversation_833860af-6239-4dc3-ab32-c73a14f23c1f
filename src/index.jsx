import React from 'react'
import { render } from 'react-dom'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import './index.css'
import App from './App'
import * as serviceWorker from './serviceWorker'
import configureStore from './redux/configureStore'
import './i18n'
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"
import localeData from 'dayjs/plugin/localeData'
import dayjsPluginUTC from "dayjs-plugin-utc"
import relativeTime from "dayjs/plugin/relativeTime"
import 'dayjs/locale/en-ca'
import 'dayjs/locale/en'
import 'dayjs/locale/fr-ca'
import 'dayjs/locale/lt'

const store = configureStore()

dayjs.extend(relativeTime)
dayjs.extend(localeData)
dayjs.extend(localizedFormat)
dayjs.extend(dayjsPluginUTC)

render(
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>,
  document.getElementById('root')
)

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister()
