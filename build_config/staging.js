const config = {
  ENV: 'staging',
  BASE_API: 'https://staging-api.cinesend.com/api/',
  ONDEMAND_API: 'https://staging-ondemand-api.cinesend.com/',
  CAST_URL: "https://bitmovin-cast-css.cinesend.com/development/styles.min.css",
  BM_ANALYTICS_ID: "7c4b2d6c-0c84-4fca-9c01-2b5e5b18dff6",
  GETSTREAM_KEY: "bajjxbkmz4sa",
  MUX_ANALYTICS_ID: "17ndhm4dfhurl5kijgqplksv7",
  STRIPE_PUBLIC_KEY: "pk_test_gXaQ6qFcGgqjwEHA1wjYKxcn"
}

export default config