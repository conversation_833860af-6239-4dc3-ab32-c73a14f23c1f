const config = {
  ENV: 'production',
  BASE_API: 'https://api.cinesend.com/api/',
  ONDEMAND_API: 'https://ondemand-api.cinesend.com/',
  CAST_URL: "https://bitmovin-cast-css.cinesend.com/production/styles.min.css",
  BM_ANALYTICS_ID: "7c4b2d6c-0c84-4fca-9c01-2b5e5b18dff6",
  MUX_ANALYTICS_ID: "9hkvuhoegfs8helc8ojgkta9b",
  GETSTREAM_KEY: "jfjd3unm5kzz",
  STRIPE_PUBLIC_KEY: "pk_live_Wl0SxWj9MQR0cUz2bLGMAeZV"
}

export default config