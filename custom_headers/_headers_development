/*
  Content-Security-Policy-Report-Only: report-uri https://o243844.ingest.us.sentry.io/api/4508399019425792/security/?sentry_key=52751741811ecd30cd6ea01bde099c11; default-src 'self'; script-src 'report-sample' 'self' https://cdn.bitmovin.com/analytics/web/beta/2/bitmovinanalytics.min.js https://js.stripe.com/v3 https://www.googletagmanager.com/gtag/js https://www.gstatic.com/cv/js/sender/v1/cast_sender.js; style-src 'report-sample' 'self' 'strict-dynamic' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; object-src 'none'; base-uri 'self'; connect-src 'self' https://analytics-ingress-global.bitmovin.com https://cinesend-dev.s3-accelerate.amazonaws.com https://d1bejs8r9quxzj.cloudfront.net https://dash.dev.cinesend.com https://dev-api.cinesend.com https://licensing.bitmovin.com https://www.google-analytics.com; font-src 'self' data: https://fonts.gstatic.com; frame-src 'self' https://js.stripe.com; manifest-src 'self'; media-src 'self'; worker-src blob:; 