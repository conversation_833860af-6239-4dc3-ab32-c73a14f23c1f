/*
  Content-Security-Policy-Report-Only: report-uri https://o243844.ingest.us.sentry.io/api/4508400245538816/security/?sentry_key=61df535c4074aca5c250222ac6ecc3be; default-src 'self'; script-src 'report-sample' 'self' https://cdn.bitmovin.com/player/web/8.75.0/bitmovinplayer.js https://js.stripe.com/v3 https://www.googletagmanager.com/gtag/js https://www.gstatic.com/cv/js/sender/v1/cast_sender.js sha256-wFJcnmnc0zunYYrslx+e06VogNEfY7oxz2Wwwj/8Eeo= sha256-JNygJUybJWRrjKgCbKHyDP2so4Kr7w7wD0KH/R8Mafk= sha256-oDYrgzCqIkGAagxRQnGOWBNT3UCvogdGM1B6xwHsw+c= sha256-3R6yzfJUBffQ3tq6zdgVi1/D6V9kBVMkBUnIW9/IqC8= sha256-0ZkTDd8T7tLZr56y8UlqTbWgu5F7TtqxeeiEZCfrLQE= sha256-H7gBTy+5sFt3a6hoIZFmOF7PbD4f/+JY7Z21p25NJGc= sha256-5KVBrP5KP6WgRmfiIwAYjlHP8Pb6UsygGACMLyTxKYs= sha256-be+CP8cH+b9EfSInId5DxYoHOUTmKKRIpTJlvZnAlRQ=; style-src 'report-sample' 'self' 'strict-dynamic' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; object-src 'none'; base-uri 'self'; connect-src 'self' https://analytics-ingress-global.bitmovin.com https://api.cinesend.com https://cinesendcontainer.s3-accelerate.amazonaws.com https://d1bejs8r9quxzj.cloudfront.net https://dash.cinesend.com https://dev-api.cinesend.com https://licensing.bitmovin.com https://widevine-dash.ezdrm.com https://www.google-analytics.com; font-src 'self' data: https://fonts.gstatic.com; frame-src 'self' https://js.stripe.com; manifest-src 'self'; media-src 'self'; worker-src blob:;
