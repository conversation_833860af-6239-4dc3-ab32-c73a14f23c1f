/*
  Content-Security-Policy-Report-Only: report-uri https://o243844.ingest.us.sentry.io/api/4508400270639104/security/?sentry_key=cc99b11875d5c25666eef59ed32463e8; default-src 'self'; script-src 'report-sample' 'self' https://cdn.bitmovin.com/player/web/8.75.0/bitmovinplayer.js https://js.stripe.com/v3 https://www.googletagmanager.com/gtag/js https://www.gstatic.com/cast/sdk/libs/sender/1.0/cast_framework.js; style-src 'report-sample' 'self' 'strict-dynamic' 'unsafe-inline' https://fonts.googleapis.com https://unpkg.com; object-src 'none'; base-uri 'self'; connect-src 'self' https://analytics-ingress-global.bitmovin.com https://d5peyl2dqcr35.cloudfront.net https://ingesteer.services-prod.nsvcs.net https://lic.staging.drmtoday.com https://licensing.bitmovin.com https://staging-api.cinesend.com https://www.google-analytics.com; font-src 'self' data: https://fonts.gstatic.com; frame-src 'self' https://js.stripe.com; manifest-src 'self'; media-src 'self'; worker-src blob:;   