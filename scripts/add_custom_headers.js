const fs = require('fs');
const path = require('path');

const env = process.env.NODE_ENV || 'development';
const customHeadersFile = path.join(__dirname, '..', 'custom_headers', `_headers_${env}`);
const headersFile = path.join(__dirname, '..', '_headers');

fs.copyFile(customHeadersFile, headersFile, (err) => {
  if (err) {
    console.error(`Error copying ${customHeadersFile} to _headers file:`, err);
    process.exit(1);
  }
  console.log(`Copied ${customHeadersFile} to _headers`);
});

