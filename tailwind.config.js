const bitcineTheme = require('@bitcine/cinesend-theme/tailwind.config')
const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  ...defaultTheme,
  ...bitcineTheme,
  darkMode: 'class',
  content: [
    './node_modules/@bitcine/**/*.{js,jsx,ts,tsx}',
    './src/**/*.{js,jsx,ts,tsx}'
  ],
  theme: {
    extend: {
      ...bitcineTheme.theme.extend,
      animation: {
        'spin-slow': 'spin 3s linear infinite',
      },
      spacing: {
        '8xl': '96rem',
        '9xl': '128rem',
        '10xl': '156rem'
      },
    }
  }
}
